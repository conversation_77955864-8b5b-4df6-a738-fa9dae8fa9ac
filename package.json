{"name": "spacedata-backend", "version": "1.0.0", "description": "太空大数据平台后端服务", "main": "dist/main.js", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "docker:build": "docker-compose -f docker/compose/dev.yml build", "docker:up": "docker-compose -f docker/compose/dev.yml up -d", "docker:down": "docker-compose -f docker/compose/dev.yml down", "migration:generate": "typeorm migration:generate -n", "migration:run": "typeorm migration:run", "migration:revert": "typeorm migration:revert"}, "keywords": ["space", "data", "platform", "microservices"], "author": "", "license": "MIT", "dependencies": {"@elastic/elasticsearch": "^8.17.0", "@nestjs/common": "^11.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^11.0.7", "@nestjs/elasticsearch": "^11.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.0.7", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.7", "@nestjs/schedule": "^6.0.0", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.0.3", "@nestjs/typeorm": "^11.0.0", "@types/axios": "^0.9.36", "@types/fast-levenshtein": "^0.0.4", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.34", "@vitalets/google-translate-api": "^9.2.1", "amqplib": "^0.10.0", "axios": "^1.8.3", "bcrypt": "^5.1.1", "better-sqlite3": "^11.8.1", "cheerio": "^1.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "consul": "^1.0.1", "elasticsearch": "^16.7.2", "fast-levenshtein": "^3.0.0", "fs": "^0.0.1-security", "helmet": "^5.1.0", "levenshtein": "^1.0.5", "mongodb": "^4.7.0", "nest-winston": "^1.10.2", "openai": "^4.93.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.7.3", "redis": "^4.1.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.5.5", "satellite.js": "^5.0.0", "typeorm": "^0.3.6", "winston": "^3.17.0"}, "devDependencies": {"@jest/types": "^29.6.3", "@nestjs/cli": "^11.0.2", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^11.0.7", "@types/bcrypt": "^5.0.2", "@types/cheerio": "^0.22.31", "@types/express": "^4.17.13", "@types/jest": "^28.1.8", "@types/lodash": "^4.17.15", "@types/node": "^18.0.3", "@types/supertest": "^2.0.12", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^5.30.5", "@typescript-eslint/parser": "^5.30.5", "eslint": "^8.19.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^28.1.3", "prettier": "^2.7.1", "source-map-support": "^0.5.21", "supertest": "^6.2.4", "ts-jest": "^28.0.8", "ts-loader": "^9.3.1", "ts-node": "^10.8.2", "tsconfig-paths": "^4.0.0", "typescript": "^4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.ts"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/src"], "moduleNameMapper": {"^@/(.*)$": "<rootDir>/src/$1"}}}