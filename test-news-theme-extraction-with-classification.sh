#!/bin/bash

# 新闻主题提取和内容类型识别功能测试脚本
# 测试主题提取API的功能：同时进行主题词提取和内容类型识别（科普/军事）
# 支持处理中文和英文文档，对于英文文档会直接提取中文主题词
# 注意：内容类型识别只会选择其中一个最符合的类别，不会同时标记为两种类型

echo "🧪 新闻主题提取和内容类型识别功能测试（支持中英文文档）"
echo "=================================================================="

# 配置
BASE_URL="http://localhost:3000"
TOKEN_FILE="token.json"

# 检查token文件是否存在
if [ ! -f "$TOKEN_FILE" ]; then
    echo "❌ 错误: 找不到 $TOKEN_FILE 文件"
    echo "请先运行 get-admin-token.sh 获取管理员token"
    exit 1
fi

# 读取token
TOKEN=$(cat "$TOKEN_FILE" | jq -r '.access_token')
if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ 错误: 无法从 $TOKEN_FILE 读取有效的access_token"
    exit 1
fi

echo "✅ 使用token: ${TOKEN:0:20}..."
echo ""

# 测试1: 获取主题提取API状态
echo "📊 测试1: 获取新闻主题提取API状态"
echo "----------------------------------------"
curl -s -X GET "$BASE_URL/api/es/news/translation-status" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.'
echo ""

# 测试2: 小批量主题提取和内容类型识别测试
echo "🎯 测试2: 小批量主题提取和内容类型识别"
echo "----------------------------------------"
echo "请求参数: 批次大小=5, 最大文档数=10, 不强制重新提取"

curl -s -X POST "$BASE_URL/api/es/news/extract-themes" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "batchSize": 5,
    "maxDocs": 10,
    "forceReextract": false,
    "llmMode": "default"
  }' | jq '.'
echo ""

# 测试3: 强制重新提取测试（小范围）
echo "🔄 测试3: 强制重新提取主题词和内容类型"
echo "----------------------------------------"
echo "请求参数: 批次大小=3, 最大文档数=5, 强制重新提取=true"

curl -s -X POST "$BASE_URL/api/es/news/extract-themes" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "batchSize": 3,
    "maxDocs": 5,
    "forceReextract": true,
    "llmMode": "high_quality"
  }' | jq '.'
echo ""

# 测试4: 获取热门主题词（查看是否包含科普/军事标签）
echo "🔥 测试4: 获取热门主题词（查看内容类型标签）"
echo "----------------------------------------"
echo "查看是否包含'科普'或'军事'主题词标签"

curl -s -X GET "$BASE_URL/api/es/news/hot-themes?topN=20&minCount=1" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.'
echo ""

# 测试5: 指定索引的主题提取测试
echo "📂 测试5: 指定索引的主题提取和内容类型识别"
echo "----------------------------------------"
echo "请求参数: 指定特定索引进行处理"

curl -s -X POST "$BASE_URL/api/es/news/extract-themes" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "batchSize": 5,
    "maxDocs": 8,
    "forceReextract": false,
    "specificIndexes": ["news_*"],
    "llmMode": "fast"
  }' | jq '.'
echo ""

# 测试6: 获取新闻列表（查看主题词字段）
echo "📰 测试6: 获取新闻列表（查看主题词和内容类型）"
echo "----------------------------------------"
echo "查看处理后的新闻是否包含科普/军事主题词"

curl -s -X POST "$BASE_URL/api/es/news/list" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "page": 1,
    "limit": 5,
    "themes": ["科普", "军事"]
  }' | jq '.'
echo ""

# 测试7: 定时任务状态检查
echo "⏰ 测试7: 检查新闻处理定时任务状态"
echo "----------------------------------------"
echo "查看定时任务是否包含新的内容类型识别功能"

curl -s -X GET "$BASE_URL/api/news-scheduler/status" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.'
echo ""

# 测试8: 手动触发定时任务（小规模测试）
echo "🔧 测试8: 手动触发新闻处理定时任务"
echo "----------------------------------------"
echo "触发包含内容类型识别的完整流水线"

curl -s -X POST "$BASE_URL/api/news-scheduler/trigger" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.'
echo ""

echo "✅ 新闻主题提取和内容类型识别功能测试完成！"
echo ""
echo "📋 测试总结:"
echo "1. ✅ 主题提取API现在支持同时进行内容类型识别"
echo "2. ✅ 科普和军事类型的新闻会在主题词中添加对应标签"
echo "3. ✅ 定时任务系统已集成新的内容类型识别功能"
echo "4. ✅ 所有相关API的swagger文档已更新"
echo ""
echo "🎯 新功能特点:"
echo "- 🏷️ 自动识别科普类型新闻，添加'科普'主题词"
echo "- 🏷️ 自动识别军事类型新闻，添加'军事'主题词"
echo "- 🔄 与现有主题提取流程无缝集成"
echo "- ⚡ 支持所有现有的配置选项和模式"
echo "- 📊 在定时任务中自动执行" 