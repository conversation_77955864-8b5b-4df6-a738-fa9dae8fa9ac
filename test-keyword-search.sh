#!/bin/bash

# 测试keyword字段的全字段搜索功能
# 现在keyword可以搜索所有字段，包括NORAD ID和COSPAR ID等数值字段

BASE_URL="http://localhost:3001/api/v1/database"

echo "=== 测试keyword字段的全字段搜索功能 ==="
echo

# 测试1: 使用NORAD ID作为keyword搜索
echo "测试1: 使用NORAD ID '25544' 作为keyword搜索"
curl -X POST "${BASE_URL}/filter-satellites" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "keyword": "25544",
    "page": 1,
    "limit": 5
  }' | jq '.'

echo
echo "---"
echo

# 测试2: 使用COSPAR ID作为keyword搜索
echo "测试2: 使用COSPAR ID '1998-067A' 作为keyword搜索"
curl -X POST "${BASE_URL}/filter-satellites" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "keyword": "1998-067A",
    "page": 1,
    "limit": 5
  }' | jq '.'

echo
echo "---"
echo

# 测试3: 使用状态作为keyword搜索
echo "测试3: 使用状态 'ACTIVE' 作为keyword搜索"
curl -X POST "${BASE_URL}/filter-satellites" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "keyword": "ACTIVE",
    "page": 1,
    "limit": 5
  }' | jq '.'

echo
echo "---"
echo

# 测试4: 使用数字片段作为keyword搜索
echo "测试4: 使用数字片段 '255' 作为keyword搜索（应该匹配包含255的NORAD ID）"
curl -X POST "${BASE_URL}/filter-satellites" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "keyword": "255",
    "page": 1,
    "limit": 5
  }' | jq '.'

echo
echo "---"
echo

# 测试5: 使用卫星名称作为keyword搜索
echo "测试5: 使用卫星名称 'Starlink' 作为keyword搜索"
curl -X POST "${BASE_URL}/filter-satellites" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "keyword": "Starlink",
    "page": 1,
    "limit": 5
  }' | jq '.'

echo
echo "=== 测试完成 ===" 