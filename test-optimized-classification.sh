#!/bin/bash

# 优化后的科普类别识别准确性测试脚本
# 测试优化后的提示词是否能更准确地区分科普内容和新闻事实

echo "🧪 优化后的科普类别识别准确性测试"
echo "=================================================================="

# 配置
BASE_URL="http://localhost:3000"
TOKEN_FILE="token.json"

# 检查token文件是否存在
if [ ! -f "$TOKEN_FILE" ]; then
    echo "❌ 错误: 找不到 $TOKEN_FILE 文件"
    echo "请先运行 get-admin-token.sh 获取管理员token"
    exit 1
fi

# 读取token
TOKEN=$(cat "$TOKEN_FILE" | jq -r '.access_token')
if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ 错误: 无法从 $TOKEN_FILE 读取有效的access_token"
    exit 1
fi

echo "✅ 使用token: ${TOKEN:0:20}..."
echo ""

# 测试1: 小批量翻译和主题提取测试（使用优化后的提示词）
echo "🎯 测试1: 小批量翻译和主题提取（优化后的类别识别）"
echo "----------------------------------------"
echo "请求参数: 批次大小=5, 最大文档数=10, 自动提取主题词=true"

curl -s -X POST "$BASE_URL/api/es/news/translate" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "batchSize": 5,
    "maxDocs": 10,
    "forceRetranslate": false,
    "autoExtractThemes": true,
    "llmMode": "default"
  }' | jq '.'
echo ""

# 测试2: 单独的主题提取测试（使用优化后的提示词）
echo "🔍 测试2: 单独主题提取和类别识别（优化后）"
echo "----------------------------------------"
echo "请求参数: 批次大小=5, 最大文档数=10, 不强制重新提取"

curl -s -X POST "$BASE_URL/api/es/news/extract-themes" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "batchSize": 5,
    "maxDocs": 10,
    "forceReextract": false,
    "llmMode": "default"
  }' | jq '.'
echo ""

# 测试3: 查看最近处理的文档的主题词分类情况
echo "📊 测试3: 查看最近处理文档的主题词分类情况"
echo "----------------------------------------"

# 获取最近的新闻索引
RECENT_INDEX=$(curl -s -X GET "$BASE_URL/api/es/news/indices" \
  -H "Authorization: Bearer $TOKEN" | jq -r '.data[0]' 2>/dev/null)

if [ "$RECENT_INDEX" != "null" ] && [ -n "$RECENT_INDEX" ]; then
    echo "查询索引: $RECENT_INDEX"
    
    # 查询包含主题词的文档
    curl -s -X POST "$BASE_URL/api/es/news/search" \
      -H "Authorization: Bearer $TOKEN" \
      -H "Content-Type: application/json" \
      -d "{
        \"index\": \"$RECENT_INDEX\",
        \"query\": {
          \"bool\": {
            \"must\": [
              {\"exists\": {\"field\": \"themes_cn\"}}
            ]
          }
        },
        \"size\": 10,
        \"_source\": [\"title_cn\", \"themes_cn\", \"summary_cn\"]
      }" | jq '.data.hits.hits[] | {title: ._source.title_cn, themes: ._source.themes_cn, summary: ._source.summary_cn}' 2>/dev/null
else
    echo "⚠️  无法获取最近的新闻索引"
fi

echo ""
echo "🎉 测试完成！"
echo ""
echo "📝 优化说明："
echo "1. 优化了科普类别的判断标准，强调教育目的和教学性质"
echo "2. 明确区分了新闻事实和科普内容"
echo "3. 只有明确以教育为目的、主要解释概念和原理的文章才会被标记为科普"
echo "4. 单纯的技术新闻报道不再被误识别为科普内容"
echo ""
echo "🔍 请检查上述结果中的主题词分类是否更加准确！" 