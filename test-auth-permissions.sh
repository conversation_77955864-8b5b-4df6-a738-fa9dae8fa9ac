#!/bin/bash

# 太空大数据平台 - 权限管理API测试脚本
# 测试用户权限设置和管理功能

BASE_URL="http://localhost:3001"
API_BASE="$BASE_URL/auth"

echo "=== 太空大数据平台权限管理API测试 ==="
echo "基础URL: $BASE_URL"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0

# 测试函数
test_api() {
    local test_name="$1"
    local method="$2"
    local url="$3"
    local data="$4"
    local expected_status="$5"
    local headers="$6"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${YELLOW}测试 $TOTAL_TESTS: $test_name${NC}"
    
    if [ -n "$headers" ]; then
        if [ -n "$data" ]; then
            response=$(curl -s -w "\n%{http_code}" -X "$method" "$url" \
                -H "Content-Type: application/json" \
                -H "$headers" \
                -d "$data")
        else
            response=$(curl -s -w "\n%{http_code}" -X "$method" "$url" \
                -H "$headers")
        fi
    else
        if [ -n "$data" ]; then
            response=$(curl -s -w "\n%{http_code}" -X "$method" "$url" \
                -H "Content-Type: application/json" \
                -d "$data")
        else
            response=$(curl -s -w "\n%{http_code}" -X "$method" "$url")
        fi
    fi
    
    status_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✓ 通过 (状态码: $status_code)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        if [ -n "$response_body" ] && [ "$response_body" != "null" ]; then
            echo "响应: $(echo "$response_body" | jq . 2>/dev/null || echo "$response_body")"
        fi
    else
        echo -e "${RED}✗ 失败 (期望: $expected_status, 实际: $status_code)${NC}"
        echo "响应: $response_body"
    fi
    echo
}

# 1. 测试用户注册
echo "=== 1. 用户注册测试 ==="
test_api "注册普通用户" "POST" "$API_BASE/register" \
    '{"username":"testuser","password":"password123","email":"<EMAIL>"}' \
    "201"

test_api "注册管理员用户" "POST" "$API_BASE/register" \
    '{"username":"admin","password":"admin123","email":"<EMAIL>"}' \
    "201"

# 2. 测试用户登录
echo "=== 2. 用户登录测试 ==="
login_response=$(curl -s -X POST "$API_BASE/login" \
    -H "Content-Type: application/json" \
    -d '{"username":"testuser","password":"password123"}')

if echo "$login_response" | jq -e '.access_token' > /dev/null 2>&1; then
    USER_TOKEN=$(echo "$login_response" | jq -r '.access_token')
    USER_ID=$(echo "$login_response" | jq -r '.user.id')
    echo -e "${GREEN}✓ 普通用户登录成功${NC}"
    echo "用户Token: ${USER_TOKEN:0:20}..."
    echo "用户ID: $USER_ID"
else
    echo -e "${RED}✗ 普通用户登录失败${NC}"
    echo "响应: $login_response"
fi
echo

admin_login_response=$(curl -s -X POST "$API_BASE/login" \
    -H "Content-Type: application/json" \
    -d '{"username":"admin","password":"admin123"}')

if echo "$admin_login_response" | jq -e '.access_token' > /dev/null 2>&1; then
    ADMIN_TOKEN=$(echo "$admin_login_response" | jq -r '.access_token')
    ADMIN_ID=$(echo "$admin_login_response" | jq -r '.user.id')
    echo -e "${GREEN}✓ 管理员用户登录成功${NC}"
    echo "管理员Token: ${ADMIN_TOKEN:0:20}..."
    echo "管理员ID: $ADMIN_ID"
else
    echo -e "${RED}✗ 管理员用户登录失败${NC}"
    echo "响应: $admin_login_response"
fi
echo

# 3. 测试获取当前用户信息
echo "=== 3. 获取当前用户信息测试 ==="
if [ -n "$USER_TOKEN" ]; then
    test_api "获取普通用户信息" "GET" "$API_BASE/profile" "" "200" \
        "Authorization: Bearer $USER_TOKEN"
fi

if [ -n "$ADMIN_TOKEN" ]; then
    test_api "获取管理员用户信息" "GET" "$API_BASE/profile" "" "200" \
        "Authorization: Bearer $ADMIN_TOKEN"
fi

# 4. 测试权限管理功能（需要先设置管理员权限）
echo "=== 4. 权限管理测试 ==="

# 首先需要手动设置管理员权限（在实际环境中，这应该通过数据库迁移或初始化脚本完成）
echo "注意: 需要手动在数据库中将admin用户的role设置为'admin'"
echo "SQL: UPDATE users SET role = 'admin' WHERE username = 'admin';"
echo

if [ -n "$ADMIN_TOKEN" ] && [ -n "$USER_ID" ]; then
    # 测试设置用户角色（管理员权限）
    test_api "管理员设置用户角色为premium" "PUT" "$API_BASE/users/$USER_ID/role" \
        '{"role":"premium"}' "200" \
        "Authorization: Bearer $ADMIN_TOKEN"
    
    # 测试获取用户权限信息
    test_api "管理员查看用户权限" "GET" "$API_BASE/users/$USER_ID/permissions" "" "200" \
        "Authorization: Bearer $ADMIN_TOKEN"
    
    # 测试获取用户列表
    test_api "管理员获取用户列表" "GET" "$API_BASE/users?page=1&limit=10" "" "200" \
        "Authorization: Bearer $ADMIN_TOKEN"
fi

if [ -n "$USER_TOKEN" ] && [ -n "$USER_ID" ]; then
    # 测试普通用户查看自己的权限
    test_api "普通用户查看自己权限" "GET" "$API_BASE/users/$USER_ID/permissions" "" "200" \
        "Authorization: Bearer $USER_TOKEN"
    
    # 测试普通用户尝试设置权限（应该失败）
    test_api "普通用户尝试设置权限（应该失败）" "PUT" "$API_BASE/users/$USER_ID/role" \
        '{"role":"admin"}' "403" \
        "Authorization: Bearer $USER_TOKEN"
    
    # 测试普通用户尝试获取用户列表（应该失败）
    test_api "普通用户尝试获取用户列表（应该失败）" "GET" "$API_BASE/users" "" "403" \
        "Authorization: Bearer $USER_TOKEN"
fi

# 5. 测试无权限访问
echo "=== 5. 无权限访问测试 ==="
test_api "无Token访问用户列表" "GET" "$API_BASE/users" "" "401"
test_api "无Token设置用户权限" "PUT" "$API_BASE/users/1/role" \
    '{"role":"admin"}' "401"

# 测试结果汇总
echo "=== 测试结果汇总 ==="
echo -e "总测试数: $TOTAL_TESTS"
echo -e "通过测试: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败测试: ${RED}$((TOTAL_TESTS - PASSED_TESTS))${NC}"

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
    echo -e "${GREEN}🎉 所有测试通过！${NC}"
    exit 0
else
    echo -e "${RED}❌ 部分测试失败${NC}"
    exit 1
fi 