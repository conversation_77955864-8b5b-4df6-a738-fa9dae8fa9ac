#!/bin/bash

echo "🚀 卫星点云文件推送功能快速测试"
echo "=================================="

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查npm依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
fi

echo ""
echo "🔍 检查后端服务状态..."

# 检查后端服务是否运行
if ! curl -s http://localhost:3000/api/tiles/status > /dev/null 2>&1; then
    echo "❌ 后端服务未运行"
    echo "请在另一个终端窗口启动后端服务："
    echo "   npm run start:dev"
    echo ""
    echo "等待服务启动后再运行此脚本"
    exit 1
fi

echo "✅ 后端服务正在运行"
echo ""

# 创建测试目录
echo "📁 创建测试目录..."
mkdir -p test-push-results

echo "🧪 开始测试文件推送功能..."
echo ""

# 测试1: 基础HTTP推送（需要启动测试服务器）
echo "📤 测试1: 基础HTTP推送"
echo "启动测试接收服务器..."

# 在后台启动测试接收服务器
node -e "
const express = require('express');
const fs = require('fs');
const path = require('path');

const app = express();

app.use('/api/tiles/upload', express.raw({ 
  type: 'application/octet-stream', 
  limit: '100mb' 
}));

app.use(express.json());

app.post('/api/tiles/upload', (req, res) => {
  const fileName = req.headers['x-file-name'];
  const fileSize = parseInt(req.headers['x-file-size']);
  
  console.log(\`📥 接收到文件: \${fileName}, 大小: \${(fileSize / 1024 / 1024).toFixed(2)}MB\`);
  
  const testDir = './test-push-results';
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
  }
  
  const filePath = path.join(testDir, fileName);
  fs.writeFileSync(filePath, req.body);
  
  console.log(\`✅ 文件保存成功: \${filePath}\`);
  res.json({ success: true, message: '文件接收成功' });
});

app.post('/api/tiles/callback', (req, res) => {
  console.log('📞 收到推送完成回调:', JSON.stringify(req.body, null, 2));
  res.json({ success: true });
});

const server = app.listen(3001, () => {
  console.log('🚀 测试接收服务器启动在端口 3001');
});

// 5分钟后自动关闭
setTimeout(() => {
  server.close();
  console.log('🛑 测试服务器已关闭');
  process.exit(0);
}, 300000);
" &

TEST_SERVER_PID=$!

# 等待测试服务器启动
sleep 2

echo "发送HTTP推送测试请求..."

# 发送测试请求
curl -X POST http://localhost:3000/api/tiles/generate \
  -H "Content-Type: application/json" \
  -d '{
    "colorByOrbitClass": true,
    "includeStatistics": true,
    "enableGzip": true,
    "pushTargets": [
      {
        "type": "http",
        "target": "http://localhost:3001/api/tiles/upload",
        "options": {
          "timeout": 60000,
          "retries": 2
        }
      }
    ],
    "callbackUrl": "http://localhost:3001/api/tiles/callback"
  }' \
  --max-time 300 \
  --silent \
  --show-error \
  | jq '.' 2>/dev/null || echo "响应数据过大，无法格式化显示"

echo ""
echo "⏳ 等待推送完成..."
sleep 5

# 测试2: 本地推送
echo ""
echo "📁 测试2: 本地推送"

curl -X POST http://localhost:3000/api/tiles/generate \
  -H "Content-Type: application/json" \
  -d '{
    "colorByOrbitClass": true,
    "includeStatistics": true,
    "enableGzip": false,
    "pushTargets": [
      {
        "type": "local",
        "target": "./test-push-results/local"
      }
    ]
  }' \
  --max-time 300 \
  --silent \
  --show-error \
  | jq '.success, .message, .pushResults[].success, .pushResults[].target' 2>/dev/null || echo "本地推送测试完成"

echo ""
echo "📊 检查测试结果..."

# 检查生成的文件
if [ -d "test-push-results" ]; then
    echo "✅ 测试结果目录存在"
    echo "📁 生成的文件:"
    find test-push-results -type f -exec ls -lh {} \; | while read line; do
        echo "   $line"
    done
else
    echo "❌ 测试结果目录不存在"
fi

# 检查public/tiles目录
if [ -d "public/tiles" ]; then
    echo ""
    echo "📁 public/tiles 目录文件:"
    find public/tiles -type f -exec ls -lh {} \; | while read line; do
        echo "   $line"
    done
fi

echo ""
echo "🧹 清理测试环境..."

# 关闭测试服务器
if kill -0 $TEST_SERVER_PID 2>/dev/null; then
    kill $TEST_SERVER_PID
    echo "🛑 测试服务器已关闭"
fi

# 询问是否清理测试文件
echo ""
read -p "是否清理测试文件? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm -rf test-push-results
    echo "🗑️  测试文件已清理"
else
    echo "📁 测试文件保留在 test-push-results 目录"
fi

echo ""
echo "🎉 文件推送功能测试完成!"
echo ""
echo "📖 更多使用方法请参考:"
echo "   docs/satellite-tiles-push-usage.md"
echo ""
echo "🔧 完整测试脚本:"
echo "   node test-push-functionality.js" 