#!/bin/bash

# 卫星3D Tiles点云系统启动和测试脚本

echo "🚀 启动卫星3D Tiles点云系统..."

# 检查Node.js版本
echo "📋 检查环境..."
node --version
npm --version

# 安装依赖（如果需要）
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install --legacy-peer-deps
fi

# 编译项目
echo "🔨 编译项目..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 编译失败，请检查代码"
    exit 1
fi

# 启动服务（后台运行）
echo "🌟 启动服务..."
npm run start:prod &
SERVER_PID=$!

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务是否启动成功
if ps -p $SERVER_PID > /dev/null; then
    echo "✅ 服务启动成功 (PID: $SERVER_PID)"
    echo "🌐 API文档地址: http://localhost:3001/api-docs"
    echo "📊 示例页面: http://localhost:3001/public/tiles/example.html"
    echo ""
    
    # 基本健康检查
    echo "🔍 执行健康检查..."
    
    # 检查API文档是否可访问
    if curl -s http://localhost:3001/api-docs > /dev/null; then
        echo "✅ API文档可访问"
    else
        echo "⚠️  API文档不可访问"
    fi
    
    # 检查静态文件服务
    if curl -s http://localhost:3001/public/tiles/example.html > /dev/null; then
        echo "✅ 静态文件服务正常"
    else
        echo "⚠️  静态文件服务异常"
    fi
    
    echo ""
    echo "🎯 下一步操作："
    echo "1. 访问 http://localhost:3001/api-docs 查看API文档"
    echo "2. 使用JWT token调用 POST /tiles/generate 生成点云数据"
    echo "3. 访问 http://localhost:3001/public/tiles/example.html 查看示例"
    echo "4. 使用 GET /tiles/satellites 获取点云数据"
    echo ""
    echo "📝 测试脚本："
    echo "   node test-satellite-tiles.js"
    echo ""
    echo "🛑 停止服务："
    echo "   kill $SERVER_PID"
    
else
    echo "❌ 服务启动失败"
    exit 1
fi 