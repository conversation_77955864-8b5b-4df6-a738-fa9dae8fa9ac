#!/bin/bash

# 内容爬取标记机制优化测试脚本
# 测试新增的爬取标记功能和强制重新爬取选项

echo "🧪 内容爬取标记机制优化测试"
echo "==================================================================="

# 配置
BASE_URL="http://localhost:3000"
TOKEN_FILE="token.json"

# 检查token文件是否存在
if [ ! -f "$TOKEN_FILE" ]; then
    echo "❌ 错误: 找不到 $TOKEN_FILE 文件"
    echo "请先运行 get-admin-token.sh 获取管理员token"
    exit 1
fi

# 读取token
TOKEN=$(cat "$TOKEN_FILE" | jq -r '.access_token')
if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ 错误: 无法从 $TOKEN_FILE 读取有效的access_token"
    exit 1
fi

echo "✅ 使用token: ${TOKEN:0:20}..."
echo ""

# 测试1: 正常翻译（不强制重新爬取）
echo "🎯 测试1: 正常翻译（不强制重新爬取内容）"
echo "----------------------------------------"
echo "请求参数: 批次大小=5, 最大文档数=10, 强制重新爬取=false"

curl -s -X POST "$BASE_URL/api/es/news/translate" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "batchSize": 5,
    "maxDocs": 10,
    "forceRetranslate": false,
    "forceRefetchContent": false,
    "autoExtractThemes": true
  }' | jq '.'

echo ""
echo "⏱️  等待5秒后进行下一个测试..."
sleep 5

# 测试2: 强制重新爬取内容
echo "🎯 测试2: 强制重新爬取内容"
echo "----------------------------------------"
echo "请求参数: 批次大小=3, 最大文档数=5, 强制重新爬取=true"

curl -s -X POST "$BASE_URL/api/es/news/translate" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "batchSize": 3,
    "maxDocs": 5,
    "forceRetranslate": false,
    "forceRefetchContent": true,
    "autoExtractThemes": true
  }' | jq '.'

echo ""
echo "⏱️  等待5秒后进行下一个测试..."
sleep 5

# 测试3: 查询ES中的爬取标记字段
echo "🎯 测试3: 查询ES中的爬取标记字段"
echo "----------------------------------------"
echo "查询包含爬取标记的新闻文档..."

curl -s -X GET "$BASE_URL/api/es/news/search" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "page": 1,
    "size": 5,
    "keywords": ""
  }' | jq '.data[] | {
    _id: ._id,
    title: .title,
    info_source: .info_source,
    content_fetched: .content_fetched,
    content_fetch_time: .content_fetch_time,
    content_fetch_success: .content_fetch_success,
    has_content: (.content != null and .content != "")
  }'

echo ""
echo "🎯 测试4: 验证爬取标记机制"
echo "----------------------------------------"
echo "检查具有info_source但没有content的文档的爬取标记..."

# 使用ES直接查询API（如果有的话）
echo "正在查询具有info_source但content为空的文档..."

curl -s -X POST "$BASE_URL/api/es/_search" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "index": "news_*",
    "body": {
      "query": {
        "bool": {
          "must": [
            {"exists": {"field": "info_source"}},
            {"bool": {
              "should": [
                {"bool": {"must_not": {"exists": {"field": "content"}}}},
                {"term": {"content.keyword": ""}}
              ]
            }}
          ]
        }
      },
      "size": 3,
      "_source": ["title", "info_source", "content_fetched", "content_fetch_time", "content_fetch_success", "content"]
    }
  }' 2>/dev/null | jq '.hits.hits[] | {
    _id: ._id,
    _index: ._index,
    title: ._source.title,
    info_source: ._source.info_source,
    content_fetched: ._source.content_fetched,
    content_fetch_time: ._source.content_fetch_time,
    content_fetch_success: ._source.content_fetch_success,
    has_content: (._source.content != null and ._source.content != "")
  }' || echo "ES直接查询API不可用，跳过此测试"

echo ""
echo "📊 测试总结"
echo "----------------------------------------"
echo "✅ 测试1: 验证了正常翻译流程中的爬取标记机制"
echo "✅ 测试2: 验证了强制重新爬取内容的功能"
echo "✅ 测试3: 查看了新闻文档中的爬取标记字段"
echo "✅ 测试4: 检查了爬取标记的实际应用效果"
echo ""
echo "🎉 内容爬取标记机制优化测试完成！"
echo ""
echo "📝 新功能说明："
echo "1. content_fetched: 标记是否已尝试爬取内容"
echo "2. content_fetch_time: 记录爬取时间"
echo "3. content_fetch_success: 标记爬取是否成功"
echo "4. forceRefetchContent: 强制重新爬取内容的选项"
echo ""
echo "🔄 优化效果："
echo "- 避免重复爬取已处理的新闻"
echo "- 支持强制重新爬取失败的内容"
echo "- 详细记录爬取状态和时间"
echo "- 提高翻译任务的执行效率" 