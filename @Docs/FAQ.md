# 常见问题解答 (FAQ)

## JWT认证相关问题

### Q1: 在Swagger中使用JWT token时收到401未认证错误
**问题描述**：已经在Swagger中输入了有效的JWT token，但访问需要认证的API时仍然收到401错误。

**可能原因**：
1. Token格式不正确
2. Token已过期
3. 用户角色权限不足
4. Swagger认证配置问题

**解决方案**：
1. **检查Token格式**：
   - 确保在Swagger中输入的token包含"Bearer "前缀
   - 正确格式：`Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

2. **验证Token有效性**：
   ```bash
   # 使用curl测试token是否有效
   curl -X GET http://localhost:3001/auth/profile \
     -H "Authorization: Bearer your_token_here"
   ```

3. **检查用户角色**：
   - 管理员API需要admin角色
   - 使用以下命令检查用户角色：
   ```bash
   curl -X GET http://localhost:3001/auth/profile \
     -H "Authorization: Bearer your_token_here"
   ```

4. **重新获取Token**：
   ```bash
   curl -X POST http://localhost:3001/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username":"your_username","password":"your_password"}'
   ```

### Q2: Admin用户无法访问管理员API
**问题描述**：admin用户登录成功，但无法访问需要管理员权限的API。

**解决方案**：
1. **检查用户角色**：
   ```bash
   # 获取当前用户信息，查看role字段
   curl -X GET http://localhost:3001/auth/profile \
     -H "Authorization: Bearer your_token_here"
   ```

2. **设置admin角色**：
   如果role不是"admin"，使用以下方法设置：
   
   **方法1 - 使用脚本**：
   ```bash
   npx ts-node src/scripts/create-admin-user.ts
   ```
   
   **方法2 - 直接修改数据库**：
   ```sql
   UPDATE users SET role = 'admin' WHERE username = 'admin';
   ```

3. **重新登录获取新Token**：
   ```bash
   curl -X POST http://localhost:3001/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username":"admin","password":"admin123"}'
   ```

### Q3: 如何创建第一个admin用户？
**问题描述**：系统中没有admin用户，无法管理其他用户权限。

**解决方案**：
1. **注册admin用户**：
   ```bash
   curl -X POST http://localhost:3001/auth/register \
     -H "Content-Type: application/json" \
     -d '{"username":"admin","password":"admin123","email":"<EMAIL>"}'
   ```

2. **设置admin角色**：
   ```bash
   npx ts-node src/scripts/create-admin-user.ts
   ```

3. **验证设置**：
   ```bash
   # 登录获取token
   curl -X POST http://localhost:3001/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username":"admin","password":"admin123"}'
   
   # 测试admin权限
   curl -X GET http://localhost:3001/auth/users \
     -H "Authorization: Bearer your_token_here"
   ```

## API使用相关问题

### Q4: API调用次数限制
**问题描述**：收到API调用次数超限的错误。

**解决方案**：
1. **检查当前限制**：
   ```bash
   curl -X GET http://localhost:3001/auth/profile \
     -H "Authorization: Bearer your_token_here"
   ```
   查看`apiCallsToday`字段

2. **升级用户角色**：
   - free用户：每日100次
   - premium用户：每日1000次
   - enterprise/government用户：无限制

3. **联系管理员升级角色**

### Q5: 如何在代码中使用JWT认证？
**问题描述**：在前端或其他客户端中如何正确使用JWT token。

**解决方案**：
1. **JavaScript/TypeScript示例**：
   ```javascript
   // 登录获取token
   const loginResponse = await fetch('http://localhost:3001/auth/login', {
     method: 'POST',
     headers: {
       'Content-Type': 'application/json',
     },
     body: JSON.stringify({
       username: 'your_username',
       password: 'your_password'
     })
   });
   
   const { access_token } = await loginResponse.json();
   
   // 使用token调用API
   const apiResponse = await fetch('http://localhost:3001/auth/profile', {
     headers: {
       'Authorization': `Bearer ${access_token}`
     }
   });
   ```

2. **Python示例**：
   ```python
   import requests
   
   # 登录获取token
   login_response = requests.post('http://localhost:3001/auth/login', json={
       'username': 'your_username',
       'password': 'your_password'
   })
   
   access_token = login_response.json()['access_token']
   
   # 使用token调用API
   headers = {'Authorization': f'Bearer {access_token}'}
   api_response = requests.get('http://localhost:3001/auth/profile', headers=headers)
   ```

## 数据库相关问题

### Q6: 数据库连接失败
**问题描述**：应用启动时数据库连接失败。

**解决方案**：
1. **检查数据库配置**：
   - 确认PostgreSQL服务正在运行
   - 检查`config/env/development.env`中的数据库配置

2. **检查数据库权限**：
   - 确认数据库用户有足够权限
   - 确认数据库存在

3. **重启数据库服务**：
   ```bash
   # macOS
   brew services restart postgresql
   
   # Linux
   sudo systemctl restart postgresql
   ```

### Q7: 如何重置数据库？
**问题描述**：需要清空数据库重新开始。

**解决方案**：
1. **删除并重新创建数据库**：
   ```sql
   DROP DATABASE your_database_name;
   CREATE DATABASE your_database_name;
   ```

2. **运行迁移**：
   ```bash
   npm run migration:run
   ```

3. **创建admin用户**：
   ```bash
   npx ts-node src/scripts/create-admin-user.ts
   ```

## 开发环境问题

### Q8: 应用启动失败
**问题描述**：运行`npm run start:dev`时应用启动失败。

**解决方案**：
1. **检查依赖**：
   ```bash
   npm install
   ```

2. **检查环境配置**：
   - 确认`.env`文件存在且配置正确
   - 检查数据库连接配置

3. **查看详细错误信息**：
   ```bash
   npm run start:dev -- --verbose
   ```

4. **清理并重新构建**：
   ```bash
   npm run build
   rm -rf dist/
   npm run start:dev
   ```

### Q9: 端口被占用
**问题描述**：3001端口被其他进程占用。

**解决方案**：
1. **查找占用端口的进程**：
   ```bash
   # macOS/Linux
   lsof -i :3001
   
   # Windows
   netstat -ano | findstr :3001
   ```

2. **终止进程**：
   ```bash
   # macOS/Linux
   kill -9 <PID>
   
   # Windows
   taskkill /PID <PID> /F
   ```

3. **或者更改端口**：
   在`main.ts`中修改端口号：
   ```typescript
   await app.listen(3002); // 使用其他端口
   ```

## 联系支持

如果以上解决方案无法解决您的问题，请：

1. 查看应用日志：`logs/error.log` 和 `logs/combined.log`
2. 提供详细的错误信息和重现步骤
3. 联系开发团队获取支持

---

**最后更新时间**：2025-05-29 