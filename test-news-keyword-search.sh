#!/bin/bash

# 测试新闻列表API的关键词子串匹配功能
# 关键词搜索现在默认使用子串匹配，会在新闻的所有字段中进行匹配

BASE_URL="http://localhost:3001/api/es/news"

echo "=== 测试新闻列表API的关键词子串匹配功能 ==="
echo

# 测试1: 基本关键词搜索（默认子串匹配）
echo "测试1: 基本关键词搜索 - 搜索 'satellite'"
curl -X POST "${BASE_URL}/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "keywords": ["satellite"],
    "page": 1,
    "limit": 5
  }' | jq '.'

echo
echo "---"
echo

# 测试2: 多关键词搜索
echo "测试2: 多关键词搜索 - 搜索 'satellite' 和 'launch'"
curl -X POST "${BASE_URL}/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "keywords": ["satellite", "launch"],
    "page": 1,
    "limit": 5
  }' | jq '.'

echo
echo "---"
echo

# 测试3: 中文关键词搜索
echo "测试3: 中文关键词搜索 - 搜索 '卫星'"
curl -X POST "${BASE_URL}/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "keywords": ["卫星"],
    "page": 1,
    "limit": 5
  }' | jq '.'

echo
echo "---"
echo

# 测试4: 搜索来源字段
echo "测试4: 搜索来源字段 - 搜索 'SpaceX'"
curl -X POST "${BASE_URL}/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "keywords": ["SpaceX"],
    "page": 1,
    "limit": 5
  }' | jq '.'

echo
echo "---"
echo

# 测试5: 搜索作者字段
echo "测试5: 搜索作者字段 - 搜索作者名"
curl -X POST "${BASE_URL}/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "keywords": ["admin"],
    "page": 1,
    "limit": 5
  }' | jq '.'

echo
echo "---"
echo

# 测试6: 显式指定子串匹配
echo "测试6: 显式指定子串匹配 - 搜索 'space'"
curl -X POST "${BASE_URL}/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "keywords": ["space"],
    "keywordMatchType": "substring",
    "page": 1,
    "limit": 5
  }' | jq '.'

echo
echo "---"
echo

# 测试7: 相似性匹配对比
echo "测试7: 相似性匹配对比 - 搜索 'space'"
curl -X POST "${BASE_URL}/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "keywords": ["space"],
    "keywordMatchType": "similarity",
    "page": 1,
    "limit": 5
  }' | jq '.'

echo
echo "---"
echo

# 测试8: 结合主题词搜索
echo "测试8: 结合主题词搜索 - 关键词 'satellite' + 主题词 '航天'"
curl -X POST "${BASE_URL}/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "keywords": ["satellite"],
    "themes": ["航天"],
    "page": 1,
    "limit": 5
  }' | jq '.'

echo
echo "---"
echo

# 测试9: 结合时间范围搜索
echo "测试9: 结合时间范围搜索 - 关键词 'satellite' + 2023年数据"
curl -X POST "${BASE_URL}/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "keywords": ["satellite"],
    "publishDateStart": "2023-01-01",
    "publishDateEnd": "2023-12-31",
    "page": 1,
    "limit": 5
  }' | jq '.'

echo
echo "测试完成！"
echo
echo "说明："
echo "1. 关键词搜索现在默认使用子串匹配方式"
echo "2. 搜索会在新闻的所有字段中进行匹配，包括："
echo "   - 标题 (title, title_cn)"
echo "   - 摘要 (summary, summary_cn)"
echo "   - 内容 (content, content_cn)"
echo "   - 来源 (source)"
echo "   - 作者 (author)"
echo "   - 信息源链接 (info_source)"
echo "   - URL (url)"
echo "   - 主题词 (themes_cn)"
echo "   - 发布时间 (published_at)"
echo "3. 支持中英文关键词搜索"
echo "4. 可以与其他筛选条件组合使用" 