{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-06-15T09:38:44.825Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:38:44.842Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:38:44.842Z"}
{"context":"TranslationService","level":"info","message":"翻译服务初始化完成 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-06-15T09:38:44.843Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:38:44.843Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-06-15T09:38:44.843Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:38:44.845Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-06-15T09:38:44.847Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-06-15T09:38:44.847Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:38:44.849Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:38:44.849Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:38:44.849Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:38:44.849Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-06-15T09:38:44.853Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-06-15T09:38:44.854Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-06-15T09:38:44.861Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-06-15T09:38:44.862Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-06-15T09:38:44.863Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-06-15T09:38:44.864Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-06-15T09:38:44.864Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-06-15T09:38:44.864Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-06-15T09:38:44.901Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:38:44.901Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:38:44.901Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:38:44.901Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:38:44.901Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-06-15T09:38:44.902Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-06-15T09:38:44.902Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-06-15T09:38:44.902Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-06-15T09:38:44.902Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-06-15T09:38:44.903Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-06-15T09:38:44.903Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-06-15T09:38:44.993Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-06-15T09:38:44.994Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-06-15T09:38:44.995Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-06-15T09:38:44.995Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-06-15T09:38:44.995Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-06-15T09:38:44.995Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-06-15T09:38:44.995Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-06-15T09:38:44.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-06-15T09:38:44.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-06-15T09:38:44.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-06-15T09:38:44.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-06-15T09:38:44.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-06-15T09:38:44.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-06-15T09:38:44.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-06-15T09:38:44.996Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-06-15T09:38:44.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-06-15T09:38:44.996Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-06-15T09:38:44.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-06-15T09:38:44.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-06-15T09:38:44.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-06-15T09:38:44.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-06-15T09:38:44.997Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-06-15T09:38:44.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-06-15T09:38:44.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-06-15T09:38:44.997Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-06-15T09:38:44.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-06-15T09:38:44.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-06-15T09:38:44.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-06-15T09:38:44.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-06-15T09:38:44.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-06-15T09:38:44.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-06-15T09:38:44.998Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-06-15T09:38:44.998Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-06-15T09:38:44.998Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-06-15T09:38:44.998Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-06-15T09:38:44.998Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-06-15T09:38:44.998Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-06-15T09:38:44.998Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-06-15T09:38:44.998Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-06-15T09:38:44.998Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-06-15T09:38:44.998Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-06-15T09:38:44.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-06-15T09:38:44.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-06-15T09:38:44.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-06-15T09:38:44.999Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-06-15T09:38:44.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-06-15T09:38:44.999Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-06-15T09:38:44.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-06-15T09:38:44.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-06-15T09:38:44.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-06-15T09:38:44.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-06-15T09:38:44.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-06-15T09:38:44.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-06-15T09:38:44.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-06-15T09:38:45.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-06-15T09:38:45.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-06-15T09:38:45.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-06-15T09:38:45.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-06-15T09:38:45.000Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-06-15T09:38:45.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-06-15T09:38:45.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-06-15T09:38:45.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-06-15T09:38:45.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-06-15T09:38:45.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-06-15T09:38:45.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-06-15T09:38:45.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-06-15T09:38:45.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-06-15T09:38:45.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-06-15T09:38:45.001Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-06-15T09:38:45.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-06-15T09:38:45.001Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-06-15T09:38:45.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-06-15T09:38:45.001Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-06-15T09:38:45.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-06-15T09:38:45.001Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-06-15T09:38:45.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-06-15T09:38:45.001Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-06-15T09:38:45.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-06-15T09:38:45.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-06-15T09:38:45.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-06-15T09:38:45.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-06-15T09:38:45.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-06-15T09:38:45.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-06-15T09:38:45.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-06-15T09:38:45.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-06-15T09:38:45.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-06-15T09:38:45.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-06-15T09:38:45.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-06-15T09:38:45.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-06-15T09:38:45.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-06-15T09:38:45.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-06-15T09:38:45.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-06-15T09:38:45.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-06-15T09:38:45.003Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-06-15T09:38:45.003Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-06-15T09:38:45.003Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-06-15T09:38:45.003Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-06-15T09:38:45.003Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-06-15T09:38:45.003Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-06-15T09:38:45.004Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-06-15T09:38:45.004Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-06-15T09:38:45.005Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-06-15T09:38:45.005Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-06-15T09:38:45.005Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-06-15T09:38:45.005Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-06-15T09:38:45.010Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-06-15T09:38:45.010Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-06-15T09:38:45.010Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-06-15T09:38:45.010Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-06-15T09:38:45.011Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-06-15T09:38:45.012Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-06-15T09:38:45.013Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-06-15T09:38:45.013Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-06-15T09:38:45.013Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-06-15T09:38:45.013Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-06-15T09:38:45.013Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-06-15T09:38:45.013Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-06-15T09:38:45.014Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-06-15T09:38:45.014Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-06-15T09:38:45.015Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-06-15T09:38:45.015Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-06-15T09:38:45.016Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-06-15T09:38:45.016Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-06-15T09:38:45.016Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-06-15T09:38:45.016Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-06-15T09:38:45.016Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-06-15T09:38:45.016Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-06-15T09:38:45.016Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-06-15T09:38:45.033Z"}
{"context":"DatabaseService","level":"info","message":"pg_trgm扩展已启用","timestamp":"2025-06-15T09:38:45.043Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-06-15T09:38:45.086Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:38:45.093Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:38:45.093Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:38:45.093Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:38:45.094Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:38:45.094Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:38:45.094Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:38:45.094Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:38:45.095Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:38:45.583Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:38:45.585Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:38:51.917Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:38:51.918Z"}
{"context":"ElasticsearchNewsController","level":"info","message":"收到热门主题词请求: {\"topN\":20,\"minCount\":2}","timestamp":"2025-06-15T09:39:16.818Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:16.849Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"开始统计12个新闻索引的热门主题词","timestamp":"2025-06-15T09:39:16.850Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:16.852Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:16.853Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:16.854Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"热门主题词统计完成，共处理2829条文档，找到1352个主题词，返回前20个","timestamp":"2025-06-15T09:39:18.515Z"}
{"context":"ElasticsearchNewsController","level":"info","message":"收到热门主题词请求: {\"topN\":20,\"minCount\":2}","timestamp":"2025-06-15T09:39:18.526Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:18.547Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"开始统计12个新闻索引的热门主题词","timestamp":"2025-06-15T09:39:18.547Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"热门主题词统计完成，共处理2829条文档，找到1352个主题词，返回前20个","timestamp":"2025-06-15T09:39:19.981Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:22.010Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:22.011Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:23.962Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:23.962Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:24.404Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:24.405Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:36.773Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:36.776Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:36.990Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-06-15T09:39:43.712Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:39:43.746Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:39:43.747Z"}
{"context":"TranslationService","level":"info","message":"翻译服务初始化完成 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-06-15T09:39:43.748Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:39:43.749Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-06-15T09:39:43.749Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:39:43.749Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-06-15T09:39:43.749Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-06-15T09:39:43.750Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:39:43.751Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:39:43.752Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:39:43.754Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:39:43.754Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-06-15T09:39:43.762Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-06-15T09:39:43.763Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-06-15T09:39:43.769Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-06-15T09:39:43.770Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-06-15T09:39:43.771Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-06-15T09:39:43.772Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-06-15T09:39:43.772Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-06-15T09:39:43.772Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:39:43.813Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:39:43.813Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:39:43.813Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:39:43.813Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:39:43.813Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:39:43.813Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:39:43.813Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:39:43.814Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-06-15T09:39:43.831Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:39:43.831Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:39:43.832Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:39:43.832Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:39:43.832Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-06-15T09:39:43.832Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-06-15T09:39:43.833Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-06-15T09:39:43.833Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-06-15T09:39:43.833Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-06-15T09:39:43.834Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-06-15T09:39:43.834Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-06-15T09:39:43.912Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-06-15T09:39:43.913Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-06-15T09:39:43.913Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-06-15T09:39:43.913Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-06-15T09:39:43.913Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-06-15T09:39:43.913Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-06-15T09:39:43.914Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-06-15T09:39:43.914Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-06-15T09:39:43.914Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-06-15T09:39:43.914Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-06-15T09:39:43.914Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-06-15T09:39:43.914Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-06-15T09:39:43.914Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-06-15T09:39:43.915Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-06-15T09:39:43.915Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-06-15T09:39:43.915Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-06-15T09:39:43.916Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-06-15T09:39:43.916Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-06-15T09:39:43.916Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-06-15T09:39:43.916Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-06-15T09:39:43.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-06-15T09:39:43.917Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-06-15T09:39:43.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-06-15T09:39:43.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-06-15T09:39:43.918Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-06-15T09:39:43.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-06-15T09:39:43.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-06-15T09:39:43.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-06-15T09:39:43.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-06-15T09:39:43.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-06-15T09:39:43.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-06-15T09:39:43.920Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-06-15T09:39:43.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-06-15T09:39:43.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-06-15T09:39:43.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-06-15T09:39:43.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-06-15T09:39:43.920Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-06-15T09:39:43.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-06-15T09:39:43.921Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-06-15T09:39:43.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-06-15T09:39:43.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-06-15T09:39:43.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-06-15T09:39:43.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-06-15T09:39:43.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-06-15T09:39:43.921Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-06-15T09:39:43.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-06-15T09:39:43.922Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-06-15T09:39:43.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-06-15T09:39:43.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-06-15T09:39:43.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-06-15T09:39:43.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-06-15T09:39:43.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-06-15T09:39:43.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-06-15T09:39:43.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-06-15T09:39:43.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-06-15T09:39:43.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-06-15T09:39:43.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-06-15T09:39:43.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-06-15T09:39:43.923Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-06-15T09:39:43.924Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-06-15T09:39:43.925Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-06-15T09:39:43.925Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-06-15T09:39:43.925Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-06-15T09:39:43.925Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-06-15T09:39:43.926Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-06-15T09:39:43.926Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-06-15T09:39:43.926Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-06-15T09:39:43.926Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-06-15T09:39:43.926Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-06-15T09:39:43.926Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-06-15T09:39:43.927Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-06-15T09:39:43.927Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-06-15T09:39:43.927Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-06-15T09:39:43.927Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-06-15T09:39:43.927Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-06-15T09:39:43.927Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-06-15T09:39:43.927Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-06-15T09:39:43.928Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-06-15T09:39:43.928Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-06-15T09:39:43.928Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-06-15T09:39:43.928Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-06-15T09:39:43.928Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-06-15T09:39:43.929Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-06-15T09:39:43.929Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-06-15T09:39:43.929Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-06-15T09:39:43.929Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-06-15T09:39:43.929Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-06-15T09:39:43.929Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-06-15T09:39:43.929Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-06-15T09:39:43.929Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-06-15T09:39:43.930Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-06-15T09:39:43.930Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-06-15T09:39:43.930Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-06-15T09:39:43.930Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-06-15T09:39:43.931Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-06-15T09:39:43.931Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-06-15T09:39:43.931Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-06-15T09:39:43.931Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-06-15T09:39:43.931Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-06-15T09:39:43.931Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-06-15T09:39:43.931Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-06-15T09:39:43.932Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-06-15T09:39:43.932Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-06-15T09:39:43.933Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-06-15T09:39:43.933Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-06-15T09:39:43.933Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-06-15T09:39:43.933Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-06-15T09:39:43.937Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-06-15T09:39:43.938Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-06-15T09:39:43.938Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-06-15T09:39:43.938Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-06-15T09:39:43.938Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-06-15T09:39:43.938Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-06-15T09:39:43.938Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-06-15T09:39:43.939Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-06-15T09:39:43.939Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-06-15T09:39:43.939Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-06-15T09:39:43.939Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-06-15T09:39:43.939Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-06-15T09:39:43.939Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-06-15T09:39:43.939Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-06-15T09:39:43.939Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-06-15T09:39:43.939Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-06-15T09:39:43.939Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-06-15T09:39:43.939Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-06-15T09:39:43.939Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-06-15T09:39:43.940Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-06-15T09:39:43.940Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-06-15T09:39:43.944Z"}
{"context":"DatabaseService","level":"info","message":"pg_trgm扩展已启用","timestamp":"2025-06-15T09:39:43.947Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-06-15T09:39:43.955Z"}
{"context":"ElasticsearchNewsController","level":"info","message":"收到热门主题词请求: {\"topN\":20,\"minCount\":2}","timestamp":"2025-06-15T09:39:47.503Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:47.507Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:47.524Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:47.524Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"开始统计12个新闻索引的热门主题词","timestamp":"2025-06-15T09:39:47.524Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:47.525Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"热门主题词统计完成，共处理2829条文档，找到1352个主题词，返回前20个","timestamp":"2025-06-15T09:39:49.096Z"}
{"context":"ElasticsearchNewsController","level":"info","message":"收到热门主题词请求: {\"topN\":20,\"minCount\":2}","timestamp":"2025-06-15T09:39:49.102Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:49.116Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"开始统计12个新闻索引的热门主题词","timestamp":"2025-06-15T09:39:49.117Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"热门主题词统计完成，共处理2829条文档，找到1352个主题词，返回前20个","timestamp":"2025-06-15T09:39:50.607Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:53.483Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:53.484Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:53.881Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:39:53.881Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-06-15T09:39:56.575Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:39:56.597Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:39:56.598Z"}
{"context":"TranslationService","level":"info","message":"翻译服务初始化完成 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-06-15T09:39:56.598Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:39:56.599Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-06-15T09:39:56.600Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:39:56.600Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-06-15T09:39:56.600Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-06-15T09:39:56.600Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:39:56.601Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:39:56.601Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:39:56.601Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:39:56.601Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-06-15T09:39:56.603Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-06-15T09:39:56.604Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-06-15T09:39:56.606Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-06-15T09:39:56.607Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-06-15T09:39:56.608Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-06-15T09:39:56.609Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-06-15T09:39:56.610Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-06-15T09:39:56.610Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-06-15T09:39:56.624Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-06-15T09:39:58.111Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:39:58.127Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:39:58.127Z"}
{"context":"TranslationService","level":"info","message":"翻译服务初始化完成 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-06-15T09:39:58.128Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:39:58.129Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-06-15T09:39:58.129Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:39:58.129Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-06-15T09:39:58.129Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-06-15T09:39:58.129Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:39:58.130Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:39:58.130Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:39:58.130Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:39:58.130Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-06-15T09:39:58.132Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-06-15T09:39:58.132Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-06-15T09:39:58.134Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-06-15T09:39:58.135Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-06-15T09:39:58.136Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-06-15T09:39:58.136Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-06-15T09:39:58.136Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-06-15T09:39:58.136Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-06-15T09:39:58.150Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:39:58.150Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:39:58.150Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:39:58.150Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:39:58.150Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-06-15T09:39:58.151Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-06-15T09:39:58.151Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-06-15T09:39:58.151Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-06-15T09:39:58.151Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-06-15T09:39:58.151Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-06-15T09:39:58.151Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-06-15T09:39:58.206Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-06-15T09:39:58.208Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-06-15T09:39:58.208Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-06-15T09:39:58.208Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-06-15T09:39:58.208Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-06-15T09:39:58.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-06-15T09:39:58.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-06-15T09:39:58.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-06-15T09:39:58.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-06-15T09:39:58.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-06-15T09:39:58.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-06-15T09:39:58.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-06-15T09:39:58.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-06-15T09:39:58.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-06-15T09:39:58.210Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-06-15T09:39:58.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-06-15T09:39:58.210Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-06-15T09:39:58.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-06-15T09:39:58.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-06-15T09:39:58.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-06-15T09:39:58.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-06-15T09:39:58.210Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-06-15T09:39:58.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-06-15T09:39:58.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-06-15T09:39:58.210Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-06-15T09:39:58.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-06-15T09:39:58.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-06-15T09:39:58.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-06-15T09:39:58.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-06-15T09:39:58.211Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-06-15T09:39:58.211Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-06-15T09:39:58.211Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-06-15T09:39:58.211Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-06-15T09:39:58.211Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-06-15T09:39:58.211Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-06-15T09:39:58.211Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-06-15T09:39:58.211Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-06-15T09:39:58.211Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-06-15T09:39:58.211Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-06-15T09:39:58.211Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-06-15T09:39:58.211Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-06-15T09:39:58.211Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-06-15T09:39:58.211Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-06-15T09:39:58.212Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-06-15T09:39:58.212Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-06-15T09:39:58.212Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-06-15T09:39:58.212Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-06-15T09:39:58.212Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-06-15T09:39:58.212Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-06-15T09:39:58.212Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-06-15T09:39:58.212Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-06-15T09:39:58.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-06-15T09:39:58.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-06-15T09:39:58.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-06-15T09:39:58.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-06-15T09:39:58.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-06-15T09:39:58.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-06-15T09:39:58.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-06-15T09:39:58.213Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-06-15T09:39:58.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-06-15T09:39:58.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-06-15T09:39:58.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-06-15T09:39:58.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-06-15T09:39:58.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-06-15T09:39:58.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-06-15T09:39:58.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-06-15T09:39:58.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-06-15T09:39:58.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-06-15T09:39:58.214Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-06-15T09:39:58.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-06-15T09:39:58.214Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-06-15T09:39:58.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-06-15T09:39:58.214Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-06-15T09:39:58.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-06-15T09:39:58.214Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-06-15T09:39:58.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-06-15T09:39:58.214Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-06-15T09:39:58.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-06-15T09:39:58.215Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-06-15T09:39:58.215Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-06-15T09:39:58.215Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-06-15T09:39:58.215Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-06-15T09:39:58.215Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-06-15T09:39:58.215Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-06-15T09:39:58.215Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-06-15T09:39:58.215Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-06-15T09:39:58.215Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-06-15T09:39:58.215Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-06-15T09:39:58.215Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-06-15T09:39:58.215Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-06-15T09:39:58.216Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-06-15T09:39:58.216Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-06-15T09:39:58.216Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-06-15T09:39:58.217Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-06-15T09:39:58.217Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-06-15T09:39:58.217Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-06-15T09:39:58.217Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-06-15T09:39:58.217Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-06-15T09:39:58.217Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-06-15T09:39:58.217Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-06-15T09:39:58.218Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-06-15T09:39:58.218Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-06-15T09:39:58.218Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-06-15T09:39:58.218Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-06-15T09:39:58.218Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-06-15T09:39:58.218Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-06-15T09:39:58.218Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-06-15T09:39:58.218Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-06-15T09:39:58.218Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-06-15T09:39:58.218Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-06-15T09:39:58.218Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-06-15T09:39:58.218Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-06-15T09:39:58.218Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-06-15T09:39:58.218Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-06-15T09:39:58.219Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-06-15T09:39:58.219Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-06-15T09:39:58.219Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-06-15T09:39:58.219Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-06-15T09:39:58.219Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-06-15T09:39:58.219Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-06-15T09:39:58.219Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-06-15T09:39:58.219Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-06-15T09:39:58.219Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-06-15T09:39:58.219Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-06-15T09:39:58.219Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-06-15T09:39:58.219Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-06-15T09:39:58.219Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-06-15T09:39:58.219Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-06-15T09:39:58.223Z"}
{"context":"DatabaseService","level":"info","message":"pg_trgm扩展已启用","timestamp":"2025-06-15T09:39:58.229Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-06-15T09:39:58.236Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:39:58.237Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:39:58.237Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:39:58.237Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:39:58.237Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:39:58.238Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:39:58.238Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:39:58.238Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:39:58.238Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-06-15T09:40:06.893Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:40:06.908Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:40:06.908Z"}
{"context":"TranslationService","level":"info","message":"翻译服务初始化完成 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-06-15T09:40:06.909Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:40:06.909Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-06-15T09:40:06.909Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:40:06.909Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-06-15T09:40:06.910Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-06-15T09:40:06.910Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:40:06.911Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:40:06.911Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:40:06.911Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:40:06.911Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-06-15T09:40:06.913Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-06-15T09:40:06.913Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-06-15T09:40:06.916Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-06-15T09:40:06.916Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-06-15T09:40:06.917Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-06-15T09:40:06.918Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-06-15T09:40:06.918Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-06-15T09:40:06.918Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-06-15T09:40:06.934Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:40:06.934Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:40:06.934Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:40:06.934Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:40:06.934Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-06-15T09:40:06.935Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-06-15T09:40:06.935Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-06-15T09:40:06.935Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-06-15T09:40:06.935Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-06-15T09:40:06.935Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-06-15T09:40:06.935Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-06-15T09:40:06.991Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-06-15T09:40:06.993Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-06-15T09:40:06.993Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-06-15T09:40:06.993Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-06-15T09:40:06.993Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-06-15T09:40:06.993Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-06-15T09:40:06.993Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-06-15T09:40:06.994Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-06-15T09:40:06.994Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-06-15T09:40:06.994Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-06-15T09:40:06.994Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-06-15T09:40:06.994Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-06-15T09:40:06.994Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-06-15T09:40:06.994Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-06-15T09:40:06.994Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-06-15T09:40:06.994Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-06-15T09:40:06.994Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-06-15T09:40:06.994Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-06-15T09:40:06.995Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-06-15T09:40:06.995Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-06-15T09:40:06.995Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-06-15T09:40:06.995Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-06-15T09:40:06.995Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-06-15T09:40:06.995Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-06-15T09:40:06.995Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-06-15T09:40:06.995Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-06-15T09:40:06.995Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-06-15T09:40:06.995Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-06-15T09:40:06.995Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-06-15T09:40:06.995Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-06-15T09:40:06.995Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-06-15T09:40:06.995Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-06-15T09:40:06.995Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-06-15T09:40:06.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-06-15T09:40:06.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-06-15T09:40:06.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-06-15T09:40:06.996Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-06-15T09:40:06.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-06-15T09:40:06.996Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-06-15T09:40:06.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-06-15T09:40:06.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-06-15T09:40:06.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-06-15T09:40:06.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-06-15T09:40:06.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-06-15T09:40:06.996Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-06-15T09:40:06.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-06-15T09:40:06.996Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-06-15T09:40:06.996Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-06-15T09:40:06.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-06-15T09:40:06.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-06-15T09:40:06.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-06-15T09:40:06.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-06-15T09:40:06.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-06-15T09:40:06.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-06-15T09:40:06.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-06-15T09:40:06.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-06-15T09:40:06.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-06-15T09:40:06.997Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-06-15T09:40:06.997Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-06-15T09:40:06.998Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-06-15T09:40:06.998Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-06-15T09:40:06.998Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-06-15T09:40:06.998Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-06-15T09:40:06.998Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-06-15T09:40:06.998Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-06-15T09:40:06.998Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-06-15T09:40:06.998Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-06-15T09:40:06.998Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-06-15T09:40:06.998Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-06-15T09:40:06.998Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-06-15T09:40:06.998Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-06-15T09:40:06.998Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-06-15T09:40:06.999Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-06-15T09:40:06.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-06-15T09:40:06.999Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-06-15T09:40:06.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-06-15T09:40:06.999Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-06-15T09:40:06.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-06-15T09:40:06.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-06-15T09:40:06.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-06-15T09:40:06.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-06-15T09:40:06.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-06-15T09:40:06.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-06-15T09:40:06.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-06-15T09:40:06.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-06-15T09:40:06.999Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-06-15T09:40:07.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-06-15T09:40:07.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-06-15T09:40:07.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-06-15T09:40:07.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-06-15T09:40:07.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-06-15T09:40:07.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-06-15T09:40:07.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-06-15T09:40:07.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-06-15T09:40:07.000Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-06-15T09:40:07.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-06-15T09:40:07.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-06-15T09:40:07.000Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-06-15T09:40:07.000Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-06-15T09:40:07.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-06-15T09:40:07.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-06-15T09:40:07.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-06-15T09:40:07.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-06-15T09:40:07.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-06-15T09:40:07.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-06-15T09:40:07.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-06-15T09:40:07.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-06-15T09:40:07.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-06-15T09:40:07.001Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-06-15T09:40:07.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-06-15T09:40:07.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-06-15T09:40:07.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-06-15T09:40:07.001Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-06-15T09:40:07.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-06-15T09:40:07.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-06-15T09:40:07.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-06-15T09:40:07.002Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-06-15T09:40:07.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-06-15T09:40:07.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-06-15T09:40:07.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-06-15T09:40:07.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-06-15T09:40:07.002Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-06-15T09:40:07.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-06-15T09:40:07.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-06-15T09:40:07.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-06-15T09:40:07.002Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-06-15T09:40:07.003Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-06-15T09:40:07.003Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-06-15T09:40:07.006Z"}
{"context":"DatabaseService","level":"info","message":"pg_trgm扩展已启用","timestamp":"2025-06-15T09:40:07.012Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-06-15T09:40:07.019Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:40:07.020Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:40:07.020Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:40:07.020Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:40:07.020Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:40:07.020Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:40:07.020Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:40:07.020Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:40:07.020Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:40:10.376Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:40:10.379Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:40:10.919Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-06-15T09:40:13.818Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:40:13.833Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:40:13.834Z"}
{"context":"TranslationService","level":"info","message":"翻译服务初始化完成 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-06-15T09:40:13.834Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:40:13.835Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-06-15T09:40:13.835Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:40:13.835Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-06-15T09:40:13.836Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-06-15T09:40:13.836Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:40:13.836Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:40:13.836Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:40:13.836Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:40:13.837Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:40:13.837Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-06-15T09:40:13.838Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-06-15T09:40:13.838Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-06-15T09:40:13.841Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-06-15T09:40:13.842Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-06-15T09:40:13.842Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-06-15T09:40:13.843Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-06-15T09:40:13.843Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-06-15T09:40:13.843Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:40:13.874Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:40:13.874Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:40:13.874Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:40:13.874Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:40:13.874Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:40:13.874Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:40:13.874Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:40:13.874Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-06-15T09:40:13.884Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:40:13.884Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:40:13.884Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:40:13.884Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:40:13.884Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-06-15T09:40:13.885Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-06-15T09:40:13.885Z"}
{"context":"SatelliteSchedulerService","level":"info","message":"卫星数据定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 3 * * *\",\"timezone\":\"Asia/Shanghai\",\"saveToDatabase\":true}","timestamp":"2025-06-15T09:40:13.885Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-06-15T09:40:13.886Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-06-15T09:40:13.886Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-06-15T09:40:13.886Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-06-15T09:40:13.886Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteSchedulerModule dependencies initialized","timestamp":"2025-06-15T09:40:13.886Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-06-15T09:40:13.949Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-06-15T09:40:13.950Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-06-15T09:40:13.950Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-06-15T09:40:13.950Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-06-15T09:40:13.950Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-06-15T09:40:13.950Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-06-15T09:40:13.951Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-06-15T09:40:13.951Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-06-15T09:40:13.951Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-06-15T09:40:13.951Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-06-15T09:40:13.951Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-06-15T09:40:13.951Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-06-15T09:40:13.951Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-06-15T09:40:13.952Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-06-15T09:40:13.952Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-06-15T09:40:13.952Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-06-15T09:40:13.952Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-06-15T09:40:13.952Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-06-15T09:40:13.952Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-06-15T09:40:13.952Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-06-15T09:40:13.952Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-06-15T09:40:13.952Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-06-15T09:40:13.952Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-06-15T09:40:13.952Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-06-15T09:40:13.952Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-06-15T09:40:13.953Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-06-15T09:40:13.953Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-06-15T09:40:13.953Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-06-15T09:40:13.953Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-06-15T09:40:13.953Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-06-15T09:40:13.953Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-06-15T09:40:13.953Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-06-15T09:40:13.953Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-06-15T09:40:13.953Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-06-15T09:40:13.953Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-06-15T09:40:13.953Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-06-15T09:40:13.953Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-06-15T09:40:13.954Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-06-15T09:40:13.954Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-06-15T09:40:13.954Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-06-15T09:40:13.954Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-06-15T09:40:13.954Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-06-15T09:40:13.954Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-06-15T09:40:13.954Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-06-15T09:40:13.954Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-06-15T09:40:13.954Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-06-15T09:40:13.954Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-06-15T09:40:13.954Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-06-15T09:40:13.955Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-06-15T09:40:13.955Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-06-15T09:40:13.955Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-06-15T09:40:13.955Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-06-15T09:40:13.955Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-06-15T09:40:13.955Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-06-15T09:40:13.955Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-06-15T09:40:13.955Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-06-15T09:40:13.955Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-06-15T09:40:13.955Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-06-15T09:40:13.955Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-06-15T09:40:13.956Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-06-15T09:40:13.956Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-06-15T09:40:13.956Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-06-15T09:40:13.956Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-06-15T09:40:13.956Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-06-15T09:40:13.956Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-06-15T09:40:13.956Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-06-15T09:40:13.956Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-06-15T09:40:13.956Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-06-15T09:40:13.956Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-06-15T09:40:13.956Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-06-15T09:40:13.956Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-06-15T09:40:13.956Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-06-15T09:40:13.957Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-06-15T09:40:13.957Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-06-15T09:40:13.957Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-06-15T09:40:13.957Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-06-15T09:40:13.957Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-06-15T09:40:13.957Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-06-15T09:40:13.957Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-06-15T09:40:13.957Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-06-15T09:40:13.957Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-06-15T09:40:13.957Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-06-15T09:40:13.957Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-06-15T09:40:13.957Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-06-15T09:40:13.958Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-06-15T09:40:13.958Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-06-15T09:40:13.958Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-06-15T09:40:13.958Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-06-15T09:40:13.958Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-06-15T09:40:13.958Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-06-15T09:40:13.958Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-06-15T09:40:13.958Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-06-15T09:40:13.958Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-06-15T09:40:13.958Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-06-15T09:40:13.958Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-06-15T09:40:13.958Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-06-15T09:40:13.958Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-06-15T09:40:13.959Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-06-15T09:40:13.959Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-06-15T09:40:13.959Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-06-15T09:40:13.959Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-06-15T09:40:13.959Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-06-15T09:40:13.959Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-06-15T09:40:13.959Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-06-15T09:40:13.959Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-06-15T09:40:13.959Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-06-15T09:40:13.959Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-06-15T09:40:13.959Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-06-15T09:40:13.959Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-06-15T09:40:13.959Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-06-15T09:40:13.959Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-06-15T09:40:13.960Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-06-15T09:40:13.960Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-06-15T09:40:13.960Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-06-15T09:40:13.960Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-06-15T09:40:13.960Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-06-15T09:40:13.960Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-06-15T09:40:13.960Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-06-15T09:40:13.960Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-06-15T09:40:13.960Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-06-15T09:40:13.961Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-06-15T09:40:13.961Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-06-15T09:40:13.961Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-06-15T09:40:13.961Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-06-15T09:40:13.961Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-06-15T09:40:13.961Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-06-15T09:40:13.961Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-06-15T09:40:13.961Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteSchedulerController {/api/satellite-scheduler}:","timestamp":"2025-06-15T09:40:13.961Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/status, GET} route","timestamp":"2025-06-15T09:40:13.962Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/trigger, POST} route","timestamp":"2025-06-15T09:40:13.962Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/config, POST} route","timestamp":"2025-06-15T09:40:13.962Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/stop, POST} route","timestamp":"2025-06-15T09:40:13.962Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/cron-expressions, GET} route","timestamp":"2025-06-15T09:40:13.962Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-06-15T09:40:13.966Z"}
{"context":"DatabaseService","level":"info","message":"pg_trgm扩展已启用","timestamp":"2025-06-15T09:40:13.968Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-06-15T09:40:13.977Z"}
