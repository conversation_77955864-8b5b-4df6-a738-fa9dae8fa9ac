"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateLLMConfig = exports.mergeConfig = exports.getThemeExtractionConfig = exports.getTranslationConfig = exports.ALTERNATIVE_CONFIGS = exports.DEFAULT_THEME_EXTRACTION_CONFIG = exports.DEFAULT_TRANSLATION_CONFIG = exports.LLMProvider = void 0;
var LLMProvider;
(function (LLMProvider) {
    LLMProvider["QWEN"] = "qwen";
    LLMProvider["OPENAI"] = "openai";
    LLMProvider["CLAUDE"] = "claude";
    LLMProvider["GLM"] = "glm";
})(LLMProvider = exports.LLMProvider || (exports.LLMProvider = {}));
exports.DEFAULT_TRANSLATION_CONFIG = {
    provider: LLMProvider.QWEN,
    model: 'qwen-turbo',
    apiKey: process.env.QWEN_API_KEY || 'sk-1b581fd6f319419d9d0e3f2cc855c10d',
    baseURL: process.env.QWEN_BASE_URL || 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    maxTokens: 4000,
    temperature: 0.1,
    timeout: 30000,
    maxRetries: 2,
    retryDelay: 1000,
    maxConcurrentRequests: 3,
    systemPrompt: '你是一位专业翻译，特别是很擅长在航空航天领域的翻译，将以下内容准确翻译为中文，保留数字和专有名词格式。',
    maxTextLength: 1500,
    batchSize: 10
};
exports.DEFAULT_THEME_EXTRACTION_CONFIG = {
    provider: LLMProvider.QWEN,
    model: 'qwen-turbo',
    apiKey: process.env.QWEN_API_KEY || 'sk-1b581fd6f319419d9d0e3f2cc855c10d',
    baseURL: process.env.QWEN_BASE_URL || 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    maxTokens: 2000,
    temperature: 0.2,
    timeout: 30000,
    maxRetries: 2,
    retryDelay: 1000,
    maxConcurrentRequests: 3,
    systemPrompt: '你是一位专业的文本分析专家，擅长提取文章的主题词和识别内容类型。请从以下文本中（可能是中文或英文）：1）提取不超过5个最重要的主题词，这些词应该能够概括文章的核心内容，主题词必须用中文表示；2）识别文章类型，请严格按照以下标准判断：科普类型-文章主要目的是教育和知识普及，重点解释科学原理、技术概念、工作机制等，具有明显的教学性质；军事类型-文章涉及军事装备、军事行动、国防安全、武器系统等；新闻事实-文章主要报道具体事件、发布消息、描述事实，即使涉及技术话题也主要是事实报道而非教学解释。判断原则：只有当文章明确以教育为目的、主要在解释概念和原理时才标记为科普；单纯的技术新闻报道不应标记为科普。如果判断为科普类型，请在主题词中添加"科普"；如果判断为军事类型，请在主题词中添加"军事"；如果都不是，则不添加类型标签。只返回最终的中文主题词，用逗号分隔，不要有其他内容。',
    maxContentLength: 2000,
    maxThemes: 5
};
exports.ALTERNATIVE_CONFIGS = {
    HIGH_QUALITY_TRANSLATION: Object.assign(Object.assign({}, exports.DEFAULT_TRANSLATION_CONFIG), { model: 'qwen-max-latest', temperature: 0.05, maxConcurrentRequests: 2 }),
    FAST_TRANSLATION: Object.assign(Object.assign({}, exports.DEFAULT_TRANSLATION_CONFIG), { model: 'qwen-turbo', temperature: 0.2, maxConcurrentRequests: 5 }),
    HIGH_QUALITY_THEME_EXTRACTION: Object.assign(Object.assign({}, exports.DEFAULT_THEME_EXTRACTION_CONFIG), { model: 'qwen-max-latest', temperature: 0.1, maxConcurrentRequests: 2, systemPrompt: '你是一位专业的文本分析专家，擅长提取文章的主题词和识别内容类型。请从以下文本中（可能是中文或英文）：1）提取不超过5个最重要的主题词，这些词应该能够概括文章的核心内容，主题词必须用中文表示；2）识别文章类型，请严格按照以下标准判断：科普类型-文章主要目的是教育和知识普及，重点解释科学原理、技术概念、工作机制等，具有明显的教学性质；军事类型-文章涉及军事装备、军事行动、国防安全、武器系统等；新闻事实-文章主要报道具体事件、发布消息、描述事实，即使涉及技术话题也主要是事实报道而非教学解释。判断原则：只有当文章明确以教育为目的、主要在解释概念和原理时才标记为科普；单纯的技术新闻报道不应标记为科普。如果判断为科普类型，请在主题词中添加"科普"；如果判断为军事类型，请在主题词中添加"军事"；如果都不是，则不添加类型标签。只返回最终的中文主题词，用逗号分隔，不要有其他内容。' }),
    FAST_THEME_EXTRACTION: Object.assign(Object.assign({}, exports.DEFAULT_THEME_EXTRACTION_CONFIG), { model: 'qwen-turbo', temperature: 0.3, maxConcurrentRequests: 5, systemPrompt: '你是一位专业的文本分析专家，擅长提取文章的主题词和识别内容类型。请从以下文本中（可能是中文或英文）：1）提取不超过5个最重要的主题词，这些词应该能够概括文章的核心内容，主题词必须用中文表示；2）识别文章类型，请严格按照以下标准判断：科普类型-文章主要目的是教育和知识普及，重点解释科学原理、技术概念、工作机制等，具有明显的教学性质；军事类型-文章涉及军事装备、军事行动、国防安全、武器系统等；新闻事实-文章主要报道具体事件、发布消息、描述事实，即使涉及技术话题也主要是事实报道而非教学解释。判断原则：只有当文章明确以教育为目的、主要在解释概念和原理时才标记为科普；单纯的技术新闻报道不应标记为科普。如果判断为科普类型，请在主题词中添加"科普"；如果判断为军事类型，请在主题词中添加"军事"；如果都不是，则不添加类型标签。只返回最终的中文主题词，用逗号分隔，不要有其他内容。' })
};
function getTranslationConfig(mode = 'default') {
    switch (mode) {
        case 'high_quality':
            return exports.ALTERNATIVE_CONFIGS.HIGH_QUALITY_TRANSLATION;
        case 'fast':
            return exports.ALTERNATIVE_CONFIGS.FAST_TRANSLATION;
        default:
            return exports.DEFAULT_TRANSLATION_CONFIG;
    }
}
exports.getTranslationConfig = getTranslationConfig;
function getThemeExtractionConfig(mode = 'default') {
    switch (mode) {
        case 'high_quality':
            return exports.ALTERNATIVE_CONFIGS.HIGH_QUALITY_THEME_EXTRACTION;
        case 'fast':
            return exports.ALTERNATIVE_CONFIGS.FAST_THEME_EXTRACTION;
        default:
            return exports.DEFAULT_THEME_EXTRACTION_CONFIG;
    }
}
exports.getThemeExtractionConfig = getThemeExtractionConfig;
function mergeConfig(baseConfig, customConfig) {
    return Object.assign(Object.assign({}, baseConfig), customConfig);
}
exports.mergeConfig = mergeConfig;
function validateLLMConfig(config) {
    if (!config.apiKey) {
        throw new Error('API密钥不能为空');
    }
    if (!config.model) {
        throw new Error('模型名称不能为空');
    }
    if (config.maxRetries !== undefined && config.maxRetries < 0) {
        throw new Error('最大重试次数不能小于0');
    }
    if (config.timeout !== undefined && config.timeout <= 0) {
        throw new Error('超时时间必须大于0');
    }
    if (config.maxConcurrentRequests !== undefined && config.maxConcurrentRequests <= 0) {
        throw new Error('最大并发请求数必须大于0');
    }
}
exports.validateLLMConfig = validateLLMConfig;
//# sourceMappingURL=llm.config.js.map