"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TranslationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TranslationService = void 0;
const common_1 = require("@nestjs/common");
const openai_1 = require("openai");
const llm_config_1 = require("../../../config/llm.config");
let TranslationService = TranslationService_1 = class TranslationService {
    constructor() {
        this.logger = new common_1.Logger(TranslationService_1.name);
        this.translationCache = new Map();
        this.apiCallCount = 0;
        this.apiTotalTime = 0;
        this.activeRequests = 0;
        this.requestQueue = [];
        this.paragraphSeparators = ["\n\n", "\n", ". ", "! ", "? "];
        this.consecutiveFailures = 0;
        this.failureStats = {
            contentFilter: 0,
            timeout: 0,
            rateLimit: 0,
            networkError: 0,
            other: 0,
            totalAttempts: 0,
            successfulProcessing: 0
        };
        this.sensitiveReplacements = [
            { pattern: /\bterrorist(s)?\b/gi, replacement: 'extremist $1' },
            { pattern: /\bterrorism\b/gi, replacement: 'extremism' },
            { pattern: /\bexplosive(s)?\b/gi, replacement: 'explosive material' },
            { pattern: /\bbomb(s)?\b/gi, replacement: 'explosive device$1' },
            { pattern: /\bnuclear weapon(s)?\b/gi, replacement: 'special weapon$1' },
            { pattern: /\bmissile(s)?\b/gi, replacement: 'projectile$1' },
            { pattern: /炸弹/g, replacement: '爆炸装置' },
            { pattern: /核武器/g, replacement: '特殊武器' },
            { pattern: /恐怖分子/g, replacement: '极端分子' },
            { pattern: /恐怖主义/g, replacement: '极端主义' }
        ];
        this.translationConfig = this.loadTranslationConfig();
        this.themeExtractionConfig = this.loadThemeExtractionConfig();
        try {
            (0, llm_config_1.validateLLMConfig)(this.translationConfig);
            (0, llm_config_1.validateLLMConfig)(this.themeExtractionConfig);
        }
        catch (error) {
            this.logger.error(`配置验证失败: ${error.message}`);
            throw error;
        }
        this.openai = new openai_1.default({
            apiKey: this.translationConfig.apiKey,
            baseURL: this.translationConfig.baseURL
        });
        this.logger.log(`翻译服务初始化完成 - 翻译模型: ${this.translationConfig.model}, 主题提取模型: ${this.themeExtractionConfig.model}`);
    }
    loadTranslationConfig() {
        const mode = process.env.TRANSLATION_MODE || 'default';
        let config = (0, llm_config_1.getTranslationConfig)(mode);
        const envOverrides = {};
        if (process.env.TRANSLATION_MODEL) {
            envOverrides.model = process.env.TRANSLATION_MODEL;
        }
        if (process.env.QWEN_API_KEY) {
            envOverrides.apiKey = process.env.QWEN_API_KEY;
        }
        if (process.env.QWEN_BASE_URL) {
            envOverrides.baseURL = process.env.QWEN_BASE_URL;
        }
        if (process.env.TRANSLATION_MAX_CONCURRENT) {
            envOverrides.maxConcurrentRequests = parseInt(process.env.TRANSLATION_MAX_CONCURRENT);
        }
        if (process.env.TRANSLATION_TIMEOUT) {
            envOverrides.timeout = parseInt(process.env.TRANSLATION_TIMEOUT);
        }
        if (process.env.TRANSLATION_MAX_RETRIES) {
            envOverrides.maxRetries = parseInt(process.env.TRANSLATION_MAX_RETRIES);
        }
        config = (0, llm_config_1.mergeConfig)(config, envOverrides);
        this.logger.log(`加载翻译配置 - 模式: ${mode}, 模型: ${config.model}`);
        return config;
    }
    loadThemeExtractionConfig() {
        const mode = process.env.THEME_EXTRACTION_MODE || 'default';
        let config = (0, llm_config_1.getThemeExtractionConfig)(mode);
        const envOverrides = {};
        if (process.env.THEME_EXTRACTION_MODEL) {
            envOverrides.model = process.env.THEME_EXTRACTION_MODEL;
        }
        if (process.env.QWEN_API_KEY) {
            envOverrides.apiKey = process.env.QWEN_API_KEY;
        }
        if (process.env.QWEN_BASE_URL) {
            envOverrides.baseURL = process.env.QWEN_BASE_URL;
        }
        if (process.env.THEME_EXTRACTION_MAX_CONCURRENT) {
            envOverrides.maxConcurrentRequests = parseInt(process.env.THEME_EXTRACTION_MAX_CONCURRENT);
        }
        if (process.env.THEME_EXTRACTION_TIMEOUT) {
            envOverrides.timeout = parseInt(process.env.THEME_EXTRACTION_TIMEOUT);
        }
        if (process.env.THEME_EXTRACTION_MAX_RETRIES) {
            envOverrides.maxRetries = parseInt(process.env.THEME_EXTRACTION_MAX_RETRIES);
        }
        config = (0, llm_config_1.mergeConfig)(config, envOverrides);
        this.logger.log(`加载主题提取配置 - 模式: ${mode}, 模型: ${config.model}`);
        return config;
    }
    getTranslationConfig() {
        return Object.assign({}, this.translationConfig);
    }
    getThemeExtractionConfig() {
        return Object.assign({}, this.themeExtractionConfig);
    }
    updateTranslationConfig(newConfig) {
        this.translationConfig = (0, llm_config_1.mergeConfig)(this.translationConfig, newConfig);
        (0, llm_config_1.validateLLMConfig)(this.translationConfig);
        if (newConfig.apiKey || newConfig.baseURL) {
            this.openai = new openai_1.default({
                apiKey: this.translationConfig.apiKey,
                baseURL: this.translationConfig.baseURL
            });
        }
        this.logger.log(`翻译配置已更新 - 模型: ${this.translationConfig.model}`);
    }
    updateThemeExtractionConfig(newConfig) {
        this.themeExtractionConfig = (0, llm_config_1.mergeConfig)(this.themeExtractionConfig, newConfig);
        (0, llm_config_1.validateLLMConfig)(this.themeExtractionConfig);
        this.logger.log(`主题提取配置已更新 - 模型: ${this.themeExtractionConfig.model}`);
    }
    sanitizeSensitiveWords(text) {
        let sanitized = text;
        for (const { pattern, replacement } of this.sensitiveReplacements) {
            sanitized = sanitized.replace(pattern, replacement);
        }
        return sanitized;
    }
    async translateText(text, retryCount = 0, isSegment = false) {
        if (!text || text.trim() === '') {
            return '';
        }
        this.failureStats.totalAttempts++;
        let inputText = text;
        if (!isSegment) {
            inputText = this.sanitizeSensitiveWords(text);
        }
        const cacheKey = this.generateCacheKey(inputText);
        if (this.translationCache.has(cacheKey)) {
            this.apiCallCount++;
            this.logger.debug(`命中翻译缓存 - 长度: ${inputText.length}`);
            return this.translationCache.get(cacheKey) || '';
        }
        if (!isSegment && inputText.length > this.translationConfig.maxTextLength) {
            return this.translateLongText(inputText);
        }
        return this.executeWithConcurrencyControl(async () => {
            var _a, _b;
            try {
                this.apiCallCount++;
                const startTime = Date.now();
                const processedText = this.preprocessContent(inputText);
                const completion = await this.openai.chat.completions.create({
                    model: this.translationConfig.model,
                    messages: [
                        { role: 'system', content: this.translationConfig.systemPrompt },
                        { role: 'user', content: processedText }
                    ],
                });
                const translatedText = ((_b = (_a = completion.choices[0]) === null || _a === void 0 ? void 0 : _a.message) === null || _b === void 0 ? void 0 : _b.content) || '';
                const elapsed = Date.now() - startTime;
                this.apiTotalTime += elapsed;
                this.logger.debug(`翻译API调用完成，耗时: ${elapsed}ms，总调用次数: ${this.apiCallCount}`);
                this.translationCache.set(cacheKey, translatedText);
                this.consecutiveFailures = 0;
                this.failureStats.successfulProcessing++;
                return translatedText;
            }
            catch (error) {
                this.consecutiveFailures++;
                this.logger.error(`翻译失败 - 模型: ${this.translationConfig.model}, 重试次数: ${retryCount}, 错误: ${error.message}`);
                if (error.message.includes('inappropriate content') ||
                    error.message.includes('content_filter') ||
                    error.message.includes('content policy') ||
                    error.message.includes('safety') ||
                    error.message.includes('harmful')) {
                    this.failureStats.contentFilter++;
                    this.logger.warn(`检测到内容过滤错误，错误信息: ${error.message}, 当前重试次数=${retryCount}, 最大重试次数=${this.translationConfig.maxRetries}`);
                    if (retryCount >= (this.translationConfig.maxRetries || 2)) {
                        this.logger.error(`内容过滤错误超过最大重试次数(${this.translationConfig.maxRetries})，当前重试次数=${retryCount}，跳过该文档`);
                        this.logger.error(`原始文本前100字符: ${inputText.substring(0, 100)}...`);
                        throw new Error(`SKIP_DOCUMENT:内容过滤错误超过最大重试次数 - ${error.message}`);
                    }
                    this.logger.warn(`使用智能预处理重试，重试次数: ${retryCount + 1}`);
                    const preprocessingResult = this.intelligentPreprocessing(inputText);
                    this.logger.debug(`智能预处理结果: 策略=${preprocessingResult.strategy}, 敏感词=${preprocessingResult.analysis.original.sensitiveWords.length}个, 风险等级=${preprocessingResult.analysis.original.riskLevel}, 敏感类别=[${preprocessingResult.analysis.original.categories.join(', ')}]`);
                    const originalLength = inputText.length;
                    const processedLength = preprocessingResult.processedText.length;
                    this.logger.debug(`文本预处理: 原长度=${originalLength}, 处理后长度=${processedLength}, 压缩率=${Math.round((1 - processedLength / originalLength) * 100)}%`);
                    if (preprocessingResult.needsManualReview) {
                        this.logger.warn(`文档需要人工审核: 敏感类别=${preprocessingResult.analysis.original.categories.join(', ')}, 敏感词数量=${preprocessingResult.analysis.original.sensitiveWords.length}`);
                        this.logger.debug(`检测到的敏感词: ${preprocessingResult.analysis.original.sensitiveWords.slice(0, 10).join(', ')}${preprocessingResult.analysis.original.sensitiveWords.length > 10 ? '...' : ''}`);
                    }
                    return this.translateText(preprocessingResult.processedText, retryCount + 1, isSegment);
                }
                if (error.message.includes('timed out') || error.message.includes('timeout')) {
                    this.failureStats.timeout++;
                    if (retryCount < (this.translationConfig.maxRetries || 2)) {
                        const waitTime = Math.pow(2, retryCount) * (this.translationConfig.retryDelay || 1000);
                        this.logger.warn(`翻译请求超时，${waitTime}ms后重试，重试次数: ${retryCount + 1} - 模型: ${this.translationConfig.model}`);
                        await new Promise(resolve => setTimeout(resolve, waitTime));
                        return this.translateText(inputText, retryCount + 1, isSegment);
                    }
                    else {
                        this.logger.error(`翻译超时错误超过最大重试次数(${this.translationConfig.maxRetries})，跳过该文档`);
                        throw new Error(`SKIP_DOCUMENT:翻译超时错误超过最大重试次数`);
                    }
                }
                if (error.message.includes('rate limit') || error.message.includes('429')) {
                    this.failureStats.rateLimit++;
                    if (retryCount < (this.translationConfig.maxRetries || 2)) {
                        const waitTime = (retryCount + 1) * 5000;
                        this.logger.warn(`API限制错误，${waitTime}ms后重试，重试次数: ${retryCount + 1} - 模型: ${this.translationConfig.model}`);
                        await new Promise(resolve => setTimeout(resolve, waitTime));
                        return this.translateText(inputText, retryCount + 1, isSegment);
                    }
                    else {
                        this.logger.error(`API限制错误超过最大重试次数(${this.translationConfig.maxRetries})，跳过该文档`);
                        throw new Error(`SKIP_DOCUMENT:API限制错误超过最大重试次数`);
                    }
                }
                if (error.message.includes('network') ||
                    error.message.includes('connection') ||
                    error.message.includes('ENOTFOUND') ||
                    error.message.includes('ECONNRESET') ||
                    error.message.includes('socket')) {
                    this.failureStats.networkError++;
                    this.logger.error(`网络连接错误 - 模型: ${this.translationConfig.model}: ${error.message}`);
                }
                else {
                    this.failureStats.other++;
                }
                this.logger.error(`翻译失败 - 模型: ${this.translationConfig.model}: ${error.message}`, error.stack);
                throw new Error(`翻译失败: ${error.message}`);
            }
        });
    }
    async translateBatch(texts) {
        const results = [];
        for (const text of texts) {
            try {
                const translated = await this.translateText(text);
                results.push(translated);
            }
            catch (error) {
                this.logger.error(`批量翻译出错: ${error.message}`);
                results.push('');
            }
        }
        return results;
    }
    generateCacheKey(text) {
        return text.trim().toLowerCase();
    }
    getAPIStats() {
        return {
            callCount: this.apiCallCount,
            totalTime: this.apiTotalTime,
            averageTime: this.apiCallCount > 0 ? this.apiTotalTime / this.apiCallCount : 0,
            cacheSize: this.translationCache.size
        };
    }
    getFailureStats() {
        const totalFailures = this.failureStats.contentFilter +
            this.failureStats.timeout +
            this.failureStats.rateLimit +
            this.failureStats.networkError +
            this.failureStats.other;
        const successRate = this.failureStats.totalAttempts > 0 ?
            (this.failureStats.successfulProcessing / this.failureStats.totalAttempts * 100).toFixed(2) :
            '0.00';
        return {
            totalAttempts: this.failureStats.totalAttempts,
            successfulProcessing: this.failureStats.successfulProcessing,
            totalFailures: totalFailures,
            successRate: parseFloat(successRate),
            failures: {
                contentFilter: {
                    count: this.failureStats.contentFilter,
                    percentage: totalFailures > 0 ? (this.failureStats.contentFilter / totalFailures * 100).toFixed(2) : '0.00'
                },
                timeout: {
                    count: this.failureStats.timeout,
                    percentage: totalFailures > 0 ? (this.failureStats.timeout / totalFailures * 100).toFixed(2) : '0.00'
                },
                rateLimit: {
                    count: this.failureStats.rateLimit,
                    percentage: totalFailures > 0 ? (this.failureStats.rateLimit / totalFailures * 100).toFixed(2) : '0.00'
                },
                networkError: {
                    count: this.failureStats.networkError,
                    percentage: totalFailures > 0 ? (this.failureStats.networkError / totalFailures * 100).toFixed(2) : '0.00'
                },
                other: {
                    count: this.failureStats.other,
                    percentage: totalFailures > 0 ? (this.failureStats.other / totalFailures * 100).toFixed(2) : '0.00'
                }
            },
            consecutiveFailures: this.consecutiveFailures,
            recommendations: this.generateRecommendations()
        };
    }
    generateRecommendations() {
        const recommendations = [];
        const stats = this.failureStats;
        const totalFailures = stats.contentFilter + stats.timeout + stats.rateLimit + stats.networkError + stats.other;
        if (totalFailures === 0) {
            recommendations.push('系统运行正常，无需特殊优化');
            return recommendations;
        }
        if (stats.contentFilter > totalFailures * 0.3) {
            recommendations.push('内容过滤失败率较高，建议：');
            recommendations.push('  - 加强敏感内容预处理');
            recommendations.push('  - 检查新闻来源的内容质量');
            recommendations.push('  - 考虑调整内容处理策略');
        }
        if (stats.timeout > totalFailures * 0.2) {
            recommendations.push('超时问题频发，建议：');
            recommendations.push('  - 增加请求超时时间');
            recommendations.push('  - 减少批次大小');
            recommendations.push('  - 检查网络连接稳定性');
        }
        if (stats.rateLimit > totalFailures * 0.2) {
            recommendations.push('API限制频繁触发，建议：');
            recommendations.push('  - 减少并发请求数');
            recommendations.push('  - 增加请求间隔时间');
            recommendations.push('  - 考虑升级API套餐');
        }
        if (stats.networkError > totalFailures * 0.1) {
            recommendations.push('网络连接不稳定，建议：');
            recommendations.push('  - 检查网络环境');
            recommendations.push('  - 增加重试机制');
            recommendations.push('  - 考虑使用备用网络');
        }
        return recommendations;
    }
    resetFailureStats() {
        this.failureStats = {
            contentFilter: 0,
            timeout: 0,
            rateLimit: 0,
            networkError: 0,
            other: 0,
            totalAttempts: 0,
            successfulProcessing: 0
        };
        this.consecutiveFailures = 0;
        this.logger.log('失败统计已重置');
    }
    clearCache() {
        const cacheSize = this.translationCache.size;
        this.translationCache.clear();
        this.logger.log(`清除了${cacheSize}条翻译缓存`);
    }
    async executeWithConcurrencyControl(task) {
        if (this.activeRequests < (this.translationConfig.maxConcurrentRequests || 3)) {
            this.activeRequests++;
            try {
                return await task();
            }
            finally {
                this.activeRequests--;
                this.processNextQueuedTask();
            }
        }
        return new Promise((resolve, reject) => {
            this.requestQueue.push(async () => {
                try {
                    const result = await task();
                    resolve(result);
                }
                catch (error) {
                    reject(error);
                }
            });
            this.logger.debug(`当前并发请求数: ${this.activeRequests}, 队列中的请求数: ${this.requestQueue.length}`);
        });
    }
    processNextQueuedTask() {
        if (this.requestQueue.length > 0 && this.activeRequests < (this.translationConfig.maxConcurrentRequests || 3)) {
            const nextTask = this.requestQueue.shift();
            if (nextTask) {
                this.activeRequests++;
                nextTask().finally(() => {
                    this.activeRequests--;
                    this.processNextQueuedTask();
                });
            }
        }
    }
    async translateLongText(text) {
        const segments = this.splitTextIntoSegments(text);
        this.logger.debug(`长文本分段处理: 共${segments.length}段`);
        const translatedSegments = [];
        for (let i = 0; i < segments.length; i++) {
            const segment = segments[i];
            if (segment.trim()) {
                try {
                    this.logger.debug(`翻译第${i + 1}/${segments.length}段，长度: ${segment.length}`);
                    const translated = await this.translateText(segment, 0, true);
                    translatedSegments.push(translated);
                    if (i < segments.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 200));
                    }
                }
                catch (error) {
                    this.logger.error(`段落翻译失败: ${error.message}`);
                    translatedSegments.push(`[翻译失败: ${segment.substring(0, 30)}...]`);
                }
            }
            else {
                translatedSegments.push('');
            }
        }
        return translatedSegments.join('\n\n');
    }
    splitTextIntoSegments(text) {
        const segments = [];
        let remainingText = text;
        while (remainingText.length > 0) {
            if (remainingText.length <= this.translationConfig.maxTextLength) {
                segments.push(remainingText);
                break;
            }
            let splitIndex = -1;
            for (const separator of this.paragraphSeparators) {
                const lastSeparatorIndex = remainingText.lastIndexOf(separator, this.translationConfig.maxTextLength);
                if (lastSeparatorIndex > splitIndex) {
                    splitIndex = lastSeparatorIndex + separator.length;
                }
            }
            if (splitIndex <= 0) {
                splitIndex = this.translationConfig.maxTextLength;
            }
            segments.push(remainingText.substring(0, splitIndex));
            remainingText = remainingText.substring(splitIndex);
        }
        return segments;
    }
    removeAdvertisements(text) {
        const adPatterns = [
            /^\s*广告[:：]/i,
            /^\s*Sponsored[:：]?/i,
            /^\s*Advertisement[:：]?/i,
            /点击(这里|链接)查看全文/i,
            /如果您无法正常浏览此邮件/i,
            /免责声明[:：]/i,
            /本文(来源|来自)/i,
            /更多精彩内容请关注/i,
            /All rights reserved/i,
            /版权所有/i
        ];
        const cleanedLines = text.split(/\n+/).filter(line => {
            return !adPatterns.some(pattern => pattern.test(line.trim()));
        });
        if (cleanedLines.length > 0) {
            const lastLine = cleanedLines[cleanedLines.length - 1].trim();
            if (lastLine.length < 30 && /\b(http|www\.|\.com|\.cn)\b/i.test(lastLine)) {
                cleanedLines.pop();
            }
        }
        return cleanedLines.join('\n').trim();
    }
    preprocessContent(text) {
        let processed = this.removeAdvertisements(text);
        processed = processed
            .replace(/<[^>]+>/g, ' ')
            .replace(/https?:\/\/\S+/g, '[URL]')
            .replace(/\S+@\S+\.\S+/g, '[EMAIL]')
            .replace(/\s{2,}/g, ' ')
            .trim();
        return processed;
    }
    applyStricterPreprocessing(text) {
        let processedText = this.preprocessContent(text);
        processedText = processedText
            .replace(/(\d{4,})/g, '[NUMBER]')
            .replace(/[^\w\s.,;:\-()\[\]{}"']/g, ' ')
            .replace(/\b(missile|nuclear|classified|confidential|secret|intelligence|spy|surveillance|hack|breach|leak|sensitive|restricted|clearance|covert|operation|agent|agency|infiltrate|sabotage|conspiracy|whistleblower)\b/gi, '[SENSITIVE]')
            .replace(/\s+/g, ' ')
            .split(/[.!?]\s+/)
            .map(sentence => sentence.trim().substring(0, 100))
            .join('. ')
            .trim();
        if (processedText.length > 1000) {
            processedText = processedText.substring(0, 1000) + '...';
        }
        return processedText;
    }
    async extractThemes(title, content, retryCount = 0) {
        if ((!title || title.trim() === '') && (!content || content.trim() === '')) {
            return '';
        }
        let textForThemeExtraction = '';
        if (title && title.trim() !== '') {
            textForThemeExtraction += `标题：${title}\n\n`;
        }
        if (content && content.trim() !== '') {
            const maxContentLength = 2000;
            const truncatedContent = content.length > maxContentLength
                ? content.substring(0, maxContentLength) + '...'
                : content;
            textForThemeExtraction += `内容：${truncatedContent}`;
        }
        const cacheKey = 'theme_' + this.generateCacheKey(textForThemeExtraction);
        if (this.translationCache.has(cacheKey)) {
            this.logger.debug(`使用缓存的主题提取结果: ${cacheKey.substring(0, 30)}...`);
            return this.translationCache.get(cacheKey) || '';
        }
        return this.executeWithConcurrencyControl(async () => {
            var _a, _b;
            try {
                this.apiCallCount++;
                const startTime = Date.now();
                const completion = await this.openai.chat.completions.create({
                    model: this.themeExtractionConfig.model,
                    messages: [
                        { role: 'system', content: this.themeExtractionConfig.systemPrompt },
                        { role: 'user', content: textForThemeExtraction }
                    ],
                });
                const themes = ((_b = (_a = completion.choices[0]) === null || _a === void 0 ? void 0 : _a.message) === null || _b === void 0 ? void 0 : _b.content) || '';
                const elapsed = Date.now() - startTime;
                this.apiTotalTime += elapsed;
                this.logger.debug(`主题提取API调用完成，耗时: ${elapsed}ms，总调用次数: ${this.apiCallCount}`);
                this.translationCache.set(cacheKey, themes);
                this.consecutiveFailures = 0;
                return themes;
            }
            catch (error) {
                this.consecutiveFailures++;
                this.logger.warn(`主题提取服务连续失败${this.consecutiveFailures}次 - 模型: ${this.themeExtractionConfig.model}`);
                if (error.message.includes('inappropriate content')) {
                    if (retryCount < (this.themeExtractionConfig.maxRetries || 2)) {
                        this.logger.warn(`检测到不适当内容，尝试进一步处理后重试，重试次数: ${retryCount + 1}`);
                        const furtherProcessedTitle = this.applyStricterPreprocessing(title);
                        const furtherProcessedContent = this.applyStricterPreprocessing(content);
                        return this.extractThemes(furtherProcessedTitle, furtherProcessedContent, retryCount + 1);
                    }
                    else {
                        this.logger.error(`主题提取内容不适当错误超过最大重试次数(${this.themeExtractionConfig.maxRetries})，跳过该文档`);
                        throw new Error(`SKIP_DOCUMENT:主题提取内容不适当错误超过最大重试次数`);
                    }
                }
                if (error.message.includes('timed out')) {
                    if (retryCount < (this.themeExtractionConfig.maxRetries || 2)) {
                        const waitTime = Math.pow(2, retryCount) * (this.themeExtractionConfig.retryDelay || 1000);
                        this.logger.warn(`主题提取请求超时，${waitTime}ms后重试，重试次数: ${retryCount + 1} - 模型: ${this.themeExtractionConfig.model}`);
                        await new Promise(resolve => setTimeout(resolve, waitTime));
                        return this.extractThemes(title, content, retryCount + 1);
                    }
                    else {
                        this.logger.error(`主题提取超时错误超过最大重试次数(${this.themeExtractionConfig.maxRetries})，跳过该文档`);
                        throw new Error(`SKIP_DOCUMENT:主题提取超时错误超过最大重试次数`);
                    }
                }
                this.logger.error(`主题提取失败 - 模型: ${this.themeExtractionConfig.model}: ${error.message}`, error.stack);
                throw new Error(`主题提取失败: ${error.message}`);
            }
        });
    }
    async translateAndExtractInOneCall(title, summary, content, retryCount = 0) {
        if ((!title || title.trim() === '') &&
            (!summary || summary.trim() === '') &&
            (!content || content.trim() === '')) {
            return {
                title_cn: '',
                summary_cn: '',
                content_cn: '',
                themes_cn: ''
            };
        }
        let inputText = '';
        if (title && title.trim() !== '') {
            inputText += `标题：${title}\n\n`;
        }
        if (summary && summary.trim() !== '') {
            inputText += `摘要：${summary}\n\n`;
        }
        if (content && content.trim() !== '') {
            const maxContentLength = 2000;
            const truncatedContent = content.length > maxContentLength
                ? content.substring(0, maxContentLength) + '...'
                : content;
            inputText += `内容：${truncatedContent}`;
        }
        const processedInput = this.sanitizeSensitiveWords(inputText);
        const cacheKey = 'combined_' + this.generateCacheKey(processedInput);
        if (this.translationCache.has(cacheKey)) {
            this.logger.debug(`命中组合翻译缓存: ${cacheKey.substring(0, 30)}...`);
            const cachedResult = this.translationCache.get(cacheKey);
            if (cachedResult) {
                try {
                    return JSON.parse(cachedResult);
                }
                catch (parseError) {
                    this.logger.warn(`缓存结果JSON解析失败，重新调用API`);
                }
            }
        }
        const combinedSystemPrompt = `你是一位专业的翻译和文本分析专家，特别擅长航空航天领域。请对以下内容执行三个任务：

1. 翻译：将标题、摘要、内容准确翻译为中文，保留数字和专有名词格式
2. 主题提取：提取不超过5个最重要的中文主题词，概括文章核心内容
3. 类型识别：请严格按照以下标准判断文章类型：
   - 科普类型：文章主要目的是教育和知识普及，重点解释科学原理、技术概念、工作机制等，具有明显的教学性质（如"什么是量子计算"、"火箭发动机工作原理解析"等）
   - 军事类型：文章涉及军事装备、军事行动、国防安全、武器系统等军事相关内容
   - 新闻事实：文章主要报道具体事件、发布消息、描述事实，即使涉及技术话题也主要是事实报道而非教学解释
   
   判断原则：只有当文章明确以教育为目的、主要在解释概念和原理时才标记为科普；单纯的技术新闻报道不应标记为科普。如果是科普类型在主题词中添加"科普"，如果是军事类型添加"军事"，如果都不是则不添加类型标签。

请严格按照以下JSON格式返回结果，不要有其他内容：
{"title_cn":"翻译后的标题","summary_cn":"翻译后的摘要","content_cn":"翻译后的内容","themes_cn":"主题词1,主题词2,主题词3"}`;
        return this.executeWithConcurrencyControl(async () => {
            var _a, _b;
            try {
                this.apiCallCount++;
                const startTime = Date.now();
                const finalProcessedText = this.preprocessContent(processedInput);
                const completion = await this.openai.chat.completions.create({
                    model: this.translationConfig.model,
                    messages: [
                        { role: 'system', content: combinedSystemPrompt },
                        { role: 'user', content: finalProcessedText }
                    ],
                    max_tokens: Math.max(this.translationConfig.maxTokens || 4000, 6000),
                    temperature: this.translationConfig.temperature,
                });
                const responseContent = ((_b = (_a = completion.choices[0]) === null || _a === void 0 ? void 0 : _a.message) === null || _b === void 0 ? void 0 : _b.content) || '';
                const elapsed = Date.now() - startTime;
                this.apiTotalTime += elapsed;
                this.logger.debug(`组合翻译API调用完成，耗时: ${elapsed}ms，总调用次数: ${this.apiCallCount}`);
                let result;
                try {
                    let cleanedResponse = responseContent.trim();
                    cleanedResponse = cleanedResponse.replace(/^```json\s*/i, '').replace(/\s*```$/i, '');
                    const jsonMatch = cleanedResponse.match(/\{[\s\S]*"title_cn"[\s\S]*\}/);
                    const jsonStr = jsonMatch ? jsonMatch[0] : cleanedResponse;
                    result = JSON.parse(jsonStr);
                    if (!result.title_cn && !result.summary_cn && !result.content_cn) {
                        throw new Error('响应中缺少翻译字段');
                    }
                    result = {
                        title_cn: result.title_cn || '',
                        summary_cn: result.summary_cn || '',
                        content_cn: result.content_cn || '',
                        themes_cn: result.themes_cn || ''
                    };
                }
                catch (parseError) {
                    this.logger.error(`JSON解析失败，原始响应: ${responseContent.substring(0, 200)}...`);
                    const lines = responseContent.split('\n');
                    result = {
                        title_cn: this.extractFieldFromResponse(lines, 'title_cn') || title,
                        summary_cn: this.extractFieldFromResponse(lines, 'summary_cn') || summary,
                        content_cn: this.extractFieldFromResponse(lines, 'content_cn') || content,
                        themes_cn: this.extractFieldFromResponse(lines, 'themes_cn') || ''
                    };
                    this.logger.warn(`使用备用解析方法，提取结果: ${JSON.stringify(result)}`);
                }
                this.translationCache.set(cacheKey, JSON.stringify(result));
                this.consecutiveFailures = 0;
                this.failureStats.successfulProcessing++;
                return result;
            }
            catch (error) {
                this.consecutiveFailures++;
                this.logger.error(`组合翻译失败 - 模型: ${this.translationConfig.model}, 重试次数: ${retryCount}, 错误: ${error.message}`);
                if (error.message.includes('inappropriate content') ||
                    error.message.includes('content_filter') ||
                    error.message.includes('content policy') ||
                    error.message.includes('safety') ||
                    error.message.includes('harmful')) {
                    this.failureStats.contentFilter++;
                    if (retryCount >= (this.translationConfig.maxRetries || 2)) {
                        this.logger.error(`组合翻译内容过滤错误超过最大重试次数(${this.translationConfig.maxRetries})，跳过该文档`);
                        throw new Error(`SKIP_DOCUMENT:组合翻译内容过滤错误超过最大重试次数 - ${error.message}`);
                    }
                    this.logger.warn(`使用智能预处理重试组合翻译，重试次数: ${retryCount + 1}`);
                    const preprocessingResult = this.intelligentPreprocessing(inputText);
                    const retryTitle = this.extractOriginalField(preprocessingResult.processedText, '标题');
                    const retrySummary = this.extractOriginalField(preprocessingResult.processedText, '摘要');
                    const retryContent = this.extractOriginalField(preprocessingResult.processedText, '内容');
                    return this.translateAndExtractInOneCall(retryTitle, retrySummary, retryContent, retryCount + 1);
                }
                if (error.message.includes('timed out') || error.message.includes('timeout')) {
                    this.failureStats.timeout++;
                    if (retryCount < (this.translationConfig.maxRetries || 2)) {
                        const waitTime = Math.pow(2, retryCount) * (this.translationConfig.retryDelay || 1000);
                        this.logger.warn(`组合翻译请求超时，${waitTime}ms后重试，重试次数: ${retryCount + 1}`);
                        await new Promise(resolve => setTimeout(resolve, waitTime));
                        return this.translateAndExtractInOneCall(title, summary, content, retryCount + 1);
                    }
                    else {
                        this.logger.error(`组合翻译超时错误超过最大重试次数，跳过该文档`);
                        throw new Error(`SKIP_DOCUMENT:组合翻译超时错误超过最大重试次数`);
                    }
                }
                if (error.message.includes('rate limit') || error.message.includes('429')) {
                    this.failureStats.rateLimit++;
                    if (retryCount < (this.translationConfig.maxRetries || 2)) {
                        const waitTime = (retryCount + 1) * 5000;
                        this.logger.warn(`组合翻译API限制错误，${waitTime}ms后重试，重试次数: ${retryCount + 1}`);
                        await new Promise(resolve => setTimeout(resolve, waitTime));
                        return this.translateAndExtractInOneCall(title, summary, content, retryCount + 1);
                    }
                    else {
                        this.logger.error(`组合翻译API限制错误超过最大重试次数，跳过该文档`);
                        throw new Error(`SKIP_DOCUMENT:组合翻译API限制错误超过最大重试次数`);
                    }
                }
                if (error.message.includes('network') ||
                    error.message.includes('connection') ||
                    error.message.includes('ENOTFOUND') ||
                    error.message.includes('ECONNRESET') ||
                    error.message.includes('socket')) {
                    this.failureStats.networkError++;
                }
                else {
                    this.failureStats.other++;
                }
                this.logger.error(`组合翻译失败: ${error.message}`, error.stack);
                throw new Error(`组合翻译失败: ${error.message}`);
            }
        });
    }
    extractFieldFromResponse(lines, fieldName) {
        for (const line of lines) {
            if (line.includes(`"${fieldName}"`)) {
                const match = line.match(new RegExp(`"${fieldName}"\\s*:\\s*"([^"]*)"`, 'i'));
                if (match) {
                    return match[1];
                }
            }
        }
        return '';
    }
    extractOriginalField(processedText, fieldLabel) {
        const regex = new RegExp(`${fieldLabel}：([\\s\\S]*?)(?=\\n\\n|$)`, 'i');
        const match = processedText.match(regex);
        return match ? match[1].trim() : '';
    }
    analyzeSensitiveContent(text) {
        const sensitivePatterns = {
            security: /\b(terrorism|terrorist|bomb|explosive|attack|kill|weapon|suicide|violent|war|conflict|military|missile|nuclear|assassination|threat|hostage|extremist|radical|jihad|massacre|combat|battlefield|warfare|fighter|bomber|destroyer|battleship|tank|artillery|grenade|rifle|pistol|ammunition|soldier|troops|combat|invasion|occupation|siege|raid|assault|warfare|defense|offensive|strike|launch|target|enemy|hostile|aggression|counterattack)\b/gi,
            drugs: /\b(drug|cocaine|heroin|marijuana|cannabis|meth|amphetamine|opium|narcotic|substance abuse|overdose|smuggling|trafficking|dealer|addict|rehabilitation|withdrawal)\b/gi,
            adult: /\b(sex|porn|nude|naked|adult|explicit|xxx|erotic|obscene|prostitute|brothel|strip|sexual|intimacy|seduction)\b/gi,
            political: /\b(genocide|torture|dictatorship|oppression|censorship|propaganda|corruption|riot|protest|revolution|rebellion|uprising|coup|regime|authoritarian|totalitarian|persecution|discrimination|violation|human rights|freedom|democracy|election fraud|suppression)\b/gi,
            intelligence: /\b(missile|nuclear|classified|confidential|secret|intelligence|spy|surveillance|hack|breach|leak|sensitive|restricted|clearance|covert|operation|agent|agency|infiltrate|sabotage|conspiracy|whistleblower|espionage|reconnaissance|undercover|mole|double agent|black ops|cyber attack|data breach|national security|top secret|state secrets)\b/gi,
            violence: /\b(death|murder|torture|abuse|violence|blood|injury|harm|damage|destroy|destruction|killing|slaughter|execution|brutality|cruelty|savage|vicious|ruthless|merciless)\b/gi,
            cybersecurity: /\b(hacker|hacking|malware|virus|trojan|phishing|ransomware|ddos|cyber|breach|vulnerability|exploit|penetration|backdoor|rootkit|keylogger|botnet|dark web|anonymous|zero day)\b/gi,
            terrorism_extended: /\b(isis|al.?qaeda|taliban|suicide bomber|car bomb|ied|improvised explosive|terror attack|mass shooting|school shooting|public attack|lone wolf|radicalization|indoctrination)\b/gi,
            military_tech: /\b(drone|uav|guided missile|ballistic|intercontinental|submarine|aircraft carrier|stealth|radar|sonar|satellite surveillance|gps jamming|electronic warfare|signal intelligence)\b/gi
        };
        const sensitiveWords = [];
        const categories = [];
        let totalMatches = 0;
        Object.entries(sensitivePatterns).forEach(([category, pattern]) => {
            const matches = text.match(pattern);
            if (matches) {
                sensitiveWords.push(...matches);
                categories.push(category);
                totalMatches += matches.length;
            }
        });
        let riskLevel = 'low';
        if (totalMatches > 15 ||
            categories.includes('terrorism_extended') ||
            categories.includes('cybersecurity') ||
            (categories.includes('security') && categories.includes('intelligence')) ||
            (categories.includes('violence') && totalMatches > 8)) {
            riskLevel = 'high';
        }
        else if (totalMatches > 8 ||
            categories.length > 3 ||
            categories.includes('security') ||
            categories.includes('intelligence') ||
            categories.includes('military_tech')) {
            riskLevel = 'medium';
        }
        return {
            hasSensitiveContent: sensitiveWords.length > 0,
            sensitiveWords: [...new Set(sensitiveWords)],
            categories: [...new Set(categories)],
            riskLevel
        };
    }
    intelligentPreprocessing(text, title) {
        const analysis = this.analyzeSensitiveContent(text);
        const titleAnalysis = title ? this.analyzeSensitiveContent(title) : null;
        let processedText = text;
        let strategy = 'none';
        let needsManualReview = false;
        const isNewsContent = /\b(报道|新闻|发布|宣布|消息|据.*报告|according to|reported|announced|news|press|media)\b/i.test(text + (title || ''));
        const isTechnicalContent = /\b(技术|科学|研究|实验|数据|algorithm|technology|science|research|satellite|space|orbit)\b/i.test(text + (title || ''));
        if (analysis.riskLevel === 'high') {
            if (isNewsContent || isTechnicalContent) {
                processedText = this.applyContextualPreprocessing(text, 'news_technical');
                strategy = 'contextual_news_technical';
            }
            else {
                processedText = this.applyStricterPreprocessing(text);
                strategy = 'strict';
                needsManualReview = true;
            }
        }
        else if (analysis.riskLevel === 'medium') {
            processedText = this.preprocessContent(text);
            strategy = 'basic';
        }
        else if (analysis.hasSensitiveContent) {
            processedText = this.applyLightPreprocessing(text);
            strategy = 'light';
        }
        return {
            processedText,
            strategy,
            analysis: {
                original: analysis,
                title: titleAnalysis,
                isNewsContent,
                isTechnicalContent
            },
            needsManualReview
        };
    }
    applyContextualPreprocessing(text, contentType) {
        let processedText = text;
        if (contentType === 'news_technical') {
            processedText = processedText
                .replace(/\b(kill|murder|destroy|attack|violence)\b/gi, '[ACTION]')
                .replace(/\b(threat|danger|risk|hazard)\b/gi, '[CONCERN]')
                .replace(/\b(classified|secret|confidential)(?!\s+(information|data|technology))/gi, '[RESTRICTED]')
                .replace(/\b(terrorism|terrorist|extremist|radical)\b/gi, '[GROUP]');
        }
        else {
            processedText = this.preprocessContent(text);
        }
        return processedText.trim();
    }
    applyLightPreprocessing(text) {
        return text
            .replace(/\b(terrorism|terrorist|bomb|explosive|kill|murder|suicide)\b/gi, '[REDACTED]')
            .replace(/\b(porn|nude|explicit|xxx|obscene)\b/gi, '[FILTERED]')
            .replace(/https?:\/\/\S+/g, '[URL]')
            .replace(/\S+@\S+\.\S+/g, '[EMAIL]')
            .trim();
    }
};
TranslationService = TranslationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], TranslationService);
exports.TranslationService = TranslationService;
//# sourceMappingURL=translation.service.js.map