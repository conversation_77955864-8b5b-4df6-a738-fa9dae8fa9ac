"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchNewsService = void 0;
const common_1 = require("@nestjs/common");
const elasticsearch_base_service_1 = require("./elasticsearch.base.service");
const elasticsearch_1 = require("@nestjs/elasticsearch");
const translation_service_1 = require("./translation.service");
const news_list_query_dto_1 = require("../dto/news-list-query.dto");
const cheerio = require('cheerio');
let ElasticsearchNewsService = class ElasticsearchNewsService extends elasticsearch_base_service_1.ElasticsearchBaseService {
    constructor(elasticsearchService, translationService) {
        super(elasticsearchService);
        this.elasticsearchService = elasticsearchService;
        this.translationService = translationService;
        this.lastProcessedTimes = new Map();
        this.failedDocuments = [];
        this.maxFailedDocuments = 1000;
    }
    async getNewsIndices() {
        try {
            const response = await this.elasticsearchService.cat.indices({ format: 'json' });
            const allIndices = response.map(index => index.index).filter(Boolean);
            const newsIndices = allIndices.filter(index => typeof index === 'string' && index.startsWith('news_'));
            this.logger.log(`找到${newsIndices.length}个新闻索引`);
            return newsIndices;
        }
        catch (error) {
            this.logger.error(`获取新闻索引失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getUntranslatedNews(index, batchSize = 10, forceRetranslate = false) {
        var _a;
        try {
            this.logger.log(`获取未翻译的新闻，索引=${index}, 批次大小=${batchSize}, 强制重新翻译=${forceRetranslate}`);
            let query;
            if (forceRetranslate) {
                this.logger.debug(`强制重新翻译模式，获取所有文档`);
                query = {
                    match_all: {}
                };
            }
            else {
                this.logger.debug(`标准模式，获取未翻译的文档`);
                query = {
                    bool: {
                        should: [
                            { bool: { must_not: { exists: { field: "title_cn" } } } },
                            { bool: { must: { match: { title_cn: "" } } } },
                            { bool: { must_not: { exists: { field: "summary_cn" } } } },
                            { bool: { must: { match: { summary_cn: "" } } } },
                            { bool: { must_not: { exists: { field: "content_cn" } } } },
                            { bool: { must: { match: { content_cn: "" } } } },
                            {
                                bool: {
                                    must: [
                                        { exists: { field: "info_source" } },
                                        {
                                            bool: {
                                                should: [
                                                    { bool: { must_not: { exists: { field: "content" } } } },
                                                    { match: { content: "" } }
                                                ],
                                                minimum_should_match: 1
                                            }
                                        }
                                    ]
                                }
                            }
                        ],
                        minimum_should_match: 1
                    }
                };
            }
            const response = await this.elasticsearchService.search({
                index,
                body: {
                    query,
                    size: batchSize
                }
            });
            const hits = ((_a = response.hits) === null || _a === void 0 ? void 0 : _a.hits) || [];
            const documents = hits.map(hit => (Object.assign({ _id: hit._id, _index: hit._index }, hit._source)));
            this.logger.log(`从索引${index}获取了${documents.length}条未翻译的新闻`);
            if (documents.length > 0) {
                this.logger.debug(`第一个文档ID: ${documents[0]._id}`);
            }
            return documents;
        }
        catch (error) {
            this.logger.error(`查询未翻译新闻失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async updateNewsWithTranslation(index, id, translations) {
        try {
            await this.elasticsearchService.update({
                index,
                id,
                body: {
                    doc: translations
                }
            });
            return true;
        }
        catch (error) {
            this.logger.error(`更新新闻文档翻译失败, index=${index}, id=${id}: ${error.message}`, error.stack);
            return false;
        }
    }
    async updateNewsWithThemes(index, id, themes) {
        try {
            await this.elasticsearchService.update({
                index,
                id,
                body: {
                    doc: {
                        themes_cn: themes
                    }
                }
            });
            return true;
        }
        catch (error) {
            this.logger.error(`更新新闻文档主题词失败, index=${index}, id=${id}: ${error.message}`, error.stack);
            return false;
        }
    }
    async getNewsForThemeExtraction(index, batchSize = 10, forceReextract = false) {
        var _a;
        try {
            let query;
            if (forceReextract) {
                query = {
                    bool: {
                        should: [
                            {
                                bool: {
                                    must: [
                                        { exists: { field: "title_cn" } },
                                        { exists: { field: "content_cn" } }
                                    ]
                                }
                            },
                            {
                                bool: {
                                    must: [
                                        { exists: { field: "title" } },
                                        { exists: { field: "content" } }
                                    ],
                                    must_not: [
                                        { exists: { field: "title_cn" } }
                                    ]
                                }
                            }
                        ],
                        minimum_should_match: 1
                    }
                };
            }
            else {
                query = {
                    bool: {
                        should: [
                            {
                                bool: {
                                    must: [
                                        { exists: { field: "title_cn" } },
                                        { exists: { field: "content_cn" } }
                                    ],
                                    must_not: [
                                        { exists: { field: "themes_cn" } }
                                    ]
                                }
                            },
                            {
                                bool: {
                                    must: [
                                        { exists: { field: "title" } },
                                        { exists: { field: "content" } }
                                    ],
                                    must_not: [
                                        { exists: { field: "title_cn" } },
                                        { exists: { field: "themes_cn" } }
                                    ]
                                }
                            }
                        ],
                        minimum_should_match: 1
                    }
                };
            }
            const response = await this.elasticsearchService.search({
                index,
                body: {
                    query,
                    size: batchSize
                }
            });
            const hits = ((_a = response.hits) === null || _a === void 0 ? void 0 : _a.hits) || [];
            const documents = hits.map(hit => (Object.assign({ _id: hit._id, _index: hit._index }, hit._source)));
            this.logger.debug(`从索引${index}获取了${documents.length}条需要提取主题词的新闻（包括英文文档）`);
            return documents;
        }
        catch (error) {
            this.logger.error(`查询需要提取主题词的新闻失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async translateNews(params) {
        const { batchSize = 10, maxDocs = 0, forceRetranslate = false, forceRefetchContent = false, specificIndexes, llmMode, customModel, autoExtractThemes = true } = params;
        if (llmMode || customModel) {
            const configUpdates = {};
            if (customModel) {
                configUpdates.model = customModel;
                this.logger.log(`使用自定义模型: ${customModel}`);
            }
            else if (llmMode) {
                switch (llmMode) {
                    case 'high_quality':
                        configUpdates.model = 'qwen-max-latest';
                        configUpdates.temperature = 0.05;
                        configUpdates.maxConcurrentRequests = 2;
                        break;
                    case 'fast':
                        configUpdates.model = 'qwen-turbo';
                        configUpdates.temperature = 0.2;
                        configUpdates.maxConcurrentRequests = 5;
                        break;
                    default:
                        configUpdates.model = 'qwen-turbo';
                        configUpdates.temperature = 0.1;
                        configUpdates.maxConcurrentRequests = 3;
                        break;
                }
                this.logger.log(`使用模式: ${llmMode}, 模型: ${configUpdates.model}`);
            }
            this.translationService.updateTranslationConfig(configUpdates);
        }
        let statistics = {
            total: 0,
            success: 0,
            failed: 0,
            skipped: 0,
            indexCount: 0,
            indexes: [],
            startTime: new Date(),
            themeExtraction: autoExtractThemes ? {
                total: 0,
                success: 0,
                failed: 0,
                skipped: 0
            } : undefined
        };
        try {
            let newsIndices = await this.getNewsIndices();
            if (specificIndexes && specificIndexes.length > 0) {
                newsIndices = newsIndices.filter(index => specificIndexes.includes(index) ||
                    specificIndexes.some(pattern => {
                        if (pattern.includes('*')) {
                            const regexPattern = pattern.replace(/\*/g, '.*');
                            return new RegExp(`^${regexPattern}$`).test(index);
                        }
                        return index === pattern;
                    }));
            }
            statistics.indexCount = newsIndices.length;
            statistics.indexes = newsIndices;
            this.logger.log(`开始翻译${newsIndices.length}个新闻索引的数据，批次大小: ${batchSize}, 强制重翻译: ${forceRetranslate}, 强制重新爬取: ${forceRefetchContent}, 自动提取主题词: ${autoExtractThemes}`);
            for (const index of newsIndices) {
                let processedCount = 0;
                let hasMore = true;
                while (hasMore) {
                    const newsDocuments = await this.getUntranslatedNews(index, batchSize, forceRetranslate);
                    if (newsDocuments.length === 0) {
                        hasMore = false;
                        continue;
                    }
                    statistics.total += newsDocuments.length;
                    for (const doc of newsDocuments) {
                        try {
                            if (maxDocs > 0 && processedCount >= maxDocs) {
                                hasMore = false;
                                break;
                            }
                            processedCount++;
                            let content = doc.content || '';
                            let contentUpdated = false;
                            const shouldFetchContent = (!content || content.trim() === '') && doc.info_source &&
                                (forceRefetchContent || !doc.content_fetched);
                            if (shouldFetchContent) {
                                try {
                                    this.logger.log(`文档${doc._id}${forceRefetchContent ? '强制重新' : ''}尝试从${doc.info_source}获取内容`);
                                    const fetchedContent = await this.fetchContentFromUrl(doc.info_source);
                                    const fetchUpdateData = {
                                        content_fetched: true,
                                        content_fetch_time: new Date().toISOString(),
                                        content_fetch_success: false
                                    };
                                    if (fetchedContent && fetchedContent.trim() !== '') {
                                        content = fetchedContent;
                                        contentUpdated = true;
                                        fetchUpdateData.content = fetchedContent;
                                        fetchUpdateData.content_fetch_success = true;
                                        this.logger.log(`成功从${doc.info_source}获取内容并更新文档${doc._id}，内容长度: ${fetchedContent.length}`);
                                    }
                                    else {
                                        this.logger.warn(`从${doc.info_source}获取内容失败或内容为空，已标记为已爬取`);
                                    }
                                    await this.elasticsearchService.update({
                                        index: doc._index || index,
                                        id: doc._id || '',
                                        body: {
                                            doc: fetchUpdateData
                                        }
                                    });
                                }
                                catch (fetchError) {
                                    this.logger.error(`从${doc.info_source}获取内容失败: ${fetchError.message}`);
                                    try {
                                        await this.elasticsearchService.update({
                                            index: doc._index || index,
                                            id: doc._id || '',
                                            body: {
                                                doc: {
                                                    content_fetched: true,
                                                    content_fetch_time: new Date().toISOString(),
                                                    content_fetch_success: false
                                                }
                                            }
                                        });
                                    }
                                    catch (updateError) {
                                        this.logger.error(`更新爬取标记失败: ${updateError.message}`);
                                    }
                                }
                            }
                            else if (doc.content_fetched && !forceRefetchContent) {
                                this.logger.debug(`文档${doc._id}已标记为爬取过，跳过内容爬取`);
                            }
                            if (autoExtractThemes && statistics.themeExtraction) {
                                try {
                                    if (contentUpdated) {
                                        this.logger.debug(`使用从 URL 获取的内容进行组合翻译，内容长度: ${(content === null || content === void 0 ? void 0 : content.length) || 0}`);
                                    }
                                    const combinedResult = await this.translationService.translateAndExtractInOneCall(doc.title || '', doc.summary || '', content || '');
                                    const updateData = {
                                        title_cn: combinedResult.title_cn,
                                        summary_cn: combinedResult.summary_cn,
                                        content_cn: combinedResult.content_cn
                                    };
                                    if (combinedResult.themes_cn && combinedResult.themes_cn.trim() !== '') {
                                        updateData.themes_cn = combinedResult.themes_cn;
                                    }
                                    const success = await this.elasticsearchService.update({
                                        index,
                                        id: doc._id || '',
                                        body: {
                                            doc: updateData
                                        }
                                    });
                                    if (success) {
                                        statistics.success++;
                                        this.logger.debug(`成功完成组合翻译: ${index}/${doc._id}`);
                                        statistics.themeExtraction.total++;
                                        if (combinedResult.themes_cn && combinedResult.themes_cn.trim() !== '') {
                                            statistics.themeExtraction.success++;
                                            this.logger.debug(`成功提取文档主题词: ${index}/${doc._id}, 主题词: ${combinedResult.themes_cn}`);
                                        }
                                        else {
                                            statistics.themeExtraction.skipped++;
                                            this.logger.warn(`组合翻译中主题提取返回空结果: ${index}/${doc._id}`);
                                        }
                                    }
                                    else {
                                        statistics.failed++;
                                        this.logger.warn(`更新组合翻译结果失败: ${index}/${doc._id}`);
                                    }
                                }
                                catch (combinedError) {
                                    this.logger.error(`组合翻译失败: ${index}/${doc._id}: ${combinedError.message}`);
                                    if (combinedError.message.includes('SKIP_DOCUMENT')) {
                                        statistics.failed++;
                                        this.logger.warn(`跳过文档（内容不适当）: ${index}/${doc._id}`);
                                        continue;
                                    }
                                    try {
                                        this.logger.warn(`组合翻译失败，回退到分别翻译: ${index}/${doc._id}`);
                                        const titleCn = await this.translationService.translateText(doc.title || '');
                                        const summaryCn = await this.translationService.translateText(doc.summary || '');
                                        const contentCn = await this.translationService.translateText(content || '');
                                        const success = await this.updateNewsWithTranslation(index, doc._id || '', { title_cn: titleCn, summary_cn: summaryCn, content_cn: contentCn });
                                        if (success) {
                                            statistics.success++;
                                            this.logger.debug(`回退方式翻译成功: ${index}/${doc._id}`);
                                            try {
                                                statistics.themeExtraction.total++;
                                                const themes = await this.translationService.extractThemes(titleCn, contentCn);
                                                if (themes && themes.trim() !== '') {
                                                    const themeSuccess = await this.updateNewsWithThemes(index, doc._id || '', themes);
                                                    if (themeSuccess) {
                                                        statistics.themeExtraction.success++;
                                                        this.logger.debug(`回退方式主题提取成功: ${index}/${doc._id}, 主题词: ${themes}`);
                                                    }
                                                    else {
                                                        statistics.themeExtraction.failed++;
                                                        this.logger.warn(`回退方式更新主题词失败: ${index}/${doc._id}`);
                                                    }
                                                }
                                                else {
                                                    statistics.themeExtraction.skipped++;
                                                    this.logger.warn(`回退方式主题提取返回空结果: ${index}/${doc._id}`);
                                                }
                                            }
                                            catch (themeError) {
                                                statistics.themeExtraction.failed++;
                                                this.logger.error(`回退方式主题提取失败: ${index}/${doc._id}: ${themeError.message}`);
                                            }
                                        }
                                        else {
                                            statistics.failed++;
                                            this.logger.warn(`回退方式翻译更新失败: ${index}/${doc._id}`);
                                        }
                                    }
                                    catch (fallbackError) {
                                        throw combinedError;
                                    }
                                }
                            }
                            else {
                                const titleCn = await this.translationService.translateText(doc.title || '');
                                const summaryCn = await this.translationService.translateText(doc.summary || '');
                                if (contentUpdated) {
                                    this.logger.debug(`使用从 URL 获取的内容进行翻译，内容长度: ${(content === null || content === void 0 ? void 0 : content.length) || 0}`);
                                }
                                const contentCn = await this.translationService.translateText(content || '');
                                const success = await this.updateNewsWithTranslation(index, doc._id || '', { title_cn: titleCn, summary_cn: summaryCn, content_cn: contentCn });
                                if (success) {
                                    statistics.success++;
                                    this.logger.debug(`成功翻译文档: ${index}/${doc._id}`);
                                }
                                else {
                                    statistics.failed++;
                                    this.logger.warn(`更新文档失败: ${index}/${doc._id}`);
                                }
                            }
                            await new Promise(resolve => setTimeout(resolve, 100));
                        }
                        catch (error) {
                            statistics.failed++;
                            this.logger.error(`处理文档失败: ${index}/${doc._id}: ${error.message}`, error.stack);
                        }
                    }
                    if (newsDocuments.length < batchSize) {
                        hasMore = false;
                    }
                }
            }
            statistics.endTime = new Date();
            statistics.elapsedTime = statistics.endTime.getTime() - statistics.startTime.getTime();
            this.logger.log(`翻译任务完成，总计处理: ${statistics.total}, 成功: ${statistics.success}, 失败: ${statistics.failed}, 跳过: ${statistics.skipped}, 耗时: ${statistics.elapsedTime}ms`);
            if (statistics.themeExtraction) {
                this.logger.log(`主题提取统计 - 总计: ${statistics.themeExtraction.total}, 成功: ${statistics.themeExtraction.success}, 失败: ${statistics.themeExtraction.failed}, 跳过: ${statistics.themeExtraction.skipped}`);
            }
            return statistics;
        }
        catch (error) {
            this.logger.error(`翻译任务失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getTranslationStatus() {
        const result = [];
        const newsIndices = await this.getNewsIndices();
        for (const index of newsIndices) {
            try {
                const totalResponse = await this.elasticsearchService.count({
                    index
                });
                const translatedResponse = await this.elasticsearchService.count({
                    index,
                    body: {
                        query: {
                            bool: {
                                must: [
                                    { exists: { field: "title_cn" } },
                                    { exists: { field: "summary_cn" } },
                                    { exists: { field: "content_cn" } }
                                ]
                            }
                        }
                    }
                });
                const totalDocs = totalResponse.count;
                const translatedDocs = translatedResponse.count;
                const untranslatedDocs = totalDocs - translatedDocs;
                const lastProcessedTime = this.lastProcessedTimes.get(index);
                result.push({
                    index,
                    totalDocs,
                    translatedDocs,
                    untranslatedDocs,
                    translationProgress: totalDocs > 0 ? (translatedDocs / totalDocs) * 100 : 0,
                    lastProcessedTime: lastProcessedTime ? lastProcessedTime.toISOString() : null
                });
            }
            catch (error) {
                this.logger.error(`获取索引${index}的翻译状态失败: ${error.message}`, error.stack);
                result.push({
                    index,
                    error: error.message
                });
            }
        }
        return result;
    }
    getAPIStats() {
        return this.translationService.getAPIStats();
    }
    getFailureStats() {
        const stats = this.translationService.getFailureStats();
        const failedDocs = this.getFailedTranslations();
        return Object.assign(Object.assign({}, stats), { failedDocuments: failedDocs });
    }
    resetFailureStats() {
        this.translationService.resetFailureStats();
        this.failedDocuments = [];
        this.logger.log('失败统计和失败文档列表已重置');
    }
    async getHotThemes(params) {
        var _a;
        const { topN = 10, minCount = 1, specificIndexes } = params;
        try {
            let newsIndices = await this.getNewsIndices();
            if (specificIndexes && specificIndexes.length > 0) {
                newsIndices = newsIndices.filter(index => specificIndexes.includes(index) ||
                    specificIndexes.some(pattern => {
                        if (pattern.includes('*')) {
                            const regexPattern = pattern.replace(/\*/g, '.*');
                            return new RegExp(`^${regexPattern}$`).test(index);
                        }
                        return index === pattern;
                    }));
            }
            this.logger.log(`开始统计${newsIndices.length}个新闻索引的热门主题词`);
            const themeCountMap = new Map();
            let processedDocs = 0;
            for (const index of newsIndices) {
                let from = 0;
                const size = 1000;
                let hasMore = true;
                while (hasMore) {
                    const response = await this.elasticsearchService.search({
                        index,
                        body: {
                            query: {
                                exists: { field: "themes_cn" }
                            },
                            _source: ["themes_cn"],
                            from,
                            size
                        }
                    });
                    const hits = ((_a = response.hits) === null || _a === void 0 ? void 0 : _a.hits) || [];
                    if (hits.length === 0) {
                        hasMore = false;
                        continue;
                    }
                    for (const hit of hits) {
                        const source = hit._source;
                        if (source && source.themes_cn) {
                            const themes = source.themes_cn.split(/[,，]/).map((theme) => theme.trim()).filter(Boolean);
                            for (const theme of themes) {
                                const count = themeCountMap.get(theme) || 0;
                                themeCountMap.set(theme, count + 1);
                            }
                        }
                    }
                    processedDocs += hits.length;
                    from += size;
                    if (hits.length < size) {
                        hasMore = false;
                    }
                }
                this.logger.debug(`索引${index}处理完成}`);
            }
            const filteredThemes = Array.from(themeCountMap.entries())
                .filter(([_, count]) => count >= minCount)
                .map(([theme, count]) => ({ theme, count }));
            const sortedThemes = filteredThemes.sort((a, b) => b.count - a.count);
            const topThemes = sortedThemes.slice(0, topN);
            this.logger.log(`热门主题词统计完成，共处理${processedDocs}条文档，找到${filteredThemes.length}个主题词，返回前${topThemes.length}个`);
            return {
                themes: topThemes,
                total: filteredThemes.length,
                indexCount: newsIndices.length,
                indexes: newsIndices,
                processedDocs
            };
        }
        catch (error) {
            this.logger.error(`获取热门主题词失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async extractNewsThemes(params) {
        const { batchSize = 10, maxDocs = 0, forceReextract = false, specificIndexes, llmMode, customModel } = params;
        if (llmMode || customModel) {
            const configUpdates = {};
            if (customModel) {
                configUpdates.model = customModel;
                this.logger.log(`主题提取使用自定义模型: ${customModel}`);
            }
            else if (llmMode) {
                switch (llmMode) {
                    case 'high_quality':
                        configUpdates.model = 'qwen-max-latest';
                        configUpdates.temperature = 0.05;
                        configUpdates.maxConcurrentRequests = 2;
                        break;
                    case 'fast':
                        configUpdates.model = 'qwen-turbo';
                        configUpdates.temperature = 0.2;
                        configUpdates.maxConcurrentRequests = 5;
                        break;
                    default:
                        configUpdates.model = 'qwen-turbo';
                        configUpdates.temperature = 0.1;
                        configUpdates.maxConcurrentRequests = 3;
                        break;
                }
                this.logger.log(`主题提取使用模式: ${llmMode}, 模型: ${configUpdates.model}`);
            }
            this.translationService.updateThemeExtractionConfig(configUpdates);
        }
        let statistics = {
            total: 0,
            success: 0,
            failed: 0,
            skipped: 0,
            indexCount: 0,
            indexes: [],
            startTime: new Date()
        };
        try {
            let newsIndices = await this.getNewsIndices();
            if (specificIndexes && specificIndexes.length > 0) {
                newsIndices = newsIndices.filter(index => specificIndexes.includes(index) ||
                    specificIndexes.some(pattern => {
                        if (pattern.includes('*')) {
                            const regexPattern = pattern.replace(/\*/g, '.*');
                            return new RegExp(`^${regexPattern}$`).test(index);
                        }
                        return index === pattern;
                    }));
            }
            statistics.indexCount = newsIndices.length;
            statistics.indexes = newsIndices;
            this.logger.log(`开始提取${newsIndices.length}个新闻索引的主题词，批次大小: ${batchSize}, 强制重新提取: ${forceReextract}`);
            for (const index of newsIndices) {
                let processedCount = 0;
                let hasMore = true;
                while (hasMore) {
                    const newsDocuments = await this.getNewsForThemeExtraction(index, batchSize, forceReextract);
                    if (newsDocuments.length === 0) {
                        hasMore = false;
                        continue;
                    }
                    statistics.total += newsDocuments.length;
                    for (const doc of newsDocuments) {
                        try {
                            if (maxDocs > 0 && processedCount >= maxDocs) {
                                hasMore = false;
                                break;
                            }
                            processedCount++;
                            let titleForExtraction = '';
                            let contentForExtraction = '';
                            if (doc.title_cn && doc.title_cn.trim() !== '') {
                                titleForExtraction = doc.title_cn;
                            }
                            else if (doc.title && doc.title.trim() !== '') {
                                titleForExtraction = doc.title;
                            }
                            if (doc.content_cn && doc.content_cn.trim() !== '') {
                                contentForExtraction = doc.content_cn;
                            }
                            else if (doc.content && doc.content.trim() !== '') {
                                contentForExtraction = doc.content;
                            }
                            if (!titleForExtraction && !contentForExtraction) {
                                statistics.skipped++;
                                this.logger.warn(`跳过文档（无有效内容）: ${index}/${doc._id}`);
                                continue;
                            }
                            const themes = await this.translationService.extractThemes(titleForExtraction, contentForExtraction);
                            const success = await this.updateNewsWithThemes(index, doc._id || '', themes);
                            if (success) {
                                statistics.success++;
                                this.logger.debug(`成功提取文档主题词: ${index}/${doc._id}, 主题词: ${themes}`);
                            }
                            else {
                                statistics.failed++;
                                this.logger.warn(`更新文档主题词失败: ${index}/${doc._id}`);
                            }
                            await new Promise(resolve => setTimeout(resolve, 100));
                        }
                        catch (error) {
                            if (error.message.startsWith('SKIP_DOCUMENT:')) {
                                statistics.skipped++;
                                this.logger.warn(`跳过文档: ${index}/${doc._id}: ${error.message}`);
                                const skippedDoc = {
                                    index,
                                    id: doc._id || '',
                                    reason: error.message,
                                    timestamp: new Date(),
                                    skipped: true
                                };
                                if (!statistics.skippedDocuments) {
                                    statistics.skippedDocuments = [];
                                }
                                statistics.skippedDocuments.push(skippedDoc);
                                await new Promise(resolve => setTimeout(resolve, 500));
                                continue;
                            }
                            statistics.failed++;
                            const failedDoc = {
                                index,
                                id: doc._id || '',
                                reason: error.message,
                                timestamp: new Date()
                            };
                            this.addFailedDocument(failedDoc);
                            if (!statistics.failedDocuments) {
                                statistics.failedDocuments = [];
                            }
                            statistics.failedDocuments.push(failedDoc);
                            this.logger.error(`处理文档时出错: ${index}/${doc._id}: ${error.message}`, error.stack);
                            if (error.message.includes('熔断器已触发')) {
                                this.logger.warn(`检测到熔断器触发，暂停处理并等待重置`);
                                await new Promise(resolve => setTimeout(resolve, 5 * 60 * 1000));
                                continue;
                            }
                            if (error.message.includes('timed out') || error.message.includes('rate limit')) {
                                this.logger.warn(`检测到API限制或超时，暂停30秒后继续`);
                                await new Promise(resolve => setTimeout(resolve, 30000));
                            }
                        }
                    }
                    if (newsDocuments.length < batchSize) {
                        hasMore = false;
                    }
                }
                this.logger.log(`索引${index}处理完成，总计处理${processedCount}条新闻`);
                this.lastProcessedTimes.set(index, new Date());
            }
            statistics.endTime = new Date();
            statistics.elapsedTime = statistics.endTime.getTime() - statistics.startTime.getTime();
            this.logger.log(`新闻主题提取任务完成，总耗时: ${statistics.elapsedTime}ms, 成功: ${statistics.success}, 失败: ${statistics.failed}`);
            return statistics;
        }
        catch (error) {
            this.logger.error(`提取新闻主题词任务失败: ${error.message}`, error.stack);
            statistics.endTime = new Date();
            statistics.elapsedTime = statistics.endTime.getTime() - statistics.startTime.getTime();
            throw error;
        }
    }
    addFailedDocument(doc) {
        this.failedDocuments.push(doc);
        if (this.failedDocuments.length > this.maxFailedDocuments) {
            this.failedDocuments.shift();
        }
        this.logger.debug(`当前失败文档记录数: ${this.failedDocuments.length}`);
    }
    getFailedTranslations() {
        return [...this.failedDocuments];
    }
    async retryFailedTranslations(failedDocs, maxRetries = 3) {
        var _a;
        const docsToRetry = failedDocs || [...this.failedDocuments];
        if (docsToRetry.length === 0) {
            this.logger.log('没有需要重试的失败文档');
            return {
                total: 0,
                success: 0,
                failed: 0,
                skipped: 0,
                indexCount: 0,
                indexes: [],
                startTime: new Date(),
                endTime: new Date(),
                elapsedTime: 0
            };
        }
        this.logger.log(`开始重试${docsToRetry.length}个失败文档`);
        const statistics = {
            total: docsToRetry.length,
            success: 0,
            failed: 0,
            skipped: 0,
            indexCount: 0,
            indexes: [],
            startTime: new Date(),
            failedDocuments: []
        };
        const docsByIndex = new Map();
        for (const doc of docsToRetry) {
            if (!docsByIndex.has(doc.index)) {
                docsByIndex.set(doc.index, []);
                statistics.indexes.push(doc.index);
            }
            (_a = docsByIndex.get(doc.index)) === null || _a === void 0 ? void 0 : _a.push(doc);
        }
        statistics.indexCount = docsByIndex.size;
        for (const [index, docs] of docsByIndex.entries()) {
            this.logger.log(`处理索引${index}的${docs.length}个失败文档`);
            for (const doc of docs) {
                try {
                    const newsDoc = await this.getDocumentById(index, doc.id);
                    if (!newsDoc) {
                        this.logger.warn(`文档不存在或无法获取: ${index}/${doc.id}`);
                        statistics.skipped++;
                        continue;
                    }
                    const retryCount = (doc.retryCount || 0) + 1;
                    if (retryCount > maxRetries) {
                        this.logger.warn(`文档${index}/${doc.id}超过最大重试次数(${maxRetries})`);
                        statistics.skipped++;
                        continue;
                    }
                    const titleCn = await this.translationService.translateText(newsDoc.title || '');
                    const summaryCn = await this.translationService.translateText(newsDoc.summary || '');
                    const contentCn = await this.translationService.translateText(newsDoc.content || '');
                    const success = await this.updateNewsWithTranslation(index, doc.id, { title_cn: titleCn, summary_cn: summaryCn, content_cn: contentCn });
                    if (success) {
                        statistics.success++;
                        this.logger.debug(`成功重试翻译文档: ${index}/${doc.id}`);
                        this.removeFailedDocument(doc);
                    }
                    else {
                        statistics.failed++;
                        this.logger.warn(`重试更新文档失败: ${index}/${doc.id}`);
                        doc.retryCount = retryCount;
                        if (!statistics.failedDocuments) {
                            statistics.failedDocuments = [];
                        }
                        statistics.failedDocuments.push(doc);
                    }
                    await new Promise(resolve => setTimeout(resolve, 200));
                }
                catch (error) {
                    statistics.failed++;
                    this.logger.error(`重试处理文档时出错: ${index}/${doc.id}: ${error.message}`, error.stack);
                    doc.retryCount = (doc.retryCount || 0) + 1;
                    doc.reason = error.message;
                    doc.timestamp = new Date();
                    if (!statistics.failedDocuments) {
                        statistics.failedDocuments = [];
                    }
                    statistics.failedDocuments.push(doc);
                    if (error.message.includes('timed out') || error.message.includes('rate limit')) {
                        this.logger.warn(`检测到API限制或超时，暂停30秒后继续`);
                        await new Promise(resolve => setTimeout(resolve, 30000));
                    }
                }
            }
        }
        statistics.endTime = new Date();
        statistics.elapsedTime = statistics.endTime.getTime() - statistics.startTime.getTime();
        this.logger.log(`重试完成，总耗时: ${statistics.elapsedTime}ms, 成功: ${statistics.success}, 失败: ${statistics.failed}, 跳过: ${statistics.skipped}`);
        return statistics;
    }
    async getDocumentById(index, id) {
        try {
            const response = await this.elasticsearchService.get({
                index,
                id
            });
            if (response && response._source) {
                return Object.assign({ _id: response._id, _index: response._index }, response._source);
            }
            return null;
        }
        catch (error) {
            this.logger.error(`获取文档失败, index=${index}, id=${id}: ${error.message}`, error.stack);
            return null;
        }
    }
    removeFailedDocument(doc) {
        this.failedDocuments = this.failedDocuments.filter(item => !(item.index === doc.index && item.id === doc.id));
    }
    async fetchContentFromUrl(url) {
        try {
            if (!url || !url.startsWith('http')) {
                throw new Error('无效的URL');
            }
            this.logger.debug(`开始从URL获取内容: ${url}`);
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000);
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.5'
                    },
                    signal: controller.signal
                });
                clearTimeout(timeoutId);
                if (!response.ok) {
                    throw new Error(`请求失败，状态码: ${response.status}`);
                }
                const html = await response.text();
                let content = this.extractContentFromHtml(html);
                if (!content || content.trim() === '') {
                    this.logger.warn(`无法从 HTML 提取有意义的内容，返回原始 HTML`);
                    return html;
                }
                this.logger.debug(`成功从URL获取内容，长度: ${content.length}`);
                return content;
            }
            catch (error) {
                clearTimeout(timeoutId);
                throw error;
            }
        }
        catch (error) {
            this.logger.error(`从URL获取内容失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    extractContentFromHtml(html) {
        try {
            if (html.length > 800000) {
                html = html.slice(0, 800000);
            }
            const $ = cheerio.load(html, { decodeEntities: false });
            $('script, style, nav, header, footer, aside, iframe, noscript').remove();
            const adSelectors = [
                '[class*="advert" i]', '[id*="advert" i]',
                '[class*="ad-" i]', '[id*="ad-" i]',
                '[class*="ads" i]', '[id*="ads" i]',
                '[class*="sponsor" i]', '[id*="sponsor" i]'
            ];
            $(adSelectors.join(',')).remove();
            const candidates = [];
            $('article, main, section, div').each((_, el) => {
                const text = $(el).text().replace(/\s+/g, ' ').trim();
                if (text.length > 200) {
                    candidates.push({ element: el, text });
                }
            });
            let mainText = '';
            if (candidates.length > 0) {
                candidates.sort((a, b) => b.text.length - a.text.length);
                mainText = candidates[0].text;
            }
            if (!mainText || mainText.length < 100) {
                mainText = $.root().text().replace(/\s+/g, ' ').trim();
            }
            mainText = this.cleanAdLines(mainText);
            return mainText;
        }
        catch (error) {
            this.logger.error(`提取HTML内容失败: ${error.message}`, error.stack);
            return html;
        }
    }
    cleanAdLines(text) {
        const adLinePatterns = [
            /^(广告|推广|免责声明|Sponsored|Advertisement)[:：]/i,
            /更多精彩内容|点击(这里|链接)|版权所有|All rights reserved/i
        ];
        return text.split(/\n+/)
            .filter(line => !adLinePatterns.some(p => p.test(line.trim())))
            .join('\n')
            .trim();
    }
    async searchNewsList(query) {
        var _a;
        try {
            const { page = 1, limit = 10, keywords = [], themes = [], publishDateStart, publishDateEnd, indexPatterns = [], includeUntranslated = true, sort = { 'publish_date.year': 'desc' }, keywordMatchType = news_list_query_dto_1.KeywordMatchType.SUBSTRING } = query;
            const from = (page - 1) * limit;
            let newsIndices = await this.getNewsIndices();
            if (indexPatterns && indexPatterns.length > 0) {
                newsIndices = newsIndices.filter(index => indexPatterns.some(pattern => {
                    if (pattern.includes('*')) {
                        const regexPattern = pattern.replace(/\*/g, '.*');
                        return new RegExp(`^${regexPattern}$`).test(index);
                    }
                    return index === pattern;
                }));
            }
            if (newsIndices.length === 0) {
                this.logger.warn('没有找到符合条件的新闻索引');
                return {
                    data: [],
                    total: 0,
                    page,
                    size: limit,
                    totalPages: 0
                };
            }
            this.logger.debug(`搜索新闻列表，索引: ${newsIndices.join(', ')}`);
            const must = [];
            const should = [];
            const mustNot = [];
            if (keywords && keywords.length > 0) {
                const keywordQueries = keywords.map(keyword => {
                    this.logger.debug(`处理关键词: ${keyword}, 匹配方式: ${keywordMatchType}`);
                    if (keywordMatchType === news_list_query_dto_1.KeywordMatchType.SUBSTRING) {
                        return {
                            bool: {
                                should: [
                                    { match_phrase: { title: keyword } },
                                    { match_phrase: { title_cn: keyword } },
                                    { match_phrase: { summary: keyword } },
                                    { match_phrase: { summary_cn: keyword } },
                                    { match_phrase: { content: keyword } },
                                    { match_phrase: { content_cn: keyword } },
                                    { match_phrase: { source: keyword } },
                                    { match_phrase: { author: keyword } },
                                    { match_phrase: { info_source: keyword } },
                                    { match_phrase: { url: keyword } },
                                    { match_phrase: { themes_cn: keyword } },
                                    { match_phrase: { published_at: keyword } },
                                    { wildcard: { 'title.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                                    { wildcard: { 'title_cn.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                                    { wildcard: { 'summary.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                                    { wildcard: { 'summary_cn.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                                    { wildcard: { 'content.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                                    { wildcard: { 'content_cn.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                                    { wildcard: { 'source.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                                    { wildcard: { 'author.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                                    { wildcard: { 'info_source.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                                    { wildcard: { 'url.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                                    { wildcard: { 'themes_cn.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                                    { wildcard: { 'published_at.keyword': { value: `*${keyword}*`, case_insensitive: true } } }
                                ],
                                minimum_should_match: 1
                            }
                        };
                    }
                    else {
                        return {
                            multi_match: {
                                query: keyword,
                                fields: [
                                    'title', 'title_cn',
                                    'summary', 'summary_cn',
                                    'content', 'content_cn',
                                    'source', 'author',
                                    'info_source', 'url',
                                    'themes_cn', 'published_at'
                                ],
                                type: 'best_fields',
                                fuzziness: 'AUTO'
                            }
                        };
                    }
                });
                must.push(...keywordQueries);
                this.logger.debug(`构建的查询条件: ${JSON.stringify(must, null, 2)}`);
            }
            if (themes && themes.length > 0) {
                const themeQueries = themes.map(theme => ({
                    match_phrase: {
                        themes_cn: theme
                    }
                }));
                must.push(...themeQueries);
            }
            if (publishDateStart || publishDateEnd) {
                const dateQueries = [];
                if (publishDateStart) {
                    const startDate = new Date(publishDateStart);
                    const startYear = startDate.getFullYear();
                    const startMonth = startDate.getMonth() + 1;
                    const startDay = startDate.getDate();
                    dateQueries.push({
                        range: {
                            "publish_date.year": { gte: startYear }
                        }
                    });
                    dateQueries.push({
                        bool: {
                            should: [
                                { range: { "publish_date.year": { gt: startYear } } },
                                {
                                    bool: {
                                        must: [
                                            { match: { "publish_date.year": startYear } },
                                            { range: { "publish_date.month": { gte: startMonth } } }
                                        ]
                                    }
                                }
                            ]
                        }
                    });
                    dateQueries.push({
                        bool: {
                            should: [
                                { range: { "publish_date.year": { gt: startYear } } },
                                {
                                    bool: {
                                        must: [
                                            { match: { "publish_date.year": startYear } },
                                            { range: { "publish_date.month": { gt: startMonth } } }
                                        ]
                                    }
                                },
                                {
                                    bool: {
                                        must: [
                                            { match: { "publish_date.year": startYear } },
                                            { match: { "publish_date.month": startMonth } },
                                            { range: { "publish_date.day": { gte: startDay } } }
                                        ]
                                    }
                                }
                            ]
                        }
                    });
                }
                if (publishDateEnd) {
                    const endDate = new Date(publishDateEnd);
                    const endYear = endDate.getFullYear();
                    const endMonth = endDate.getMonth() + 1;
                    const endDay = endDate.getDate();
                    dateQueries.push({
                        range: {
                            "publish_date.year": { lte: endYear }
                        }
                    });
                    dateQueries.push({
                        bool: {
                            should: [
                                { range: { "publish_date.year": { lt: endYear } } },
                                {
                                    bool: {
                                        must: [
                                            { match: { "publish_date.year": endYear } },
                                            { range: { "publish_date.month": { lte: endMonth } } }
                                        ]
                                    }
                                }
                            ]
                        }
                    });
                    dateQueries.push({
                        bool: {
                            should: [
                                { range: { "publish_date.year": { lt: endYear } } },
                                {
                                    bool: {
                                        must: [
                                            { match: { "publish_date.year": endYear } },
                                            { range: { "publish_date.month": { lt: endMonth } } }
                                        ]
                                    }
                                },
                                {
                                    bool: {
                                        must: [
                                            { match: { "publish_date.year": endYear } },
                                            { match: { "publish_date.month": endMonth } },
                                            { range: { "publish_date.day": { lte: endDay } } }
                                        ]
                                    }
                                }
                            ]
                        }
                    });
                }
                must.push({
                    bool: {
                        must: dateQueries
                    }
                });
            }
            if (!includeUntranslated) {
                must.push({ exists: { field: 'title_cn' } }, { exists: { field: 'content_cn' } });
            }
            let finalQuery;
            if (must.length > 0 || should.length > 0 || mustNot.length > 0) {
                finalQuery = {
                    bool: {}
                };
                if (must.length > 0) {
                    finalQuery.bool.must = must;
                }
                if (should.length > 0) {
                    finalQuery.bool.should = should;
                    if (!finalQuery.bool.must) {
                        finalQuery.bool.minimum_should_match = 1;
                    }
                }
                if (mustNot.length > 0) {
                    finalQuery.bool.must_not = mustNot;
                }
            }
            else {
                finalQuery = { match_all: {} };
            }
            const sortConfig = [
                { "publish_date.year": { order: "desc" } },
                { "publish_date.month": { order: "desc" } },
                { "publish_date.day": { order: "desc" } }
            ];
            if (sort && Object.keys(sort).some(key => !key.startsWith('publish_date'))) {
                Object.entries(sort).forEach(([field, order]) => {
                    if (!field.startsWith('publish_date')) {
                        sortConfig.unshift({ [field]: { order } });
                    }
                });
            }
            const response = await this.elasticsearchService.search({
                index: newsIndices,
                body: {
                    query: finalQuery,
                    sort: sortConfig,
                    from,
                    size: limit
                }
            });
            const hits = ((_a = response.hits) === null || _a === void 0 ? void 0 : _a.hits) || [];
            const total = typeof response.hits.total === 'number'
                ? response.hits.total
                : response.hits.total.value || 0;
            const documents = hits.map(hit => {
                const source = hit._source || {};
                return Object.assign({ _id: hit._id, _index: hit._index }, source);
            });
            this.logger.debug(`新闻列表查询结果: 总数 ${total}, 当前页 ${page}, 每页数量 ${limit}, 返回文档数 ${documents.length}`);
            const totalPages = Math.ceil(total / limit);
            return {
                data: documents,
                total,
                page,
                size: limit,
                totalPages
            };
        }
        catch (error) {
            this.logger.error(`搜索新闻列表失败: ${error.message}`, error.stack);
            throw error;
        }
    }
};
ElasticsearchNewsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [elasticsearch_1.ElasticsearchService,
        translation_service_1.TranslationService])
], ElasticsearchNewsService);
exports.ElasticsearchNewsService = ElasticsearchNewsService;
//# sourceMappingURL=elasticsearch.news.service.js.map