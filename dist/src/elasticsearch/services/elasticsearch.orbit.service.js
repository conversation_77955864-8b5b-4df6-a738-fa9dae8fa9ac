"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var ElasticsearchOrbitService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchOrbitService = void 0;
const common_1 = require("@nestjs/common");
const elasticsearch_1 = require("@nestjs/elasticsearch");
const elasticsearch_base_service_1 = require("./elasticsearch.base.service");
const orbit_query_dto_1 = require("../dto/orbit-query.dto");
const tle_query_config_1 = require("../../../config/tle-query.config");
let ElasticsearchOrbitService = ElasticsearchOrbitService_1 = class ElasticsearchOrbitService extends elasticsearch_base_service_1.ElasticsearchBaseService {
    constructor(elasticsearchService) {
        super(elasticsearchService);
        this.logger = new common_1.Logger(ElasticsearchOrbitService_1.name);
        this.orbitIndex = 'orbit_info';
        this.tleQueryConfig = (0, tle_query_config_1.getTleQueryConfig)();
        try {
            (0, tle_query_config_1.validateTleQueryConfig)(this.tleQueryConfig);
            this.logger.log(`TLE查询配置加载完成: 时间窗口=${this.tleQueryConfig.timeWindowMinutes}分钟, 最大记录数=${this.tleQueryConfig.maxRecords}`);
        }
        catch (error) {
            this.logger.error(`TLE查询配置验证失败: ${error.message}`);
            throw error;
        }
    }
    getOrbitType(altitude) {
        if (altitude < 2000) {
            return orbit_query_dto_1.OrbitType.LEO;
        }
        else if (altitude < 35786) {
            return orbit_query_dto_1.OrbitType.MEO;
        }
        else {
            return orbit_query_dto_1.OrbitType.GEO;
        }
    }
    async searchOrbitInfo(queryDto) {
        try {
            const { page = 1, limit = 10, satellite_name, fuzzy_match = false, orbit_type, min_altitude, max_altitude, min_inclination, max_inclination, min_raan, max_raan, norad_id, cospar_id } = queryDto;
            this.logger.log('开始轨道信息查询...');
            this.logger.debug(`查询参数: ${JSON.stringify(queryDto)}`);
            let response = null;
            if (norad_id !== undefined) {
                this.logger.debug(`尝试使用norad_id(${norad_id})查询`);
                response = await this.elasticsearchService.search({
                    index: 'orbital_tle',
                    body: {
                        query: {
                            term: {
                                norad_id: norad_id
                            }
                        },
                        sort: [
                            {
                                epoch: {
                                    order: 'desc'
                                }
                            },
                            {
                                _score: {
                                    order: 'desc'
                                }
                            }
                        ]
                    },
                    size: 1,
                    from: 0
                });
                if (response.hits.hits.length > 0) {
                    this.logger.debug(`通过norad_id(${norad_id})查找到${response.hits.hits.length}条结果`);
                    return this.processSearchResults(response);
                }
            }
            if (cospar_id) {
                this.logger.debug(`尝试使用cospar_id(${cospar_id})查询`);
                response = await this.elasticsearchService.search({
                    index: 'orbital_tle',
                    body: {
                        query: {
                            match: {
                                cospar_id: cospar_id
                            }
                        },
                        sort: [
                            {
                                epoch: {
                                    order: 'desc'
                                }
                            },
                            {
                                _score: {
                                    order: 'desc'
                                }
                            }
                        ]
                    },
                    size: 1,
                    from: 0
                });
                if (response.hits.hits.length > 0) {
                    this.logger.debug(`通过cospar_id(${cospar_id})查找到${response.hits.hits.length}条结果`);
                    return this.processSearchResults(response);
                }
            }
            if (satellite_name) {
                this.logger.debug(`尝试使用satellite_name(${satellite_name})查询`);
                let query;
                if (fuzzy_match) {
                    query = {
                        query_string: {
                            query: `*${satellite_name}*`,
                            fields: ["satellite_name"]
                        }
                    };
                }
                else {
                    query = {
                        match: {
                            satellite_name: satellite_name
                        }
                    };
                }
                response = await this.elasticsearchService.search({
                    index: 'orbital_tle',
                    body: {
                        query: query,
                        sort: [
                            {
                                epoch: {
                                    order: 'desc'
                                }
                            },
                            {
                                _score: {
                                    order: 'desc'
                                }
                            }
                        ]
                    },
                    size: 1,
                    from: 0
                });
                if (response.hits.hits.length > 0) {
                    this.logger.debug(`通过satellite_name(${satellite_name})查找到${response.hits.hits.length}条结果`);
                    return this.processSearchResults(response);
                }
            }
            this.logger.debug(`使用轨道参数查询`);
            const must = [];
            if (orbit_type) {
                let altitudeRange = {};
                switch (orbit_type) {
                    case orbit_query_dto_1.OrbitType.LEO:
                        altitudeRange = { lt: 2000 };
                        break;
                    case orbit_query_dto_1.OrbitType.MEO:
                        altitudeRange = { gte: 2000, lt: 35786 };
                        break;
                    case orbit_query_dto_1.OrbitType.GEO:
                        altitudeRange = { gte: 35786 };
                        break;
                }
                must.push({
                    range: {
                        'orbital_elements.arg_alt_km': altitudeRange
                    }
                });
            }
            if (min_altitude !== undefined || max_altitude !== undefined) {
                const altitudeRange = {};
                if (min_altitude !== undefined) {
                    altitudeRange.gte = min_altitude;
                }
                if (max_altitude !== undefined) {
                    altitudeRange.lte = max_altitude;
                }
                must.push({
                    range: {
                        'orbital_elements.arg_alt_km': altitudeRange
                    }
                });
            }
            if (min_inclination !== undefined || max_inclination !== undefined) {
                const inclinationRange = {};
                if (min_inclination !== undefined) {
                    inclinationRange.gte = min_inclination;
                }
                if (max_inclination !== undefined) {
                    inclinationRange.lte = max_inclination;
                }
                must.push({
                    range: {
                        'orbital_elements.inc_deg': inclinationRange
                    }
                });
            }
            if (min_raan !== undefined || max_raan !== undefined) {
                const raanRange = {};
                if (min_raan !== undefined) {
                    raanRange.gte = min_raan;
                }
                if (max_raan !== undefined) {
                    raanRange.lte = max_raan;
                }
                must.push({
                    range: {
                        'orbital_elements.raan_deg': raanRange
                    }
                });
            }
            const query = must.length > 0
                ? { bool: { must } }
                : { match_all: {} };
            response = await this.elasticsearchService.search({
                index: 'orbital_tle',
                body: {
                    query: query,
                    sort: [
                        {
                            epoch: {
                                order: 'desc'
                            }
                        },
                        {
                            _score: {
                                order: 'desc'
                            }
                        }
                    ]
                },
                size: limit,
                from: (page - 1) * limit
            });
            return this.processSearchResults(response);
        }
        catch (error) {
            this.logger.error(`轨道信息查询出错: ${error.message}`, error.stack);
            this.logger.error(`查询参数: ${JSON.stringify(queryDto)}`);
            if (error.meta && error.meta.body) {
                this.logger.error(`ES错误详情: ${JSON.stringify(error.meta.body)}`);
            }
            throw new Error(`轨道信息查询失败: ${error.message}`);
        }
    }
    processSearchResults(response) {
        var _a;
        const resultCount = response.hits.hits.length;
        this.logger.debug(`查询结果数量: ${resultCount}`);
        if (resultCount > 0) {
            const firstHit = response.hits.hits[0]._source;
            this.logger.debug(`首条结果: norad_id=${(firstHit === null || firstHit === void 0 ? void 0 : firstHit.norad_id) || 'N/A'}, cospar_id=${(firstHit === null || firstHit === void 0 ? void 0 : firstHit.cospar_id) || 'N/A'}, satellite_name=${(firstHit === null || firstHit === void 0 ? void 0 : firstHit.satellite_name) || 'N/A'}, epoch=${(firstHit === null || firstHit === void 0 ? void 0 : firstHit.epoch) || 'N/A'}`);
        }
        else {
            this.logger.warn(`未找到匹配结果`);
        }
        const hits = response.hits.hits.map((hit) => {
            var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
            const source = hit._source;
            return {
                satellite_name: source.satellite_name,
                norad_id: source.norad_id,
                cospar_id: source.cospar_id,
                tle_raw: source.tle_raw,
                time: source.time,
                epoch: source.epoch,
                mean_motion_dot: source.mean_motion_dot,
                mean_motion_ddot: source.mean_motion_ddot,
                drag_term: source.drag_term,
                orbital_elements: {
                    inc_deg: (_a = source.orbital_elements) === null || _a === void 0 ? void 0 : _a.inc_deg,
                    raan_deg: (_b = source.orbital_elements) === null || _b === void 0 ? void 0 : _b.raan_deg,
                    ecc: (_c = source.orbital_elements) === null || _c === void 0 ? void 0 : _c.ecc,
                    arg_peri_deg: (_d = source.orbital_elements) === null || _d === void 0 ? void 0 : _d.arg_peri_deg,
                    mean_anom_deg: (_e = source.orbital_elements) === null || _e === void 0 ? void 0 : _e.mean_anom_deg,
                    day_laps: (_f = source.orbital_elements) === null || _f === void 0 ? void 0 : _f.day_laps,
                    orbit_num: (_g = source.orbital_elements) === null || _g === void 0 ? void 0 : _g.orbit_num,
                    orbital_period_min: (_h = source.orbital_elements) === null || _h === void 0 ? void 0 : _h.orbital_period_min,
                    sema_km: (_j = source.orbital_elements) === null || _j === void 0 ? void 0 : _j.sema_km,
                    arg_alt_km: (_k = source.orbital_elements) === null || _k === void 0 ? void 0 : _k.arg_alt_km,
                    arg_apo_deg: (_l = source.orbital_elements) === null || _l === void 0 ? void 0 : _l.arg_apo_deg,
                    launch_time: (_m = source.orbital_elements) === null || _m === void 0 ? void 0 : _m.launch_time
                }
            };
        });
        const total = typeof response.hits.total === 'number'
            ? response.hits.total
            : (((_a = response.hits.total) === null || _a === void 0 ? void 0 : _a.value) || 0);
        return {
            total,
            hits
        };
    }
    async searchOrbits(query) {
        try {
            const { page = 1, limit = 10, sort } = query, filters = __rest(query, ["page", "limit", "sort"]);
            const sortField = (sort === null || sort === void 0 ? void 0 : sort.field) || 'epoch';
            const sortOrder = (sort === null || sort === void 0 ? void 0 : sort.order) || 'desc';
            const must = this.buildOrbitQueryConditions(filters);
            const searchResult = await this.elasticsearchService.search({
                index: this.orbitIndex,
                body: {
                    query: {
                        bool: {
                            must,
                        },
                    },
                    sort: [
                        {
                            [sortField]: {
                                order: sortOrder,
                            },
                        },
                    ],
                    from: (page - 1) * limit,
                    size: limit,
                },
            });
            const total = typeof searchResult.hits.total === 'number'
                ? searchResult.hits.total
                : searchResult.hits.total.value;
            const items = searchResult.hits.hits.map((hit) => (Object.assign(Object.assign({}, hit._source), { _id: hit._id })));
            return {
                total,
                page,
                size: limit,
                items,
            };
        }
        catch (error) {
            this.logger.error(`搜索轨道信息失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getOrbitHistory(noradId, startDate, endDate) {
        try {
            const must = [];
            must.push({
                term: {
                    norad_id: noradId,
                },
            });
            if (startDate || endDate) {
                const rangeQuery = {};
                if (startDate) {
                    rangeQuery.gte = this.normalizeDate(startDate);
                }
                if (endDate) {
                    rangeQuery.lte = this.normalizeDate(endDate);
                }
                must.push({
                    range: {
                        epoch: rangeQuery,
                    },
                });
            }
            const searchResult = await this.elasticsearchService.search({
                index: this.orbitIndex,
                body: {
                    query: {
                        bool: {
                            must,
                        },
                    },
                    sort: [
                        {
                            epoch: {
                                order: 'asc',
                            },
                        },
                    ],
                    size: 1000,
                },
            });
            return searchResult.hits.hits.map((hit) => {
                const source = hit._source;
                return Object.assign(Object.assign({}, source), { _id: hit._id });
            });
        }
        catch (error) {
            this.logger.error(`获取轨道历史失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    buildOrbitQueryConditions(filters) {
        const must = [];
        if (filters.norad_id) {
            must.push({
                term: {
                    norad_id: filters.norad_id,
                },
            });
        }
        if (filters.start_date || filters.end_date) {
            const rangeQuery = {};
            if (filters.start_date) {
                rangeQuery.gte = this.normalizeDate(filters.start_date);
            }
            if (filters.end_date) {
                rangeQuery.lte = this.normalizeDate(filters.end_date);
            }
            must.push({
                range: {
                    epoch: rangeQuery,
                },
            });
        }
        if (filters.orbit_type) {
            must.push({
                match: {
                    orbit_type: filters.orbit_type,
                },
            });
        }
        return must.length ? must : [{ match_all: {} }];
    }
    normalizeDate(date) {
        if (!date)
            return null;
        try {
            const dateObj = new Date(date);
            if (isNaN(dateObj.getTime())) {
                throw new Error('无效的日期格式');
            }
            return dateObj.toISOString();
        }
        catch (error) {
            this.logger.warn(`日期格式化失败: ${date}, ${error.message}`);
            return null;
        }
    }
    async getBulkSatelliteTleByNoradIds(noradIds, page = 1, limit = 1000, useOneTimeQuery = false) {
        var _a, _b, _c;
        try {
            if (!noradIds || noradIds.length === 0) {
                if (useOneTimeQuery) {
                    this.logger.log(`🚀 使用一次性查询模式获取所有卫星TLE数据`);
                    return await this.getAllSatelliteTleData();
                }
                this.logger.log(`开始高性能查询所有卫星的TLE数据，第${page}页，每页${limit}条`);
                this.logger.warn(`💡 提示: 如需一次性获取所有数据，请使用 /orbit/bulk-tle/all 接口或设置 useOneTimeQuery=true`);
                const maxLimit = 10000;
                if (limit > maxLimit) {
                    this.logger.warn(`每页记录数(${limit})超过最大限制(${maxLimit})，将调整为${maxLimit}`);
                    limit = maxLimit;
                }
                let afterKey = null;
                if (page > 1) {
                    const estimatedStartNoradId = (page - 1) * limit;
                    afterKey = { norad_id: estimatedStartNoradId };
                }
                const [totalCountPromise, dataPromise] = await Promise.all([
                    this.elasticsearchService.search({
                        index: 'orbital_tle',
                        body: {
                            size: 0,
                            aggs: {
                                unique_count: {
                                    cardinality: {
                                        field: "norad_id",
                                        precision_threshold: 10000
                                    }
                                }
                            }
                        }
                    }),
                    this.elasticsearchService.search({
                        index: 'orbital_tle',
                        body: {
                            size: 0,
                            aggs: {
                                norad_pagination: {
                                    composite: Object.assign({ size: limit, sources: [
                                            {
                                                norad_id: {
                                                    terms: {
                                                        field: "norad_id",
                                                        order: "asc"
                                                    }
                                                }
                                            }
                                        ] }, (afterKey && { after: afterKey })),
                                    aggs: {
                                        latest_tle: {
                                            top_hits: {
                                                size: 1,
                                                sort: [{ epoch: { order: "desc" } }],
                                                _source: ["tle_raw", "satellite_name", "cospar_id", "norad_id", "epoch"]
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    })
                ]);
                const totalCount = ((_b = (_a = totalCountPromise.aggregations) === null || _a === void 0 ? void 0 : _a.unique_count) === null || _b === void 0 ? void 0 : _b.value) || 0;
                const totalPages = Math.ceil(totalCount / limit);
                const compositeResult = (_c = dataPromise.aggregations) === null || _c === void 0 ? void 0 : _c.norad_pagination;
                const buckets = (compositeResult === null || compositeResult === void 0 ? void 0 : compositeResult.buckets) || [];
                const nextAfterKey = compositeResult === null || compositeResult === void 0 ? void 0 : compositeResult.after_key;
                this.logger.debug(`高性能查询完成: 总数${totalCount}，当前页${buckets.length}条记录`);
                const results = buckets.map((bucket) => {
                    var _a, _b, _c;
                    const latestHit = (_c = (_b = (_a = bucket.latest_tle) === null || _a === void 0 ? void 0 : _a.hits) === null || _b === void 0 ? void 0 : _b.hits) === null || _c === void 0 ? void 0 : _c[0];
                    if (latestHit && latestHit._source) {
                        const source = latestHit._source;
                        this.logger.debug(`norad_id ${source.norad_id} 最新epoch: ${source.epoch}`);
                        return source;
                    }
                    return null;
                }).filter(Boolean);
                const result = {
                    success: true,
                    total: totalCount,
                    page: page,
                    limit: limit,
                    totalPages: totalPages,
                    currentPageCount: results.length,
                    hasNextPage: !!nextAfterKey && page < totalPages,
                    hasPrevPage: page > 1,
                    results: results,
                    _pagination: {
                        afterKey: nextAfterKey,
                        method: 'composite'
                    },
                    _performance: {
                        message: "如需一次性获取所有数据，请使用 POST /orbit/bulk-tle/all 接口",
                        estimatedTotalTime: `约 ${Math.ceil(totalPages * 0.5)} 秒（分页方式）`,
                        recommendedApproach: "一次性获取接口可在几秒内返回全部数据"
                    }
                };
                this.logger.log(`高性能查询成功: 第${page}页返回${result.currentPageCount}条去重的卫星TLE数据`);
                return result;
            }
            this.logger.log(`开始批量查询${noradIds.length}个特定卫星的TLE数据`);
            const maxIdsCount = 100;
            if (noradIds.length > maxIdsCount) {
                this.logger.warn(`请求的卫星数量(${noradIds.length})超过最大限制(${maxIdsCount})，将只处理前${maxIdsCount}个`);
                noradIds = noradIds.slice(0, maxIdsCount);
            }
            const response = await this.elasticsearchService.search({
                index: 'orbital_tle',
                preference: "_local",
                timeout: `${this.tleQueryConfig.timeout}ms`,
                body: {
                    query: {
                        terms: {
                            norad_id: noradIds
                        }
                    },
                    collapse: {
                        field: "norad_id",
                        inner_hits: {
                            name: "latest_tle",
                            size: 1,
                            sort: [{ epoch: { order: "desc" } }],
                            _source: ["tle_raw", "satellite_name", "cospar_id", "norad_id", "epoch"]
                        }
                    },
                    sort: [
                        { norad_id: { order: "asc" } }
                    ],
                    size: noradIds.length,
                    _source: ["tle_raw", "satellite_name", "cospar_id", "norad_id", "epoch"]
                }
            });
            const totalHits = response.hits.total.value || 0;
            this.logger.debug(`特定卫星查询: 找到${totalHits}条记录，返回${response.hits.hits.length}条去重记录`);
            const results = response.hits.hits.map(hit => {
                if (hit.inner_hits && hit.inner_hits.latest_tle &&
                    hit.inner_hits.latest_tle.hits.hits.length > 0) {
                    return hit.inner_hits.latest_tle.hits.hits[0]._source;
                }
                return hit._source;
            });
            const result = {
                success: true,
                total: response.hits.hits.length,
                results: results
            };
            const foundNoradIds = new Set(result.results.map((item) => item.norad_id));
            const missingNoradIds = noradIds.filter(id => !foundNoradIds.has(id));
            if (missingNoradIds.length > 0) {
                this.logger.warn(`未找到以下${missingNoradIds.length}个卫星的TLE数据: ${missingNoradIds.join(', ')}`);
            }
            this.logger.log(`特定卫星查询成功: 返回${result.total}条去重的卫星TLE数据`);
            return result;
        }
        catch (error) {
            this.logger.error(`批量查询卫星TLE数据失败: ${error.message}`, error.stack);
            throw new Error(`批量查询卫星TLE数据失败: ${error.message}`);
        }
    }
    async getAllSatelliteTleData(sampleMode = false) {
        var _a, _b, _c, _d, _e, _f;
        try {
            this.logger.log(`🚀 开始一次性获取所有卫星TLE数据（基于最新time字段前${this.tleQueryConfig.timeWindowMinutes}分钟）`);
            const startTime = Date.now();
            this.logger.log(`📊 开始获取time字段的最大值...`);
            const maxTimeStartTime = Date.now();
            const maxTimeResponse = await this.elasticsearchService.search({
                index: 'orbital_tle',
                timeout: `${this.tleQueryConfig.timeout}ms`,
                body: {
                    size: 0,
                    aggs: {
                        max_time: {
                            max: {
                                field: "time"
                            }
                        }
                    }
                }
            });
            const maxTime = (_b = (_a = maxTimeResponse.aggregations) === null || _a === void 0 ? void 0 : _a.max_time) === null || _b === void 0 ? void 0 : _b.value;
            if (!maxTime) {
                throw new Error('未找到time字段的最大值');
            }
            const maxTimeDate = new Date(maxTime);
            const timeWindowAgo = new Date(maxTimeDate.getTime() - this.tleQueryConfig.timeWindowMinutes * 60 * 1000);
            const timeWindowAgoISO = timeWindowAgo.toISOString();
            const maxTimeQueryTime = Date.now() - maxTimeStartTime;
            this.logger.log(`📊 找到最新的time值: ${maxTime}，${this.tleQueryConfig.timeWindowMinutes}分钟前时间: ${timeWindowAgoISO}，耗时${maxTimeQueryTime}ms`);
            this.logger.log(`📊 开始统计满足条件的总记录数...`);
            const countResponse = await this.elasticsearchService.count({
                index: 'orbital_tle',
                body: {
                    query: {
                        range: {
                            time: {
                                gte: timeWindowAgoISO,
                                lte: maxTime
                            }
                        }
                    }
                }
            });
            const totalCount = countResponse.count;
            this.logger.log(`📊 统计完成: 共找到${totalCount}条time在[${timeWindowAgoISO}, ${maxTime}]范围内的记录`);
            const dataStartTime = Date.now();
            let results = [];
            let queryMethod = '';
            if (totalCount < 20000) {
                this.logger.log(`📥 数据量${totalCount} < 20000，使用按norad_id去重查询所有最新数据...`);
                let allResults = [];
                let afterKey = null;
                let batchCount = 0;
                const maxBatches = 50;
                while (batchCount < maxBatches) {
                    const response = await this.elasticsearchService.search({
                        index: 'orbital_tle',
                        timeout: `${this.tleQueryConfig.timeout}ms`,
                        body: {
                            size: 0,
                            aggs: {
                                satellites_by_norad: {
                                    composite: Object.assign({ size: this.tleQueryConfig.scrollSize, sources: [
                                            {
                                                norad_id: {
                                                    terms: {
                                                        field: "norad_id",
                                                        order: "asc"
                                                    }
                                                }
                                            }
                                        ] }, (afterKey && { after: afterKey })),
                                    aggs: {
                                        latest_data: {
                                            top_hits: {
                                                size: 1,
                                                sort: [{ time: { order: "desc" } }],
                                                _source: ["tle_raw", "satellite_name", "cospar_id", "norad_id", "epoch", "time"]
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    });
                    const buckets = ((_d = (_c = response.aggregations) === null || _c === void 0 ? void 0 : _c.satellites_by_norad) === null || _d === void 0 ? void 0 : _d.buckets) || [];
                    if (buckets.length === 0) {
                        break;
                    }
                    const batchResults = buckets.map((bucket) => {
                        var _a, _b, _c;
                        const latestHit = (_c = (_b = (_a = bucket.latest_data) === null || _a === void 0 ? void 0 : _a.hits) === null || _b === void 0 ? void 0 : _b.hits) === null || _c === void 0 ? void 0 : _c[0];
                        return latestHit === null || latestHit === void 0 ? void 0 : latestHit._source;
                    }).filter(Boolean);
                    allResults = allResults.concat(batchResults);
                    afterKey = (_f = (_e = response.aggregations) === null || _e === void 0 ? void 0 : _e.satellites_by_norad) === null || _f === void 0 ? void 0 : _f.after_key;
                    batchCount++;
                    this.logger.log(`📥 第${batchCount}批: 获取${batchResults.length}个卫星的最新数据，累计${allResults.length}个卫星`);
                    if (!afterKey) {
                        break;
                    }
                }
                results = allResults;
                queryMethod = 'norad_dedup_query';
                this.logger.log(`📥 按norad_id去重查询完成: 总共${batchCount}批次，获取${results.length}个不重复卫星的最新数据`);
            }
            else if (totalCount <= 10000) {
                this.logger.log(`📥 数据量${totalCount} <= 10000，使用常规查询...`);
                const response = await this.elasticsearchService.search({
                    index: 'orbital_tle',
                    timeout: `${this.tleQueryConfig.timeout}ms`,
                    body: {
                        query: {
                            range: {
                                time: {
                                    gte: timeWindowAgoISO,
                                    lte: maxTime
                                }
                            }
                        },
                        sort: [{ time: { order: "desc" } }, { norad_id: { order: "asc" } }],
                        size: totalCount,
                        _source: ["tle_raw", "satellite_name", "cospar_id", "norad_id", "epoch", "time"]
                    }
                });
                results = response.hits.hits.map(hit => hit._source);
                queryMethod = 'direct_query';
            }
            else {
                this.logger.log(`📥 数据量${totalCount} > 10000，使用滚动查询获取所有数据...`);
                let scrollResponse = await this.elasticsearchService.search({
                    index: 'orbital_tle',
                    scroll: this.tleQueryConfig.scrollTimeout,
                    timeout: `${this.tleQueryConfig.timeout}ms`,
                    body: {
                        query: {
                            range: {
                                time: {
                                    gte: timeWindowAgoISO,
                                    lte: maxTime
                                }
                            }
                        },
                        sort: [{ time: { order: "desc" } }, { norad_id: { order: "asc" } }],
                        size: this.tleQueryConfig.scrollSize,
                        _source: ["tle_raw", "satellite_name", "cospar_id", "norad_id", "epoch", "time"]
                    }
                });
                results = scrollResponse.hits.hits.map(hit => hit._source);
                let scrollId = scrollResponse._scroll_id;
                this.logger.log(`📥 第1批: 获取${results.length}条记录`);
                let batchCount = 1;
                while (scrollResponse.hits.hits.length > 0) {
                    scrollResponse = await this.elasticsearchService.scroll({
                        scroll_id: scrollId,
                        scroll: this.tleQueryConfig.scrollTimeout
                    });
                    if (scrollResponse.hits.hits.length === 0) {
                        break;
                    }
                    const batchResults = scrollResponse.hits.hits.map(hit => hit._source);
                    results = results.concat(batchResults);
                    scrollId = scrollResponse._scroll_id;
                    batchCount++;
                    this.logger.log(`📥 第${batchCount}批: 获取${batchResults.length}条记录，累计${results.length}条`);
                }
                if (scrollId) {
                    try {
                        await this.elasticsearchService.clearScroll({
                            scroll_id: scrollId
                        });
                        this.logger.log(`🧹 已清理滚动上下文`);
                    }
                    catch (clearError) {
                        this.logger.warn(`清理滚动上下文失败: ${clearError.message}`);
                    }
                }
                this.logger.log(`📥 滚动查询完成: 总共${batchCount}批次，获取${results.length}条记录`);
                queryMethod = 'scroll_query';
            }
            const dataQueryTime = Date.now() - dataStartTime;
            const totalTime = Date.now() - startTime;
            if (results.length !== totalCount) {
                this.logger.warn(`⚠️ 数据完整性检查: 预期${totalCount}条，实际获取${results.length}条`);
            }
            else {
                this.logger.log(`✅ 数据完整性验证: 成功获取所有${totalCount}条记录`);
            }
            const timeDistribution = new Map();
            results.forEach((item) => {
                const timeKey = item.time;
                timeDistribution.set(timeKey, (timeDistribution.get(timeKey) || 0) + 1);
            });
            this.logger.log(`📊 时间分布统计: 共${timeDistribution.size}个不同的时间点`);
            let finalResults = results;
            let isSampleMode = false;
            if (sampleMode && results.length > 100) {
                finalResults = results.slice(0, 100);
                isSampleMode = true;
                this.logger.log(`📋 示例模式激活: 限制返回前100条数据（总共${results.length}条可用）`);
            }
            const result = Object.assign(Object.assign({ success: true, total: finalResults.length }, (isSampleMode && { actualTotal: results.length })), { executionTime: totalTime, timeRangeStart: timeWindowAgoISO, timeRangeEnd: maxTime, timeRangeMinutes: this.tleQueryConfig.timeWindowMinutes, method: queryMethod, queryStrategy: this.getQueryStrategyDescription(queryMethod), sampleMode: isSampleMode, results: finalResults });
            this.logger.log(`🎉 获取完成: 找到${result.total}条time在[${timeWindowAgoISO}, ${maxTime}]范围内的记录，总耗时${totalTime}ms (${(totalTime / 1000).toFixed(1)}秒)`);
            return result;
        }
        catch (error) {
            this.logger.error(`获取所有卫星TLE数据失败: ${error.message}`, error.stack);
            throw new Error(`获取所有卫星TLE数据失败: ${error.message}`);
        }
    }
    getTleQueryConfig() {
        return Object.assign({}, this.tleQueryConfig);
    }
    updateTleQueryConfig(newConfig) {
        this.tleQueryConfig = (0, tle_query_config_1.mergeTleQueryConfig)(this.tleQueryConfig, newConfig);
        (0, tle_query_config_1.validateTleQueryConfig)(this.tleQueryConfig);
        this.logger.log(`TLE查询配置已更新: 时间窗口=${this.tleQueryConfig.timeWindowMinutes}分钟, 最大记录数=${this.tleQueryConfig.maxRecords}`);
    }
    getQueryStrategyDescription(method) {
        switch (method) {
            case 'norad_dedup_query':
                return '按NORAD ID去重查询';
            case 'direct_query':
                return '单次查询';
            case 'scroll_query':
                return '滚动查询';
            default:
                return '未知查询策略';
        }
    }
};
ElasticsearchOrbitService = ElasticsearchOrbitService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [elasticsearch_1.ElasticsearchService])
], ElasticsearchOrbitService);
exports.ElasticsearchOrbitService = ElasticsearchOrbitService;
//# sourceMappingURL=elasticsearch.orbit.service.js.map