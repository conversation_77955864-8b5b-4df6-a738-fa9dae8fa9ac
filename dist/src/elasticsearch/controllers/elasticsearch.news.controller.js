"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ElasticsearchNewsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchNewsController = void 0;
const common_1 = require("@nestjs/common");
const elasticsearch_news_service_1 = require("../services/elasticsearch.news.service");
const swagger_1 = require("@nestjs/swagger");
const news_dto_1 = require("../dto/news.dto");
const news_list_query_dto_1 = require("../dto/news-list-query.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let ElasticsearchNewsController = ElasticsearchNewsController_1 = class ElasticsearchNewsController {
    constructor(newsService) {
        this.newsService = newsService;
        this.logger = new common_1.Logger(ElasticsearchNewsController_1.name);
    }
    async getNewsIndices() {
        try {
            const indices = await this.newsService.getNewsIndices();
            return {
                success: true,
                data: indices,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`获取新闻索引失败: ${error.message}`, error.stack);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
    async getTranslationStatus() {
        try {
            const status = await this.newsService.getTranslationStatus();
            return {
                success: true,
                data: status,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`获取翻译状态失败: ${error.message}`, error.stack);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
    async translateNews(params) {
        this.logger.log(`收到翻译请求: ${JSON.stringify(params)}`);
        try {
            const statistics = await this.newsService.translateNews(params);
            return {
                success: true,
                message: '翻译任务执行成功',
                statistics,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`翻译任务执行失败: ${error.message}`, error.stack);
            return {
                success: false,
                message: `翻译任务执行失败: ${error.message}`,
                statistics: {
                    total: 0,
                    success: 0,
                    failed: 0,
                    skipped: 0,
                    indexCount: 0,
                    indexes: [],
                    startTime: new Date(),
                    endTime: new Date(),
                    elapsedTime: 0
                },
                timestamp: new Date().toISOString()
            };
        }
    }
    async getAPIStats() {
        try {
            const stats = this.newsService.getAPIStats();
            return {
                success: true,
                data: stats,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`获取API调用统计失败: ${error.message}`, error.stack);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
    async getFailureStats() {
        try {
            const stats = this.newsService.getFailureStats();
            return {
                success: true,
                data: stats,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`获取失败统计失败: ${error.message}`, error.stack);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
    async resetFailureStats() {
        try {
            this.newsService.resetFailureStats();
            return {
                success: true,
                message: '失败统计已重置',
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`重置失败统计失败: ${error.message}`, error.stack);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
    async getFailedTranslations() {
        try {
            const failedDocs = this.newsService.getFailedTranslations();
            return {
                success: true,
                data: {
                    count: failedDocs.length,
                    documents: failedDocs
                },
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`获取失败翻译文档失败: ${error.message}`, error.stack);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
    async retryFailedTranslations(params) {
        const { maxRetries = 3, specificDocuments } = params;
        try {
            let failedDocs = this.newsService.getFailedTranslations();
            if (specificDocuments && specificDocuments.length > 0) {
                failedDocs = failedDocs.filter(doc => specificDocuments.some(spec => spec.index === doc.index && spec.id === doc.id));
            }
            if (failedDocs.length === 0) {
                return {
                    success: true,
                    message: '没有需要重试的失败文档',
                    count: 0,
                    timestamp: new Date().toISOString()
                };
            }
            const result = await this.newsService.retryFailedTranslations(failedDocs, maxRetries);
            return {
                success: true,
                message: '重试完成',
                statistics: result,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`重试失败的翻译出错: ${error.message}`, error.stack);
            return {
                success: false,
                message: `重试失败: ${error.message}`,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
    async extractThemes(params) {
        this.logger.log(`收到主题提取请求: ${JSON.stringify(params)}`);
        try {
            const statistics = await this.newsService.extractNewsThemes(params);
            return {
                success: true,
                message: '主题提取和内容类型识别任务执行成功',
                statistics,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`主题提取任务执行失败: ${error.message}`, error.stack);
            return {
                success: false,
                message: `主题提取和内容类型识别任务执行失败: ${error.message}`,
                statistics: {
                    total: 0,
                    success: 0,
                    failed: 0,
                    skipped: 0,
                    indexCount: 0,
                    indexes: [],
                    startTime: new Date(),
                    endTime: new Date(),
                    elapsedTime: 0
                },
                timestamp: new Date().toISOString()
            };
        }
    }
    async getNewsList(query) {
        try {
            const result = await this.newsService.searchNewsList(query);
            return {
                success: true,
                data: result,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`获取新闻列表失败: ${error.message}`, error.stack);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
    async getHotThemes(params) {
        this.logger.log(`收到热门主题词请求: ${JSON.stringify(params)}`);
        try {
            const result = await this.newsService.getHotThemes(params);
            return {
                success: true,
                message: '获取热门主题词成功',
                data: result,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`获取热门主题词失败: ${error.message}`, error.stack);
            return {
                success: false,
                message: `获取热门主题词失败: ${error.message}`,
                data: {
                    themes: [],
                    total: 0,
                    indexCount: 0,
                    indexes: [],
                    processedDocs: 0
                },
                timestamp: new Date().toISOString()
            };
        }
    }
};
__decorate([
    (0, common_1.Get)('indices'),
    (0, swagger_1.ApiOperation)({ summary: '获取所有新闻索引', description: '获取所有以news_开头的Elasticsearch索引' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功返回所有新闻索引' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ElasticsearchNewsController.prototype, "getNewsIndices", null);
__decorate([
    (0, common_1.Get)('translation-status'),
    (0, swagger_1.ApiOperation)({
        summary: '获取翻译状态',
        description: '获取所有新闻索引的翻译状态统计，包括总文档数、已翻译数、未翻译数等'
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功返回翻译状态统计' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ElasticsearchNewsController.prototype, "getTranslationStatus", null);
__decorate([
    (0, common_1.Post)('translate'),
    (0, swagger_1.ApiOperation)({
        summary: '翻译新闻并自动提取主题词（优化版）',
        description: '将未翻译的新闻数据（title、summary、content字段）翻译为中文，并存储到title_cn、summary_cn、content_cn字段。当autoExtractThemes=true时，使用优化的一次性调用方式同时完成翻译和主题提取，大幅减少API调用次数和token消耗，提高处理效率。自动识别内容类型（科普/军事），存储到themes_cn字段。使用阿里云Qwen大模型，支持智能错误处理和回退机制。建议设置合理的批处理参数，每8小时执行一次可处理新增的新闻内容。'
    }),
    (0, swagger_1.ApiBody)({
        type: news_dto_1.TranslateNewsDto,
        description: '翻译新闻请求参数',
        examples: {
            '标准请求（优化版）': {
                summary: '标准翻译和主题提取请求示例（一次性调用）',
                description: '设置批处理大小为20，最多处理100个文档，不重新翻译已翻译内容，指定处理两个特定索引，使用优化的一次性调用同时完成翻译和主题提取，减少75%的API调用次数',
                value: {
                    batchSize: 20,
                    maxDocs: 100,
                    forceRetranslate: false,
                    specificIndexes: ['news_2023_04', 'news_2023_05'],
                    autoExtractThemes: true
                }
            },
            '处理所有新闻（高效模式）': {
                summary: '处理所有新闻索引（一次性调用）',
                description: '使用默认批处理大小，不限制处理文档数量，只处理未翻译内容，使用优化的一次性调用模式，显著提高处理效率和降低成本',
                value: {
                    batchSize: 10,
                    maxDocs: 0,
                    forceRetranslate: false,
                    autoExtractThemes: true
                }
            },
            '重新翻译': {
                summary: '重新翻译已翻译的内容',
                description: '强制重新翻译所有内容，包括已翻译过的文档，自动提取主题词',
                value: {
                    batchSize: 5,
                    maxDocs: 50,
                    forceRetranslate: true,
                    autoExtractThemes: true
                }
            },
            '仅翻译不提取主题词': {
                summary: '仅翻译不提取主题词',
                description: '只进行翻译，不自动提取主题词，适用于需要单独控制主题提取的场景',
                value: {
                    batchSize: 10,
                    maxDocs: 100,
                    forceRetranslate: false,
                    autoExtractThemes: false
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '翻译和主题提取任务执行结果，包含翻译和主题提取的成功/失败文档数量、处理的索引、耗时等统计信息',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', description: '任务是否成功完成' },
                message: { type: 'string', description: '任务执行结果描述' },
                statistics: {
                    type: 'object',
                    description: '翻译和主题提取任务统计信息',
                    properties: {
                        total: { type: 'number', description: '总处理文档数' },
                        success: { type: 'number', description: '成功翻译的文档数' },
                        failed: { type: 'number', description: '翻译失败的文档数' },
                        skipped: { type: 'number', description: '已翻译过被跳过的文档数' },
                        indexCount: { type: 'number', description: '处理的索引数量' },
                        indexes: { type: 'array', items: { type: 'string' }, description: '处理的索引名称列表' },
                        elapsedTime: { type: 'number', description: '任务执行总耗时(毫秒)' },
                        startTime: { type: 'string', format: 'date-time', description: '任务开始时间' },
                        endTime: { type: 'string', format: 'date-time', description: '任务结束时间' },
                        themeExtraction: {
                            type: 'object',
                            description: '主题提取统计信息（仅在autoExtractThemes=true时存在）',
                            properties: {
                                total: { type: 'number', description: '尝试提取主题词的文档数' },
                                success: { type: 'number', description: '成功提取主题词的文档数' },
                                failed: { type: 'number', description: '主题提取失败的文档数' },
                                skipped: { type: 'number', description: '跳过主题提取的文档数' }
                            }
                        }
                    }
                },
                timestamp: { type: 'string', format: 'date-time', description: '响应时间戳' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误'
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: '服务器内部错误，如大模型API调用失败、数据库连接问题等'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [news_dto_1.TranslateNewsDto]),
    __metadata("design:returntype", Promise)
], ElasticsearchNewsController.prototype, "translateNews", null);
__decorate([
    (0, common_1.Get)('api-stats'),
    (0, swagger_1.ApiOperation)({
        summary: '获取API调用统计',
        description: '获取大模型API调用的统计信息，包括调用次数、总耗时、平均耗时、缓存大小等'
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功返回API调用统计' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ElasticsearchNewsController.prototype, "getAPIStats", null);
__decorate([
    (0, common_1.Get)('failure-stats'),
    (0, swagger_1.ApiOperation)({
        summary: '获取详细的失败统计信息',
        description: '获取翻译和主题提取过程中的详细失败统计，包括失败分类（内容过滤、超时、API限制、网络错误等）、成功率和系统优化建议'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '失败统计信息',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean' },
                data: {
                    type: 'object',
                    properties: {
                        totalAttempts: { type: 'number', description: '总尝试次数' },
                        successfulProcessing: { type: 'number', description: '成功处理次数' },
                        totalFailures: { type: 'number', description: '总失败次数' },
                        successRate: { type: 'number', description: '成功率百分比' },
                        failures: {
                            type: 'object',
                            properties: {
                                contentFilter: {
                                    type: 'object',
                                    properties: {
                                        count: { type: 'number', description: '内容过滤失败次数' },
                                        percentage: { type: 'string', description: '占总失败的百分比' }
                                    }
                                },
                                timeout: {
                                    type: 'object',
                                    properties: {
                                        count: { type: 'number', description: '超时失败次数' },
                                        percentage: { type: 'string', description: '占总失败的百分比' }
                                    }
                                },
                                rateLimit: {
                                    type: 'object',
                                    properties: {
                                        count: { type: 'number', description: 'API限制失败次数' },
                                        percentage: { type: 'string', description: '占总失败的百分比' }
                                    }
                                },
                                networkError: {
                                    type: 'object',
                                    properties: {
                                        count: { type: 'number', description: '网络错误次数' },
                                        percentage: { type: 'string', description: '占总失败的百分比' }
                                    }
                                },
                                other: {
                                    type: 'object',
                                    properties: {
                                        count: { type: 'number', description: '其他错误次数' },
                                        percentage: { type: 'string', description: '占总失败的百分比' }
                                    }
                                }
                            }
                        },
                        consecutiveFailures: { type: 'number', description: '连续失败次数' },
                        recommendations: {
                            type: 'array',
                            items: { type: 'string' },
                            description: '基于失败统计的系统优化建议'
                        }
                    }
                },
                timestamp: { type: 'string' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ElasticsearchNewsController.prototype, "getFailureStats", null);
__decorate([
    (0, common_1.Post)('reset-failure-stats'),
    (0, swagger_1.ApiOperation)({
        summary: '重置失败统计',
        description: '清零所有失败统计数据，重新开始统计。用于重新评估系统优化效果'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '重置成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean' },
                message: { type: 'string' },
                timestamp: { type: 'string' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ElasticsearchNewsController.prototype, "resetFailureStats", null);
__decorate([
    (0, common_1.Get)('failed-translations'),
    (0, swagger_1.ApiOperation)({
        summary: '获取失败的翻译文档',
        description: '获取翻译过程中失败的文档列表，包括失败原因和时间等信息'
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功返回失败文档列表' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ElasticsearchNewsController.prototype, "getFailedTranslations", null);
__decorate([
    (0, common_1.Post)('retry-failed'),
    (0, swagger_1.ApiOperation)({
        summary: '重试失败的翻译',
        description: '重新尝试翻译之前失败的文档'
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                maxRetries: {
                    type: 'number',
                    description: '最大重试次数',
                    default: 3
                },
                specificDocuments: {
                    type: 'array',
                    description: '指定要重试的文档ID列表，如果不提供则重试所有失败文档',
                    items: {
                        type: 'object',
                        properties: {
                            index: { type: 'string' },
                            id: { type: 'string' }
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功返回重试结果' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ElasticsearchNewsController.prototype, "retryFailedTranslations", null);
__decorate([
    (0, common_1.Post)('extract-themes'),
    (0, swagger_1.ApiOperation)({
        summary: '提取新闻主题词并识别内容类型',
        description: '从新闻数据中提取中文主题词，每条新闻不超过5个主题词，并存储到themes_cn字段。支持处理中文和英文文档，对于英文文档会直接提取中文主题词。同时识别新闻内容类型（科普/军事），只会选择其中一个最符合的类别，不会同时标记为两种类型。如果识别为科普类型会在主题词中添加"科普"，如果识别为军事类型会在主题词中添加"军事"，如果都不是则不添加类型标签。使用阿里云Qwen大模型进行主题提取和类型识别，提取质量高但速度较慢，建议设置合理的批处理参数。'
    }),
    (0, swagger_1.ApiBody)({
        type: news_dto_1.ExtractThemesDto,
        description: '主题提取请求参数',
        examples: {
            '标准请求': {
                summary: '标准主题提取和类型识别请求示例',
                description: '设置批处理大小为20，最多处理100个文档，不重新提取已有主题词的文档，指定处理两个特定索引。会同时提取主题词和识别内容类型（科普/军事）',
                value: {
                    batchSize: 20,
                    maxDocs: 100,
                    forceReextract: false,
                    specificIndexes: ['news_2023_04', 'news_2023_05']
                }
            },
            '处理所有新闻': {
                summary: '处理所有新闻索引',
                description: '使用默认批处理大小，不限制处理文档数量，只处理未提取主题词的文档。会自动识别科普和军事类型的新闻',
                value: {
                    batchSize: 10,
                    maxDocs: 0,
                    forceReextract: false
                }
            },
            '重新提取': {
                summary: '重新提取所有文档的主题词和内容类型',
                description: '强制重新提取所有文档的主题词和内容类型识别，包括已有主题词的文档。科普类型会添加"科普"主题词，军事类型会添加"军事"主题词',
                value: {
                    batchSize: 5,
                    maxDocs: 50,
                    forceReextract: true
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '主题提取任务执行结果，包含成功/失败文档数量、处理的索引、耗时等统计信息',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', description: '任务是否成功完成' },
                message: { type: 'string', description: '任务执行结果描述' },
                statistics: {
                    type: 'object',
                    description: '主题提取任务统计信息',
                    properties: {
                        total: { type: 'number', description: '总处理文档数' },
                        success: { type: 'number', description: '成功提取主题词和识别内容类型的文档数' },
                        failed: { type: 'number', description: '提取失败的文档数' },
                        skipped: { type: 'number', description: '已有主题词被跳过的文档数' },
                        indexCount: { type: 'number', description: '处理的索引数量' },
                        indexes: { type: 'array', items: { type: 'string' }, description: '处理的索引名称列表' },
                        elapsedTime: { type: 'number', description: '任务执行总耗时(毫秒)' },
                        startTime: { type: 'string', format: 'date-time', description: '任务开始时间' },
                        endTime: { type: 'string', format: 'date-time', description: '任务结束时间' }
                    }
                },
                timestamp: { type: 'string', format: 'date-time', description: '响应时间戳' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误'
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: '服务器内部错误，如大模型API调用失败、数据库连接问题等'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [news_dto_1.ExtractThemesDto]),
    __metadata("design:returntype", Promise)
], ElasticsearchNewsController.prototype, "extractThemes", null);
__decorate([
    (0, common_1.Post)('list'),
    (0, swagger_1.ApiOperation)({
        summary: '获取新闻列表',
        description: '根据指定条件获取新闻列表，支持关键词子串匹配（默认）、主题词、时间范围、索引模式等多种筛选条件，并支持分页返回。关键词搜索会在新闻的所有字段中进行匹配，包括标题、内容、来源、作者等。'
    }),
    (0, swagger_1.ApiBody)({
        type: news_list_query_dto_1.NewsListQueryDto,
        description: '新闻列表查询参数',
        examples: {
            '基本查询': {
                summary: '基本查询示例',
                description: '使用关键词和分页参数进行基本查询',
                value: {
                    page: 1,
                    limit: 10,
                    keywords: ['satellite', 'launch']
                }
            },
            '多条件查询': {
                summary: '多条件查询示例',
                description: '使用关键词、主题词和时间范围进行复合查询',
                value: {
                    page: 1,
                    limit: 20,
                    keywords: ['satellite'],
                    themes: ['航天', '卫星'],
                    publishDateStart: '2023-01-01',
                    publishDateEnd: '2023-12-31'
                }
            },
            '索引模式查询': {
                summary: '索引模式查询示例',
                description: '使用索引模式进行查询，可以筛选出包含defence或defense的索引',
                value: {
                    page: 1,
                    limit: 10,
                    indexPatterns: ['*defence*', '*defense*']
                }
            },
            '子串匹配查询（默认）': {
                summary: '子串匹配查询示例（默认方式）',
                description: '使用子串匹配方式搜索关键词，这是默认的匹配方式',
                value: {
                    page: 1,
                    limit: 10,
                    keywords: ['satellite']
                }
            },
            '相似性匹配查询': {
                summary: '相似性匹配查询示例',
                description: '使用相似性匹配方式搜索关键词',
                value: {
                    page: 1,
                    limit: 10,
                    keywords: ['satellite'],
                    keywordMatchType: 'similarity'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功返回新闻列表' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [news_list_query_dto_1.NewsListQueryDto]),
    __metadata("design:returntype", Promise)
], ElasticsearchNewsController.prototype, "getNewsList", null);
__decorate([
    (0, common_1.Get)('hot-themes'),
    (0, swagger_1.ApiOperation)({
        summary: '获取热门主题词',
        description: '统计新闻相关索引中所有文档的主题词，根据主题词的出现次数排序，返回排名前N个的主题词'
    }),
    (0, swagger_1.ApiQuery)({ name: 'topN', required: false, type: Number, description: '返回前N个热门主题词，默认10' }),
    (0, swagger_1.ApiQuery)({ name: 'minCount', required: false, type: Number, description: '主题词最小出现次数，默认1' }),
    (0, swagger_1.ApiQuery)({ name: 'specificIndexes', required: false, type: [String], isArray: true, description: '指定要处理的索引名称数组，不指定则处理所有news_*索引' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功返回热门主题词列表' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [news_dto_1.HotThemesDto]),
    __metadata("design:returntype", Promise)
], ElasticsearchNewsController.prototype, "getHotThemes", null);
ElasticsearchNewsController = ElasticsearchNewsController_1 = __decorate([
    (0, common_1.Controller)('api/es/news'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)('新闻分析'),
    __metadata("design:paramtypes", [elasticsearch_news_service_1.ElasticsearchNewsService])
], ElasticsearchNewsController);
exports.ElasticsearchNewsController = ElasticsearchNewsController;
//# sourceMappingURL=elasticsearch.news.controller.js.map