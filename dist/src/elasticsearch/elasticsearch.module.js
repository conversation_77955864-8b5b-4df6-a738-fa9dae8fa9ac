"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ESModule = void 0;
const common_1 = require("@nestjs/common");
const elasticsearch_1 = require("@nestjs/elasticsearch");
const config_1 = require("@nestjs/config");
const elasticsearch_base_service_1 = require("./services/elasticsearch.base.service");
const elasticsearch_debris_service_1 = require("./services/elasticsearch.debris.service");
const elasticsearch_satellite_service_1 = require("./services/elasticsearch.satellite.service");
const elasticsearch_freq_service_1 = require("./services/elasticsearch.freq.service");
const elasticsearch_orbit_service_1 = require("./services/elasticsearch.orbit.service");
const elasticsearch_constellation_service_1 = require("./services/elasticsearch.constellation.service");
const elasticsearch_debris_event_service_1 = require("./services/elasticsearch.debris-event.service");
const elasticsearch_correlation_service_1 = require("./services/elasticsearch.correlation.service");
const elasticsearch_loophole_service_1 = require("./services/elasticsearch.loophole.service");
const elasticsearch_news_service_1 = require("./services/elasticsearch.news.service");
const elasticsearch_launch_service_1 = require("./services/elasticsearch.launch.service");
const elasticsearch_rocket_service_1 = require("./services/elasticsearch.rocket.service");
const translation_service_1 = require("./services/translation.service");
const elasticsearch_debris_event_controller_1 = require("./controllers/elasticsearch.debris-event.controller");
const elasticsearch_constellation_controller_1 = require("./controllers/elasticsearch.constellation.controller");
const elasticsearch_correlation_controller_1 = require("./controllers/elasticsearch.correlation.controller");
const elasticsearch_debris_controller_1 = require("./controllers/elasticsearch.debris.controller");
const elasticsearch_satellite_controller_1 = require("./controllers/elasticsearch.satellite.controller");
const elasticsearch_freq_controller_1 = require("./controllers/elasticsearch.freq.controller");
const elasticsearch_orbit_controller_1 = require("./controllers/elasticsearch.orbit.controller");
const elasticsearch_loophole_controller_1 = require("./controllers/elasticsearch.loophole.controller");
const elasticsearch_news_controller_1 = require("./controllers/elasticsearch.news.controller");
const elasticsearch_launch_controller_1 = require("./controllers/elasticsearch.launch.controller");
const elasticsearch_rocket_controller_1 = require("./controllers/elasticsearch.rocket.controller");
let ESModule = class ESModule {
};
ESModule = __decorate([
    (0, common_1.Module)({
        imports: [
            elasticsearch_1.ElasticsearchModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    nodes: ['http://123.57.173.156:9200'],
                    auth: {
                        username: 'web_readonly',
                        password: 'web@readonly4all'
                    },
                    maxRetries: 10,
                    requestTimeout: 120000,
                    pingTimeout: 120000,
                    sniffOnStart: false,
                    ssl: {
                        rejectUnauthorized: false
                    },
                    retryOnConflict: 3,
                    compression: true,
                    suggestCompression: true,
                    keepAlive: true,
                    keepAliveInterval: 1000,
                    resurrectStrategy: 'ping',
                    name: 'spacedata-es-client',
                    healthCheck: false,
                    healthCheckInterval: 10000,
                    ignoreVersionMismatch: true,
                    tls: {
                        rejectUnauthorized: false
                    }
                }),
                inject: [config_1.ConfigService],
            }),
        ],
        controllers: [
            elasticsearch_debris_event_controller_1.ElasticsearchDebrisEventController,
            elasticsearch_constellation_controller_1.ElasticsearchConstellationController,
            elasticsearch_correlation_controller_1.ElasticsearchCorrelationController,
            elasticsearch_debris_controller_1.ElasticsearchDebrisController,
            elasticsearch_satellite_controller_1.ElasticsearchSatelliteController,
            elasticsearch_freq_controller_1.ElasticsearchFreqController,
            elasticsearch_orbit_controller_1.ElasticsearchOrbitController,
            elasticsearch_loophole_controller_1.ElasticsearchLoopholeController,
            elasticsearch_news_controller_1.ElasticsearchNewsController,
            elasticsearch_launch_controller_1.ElasticsearchLaunchController,
            elasticsearch_rocket_controller_1.ElasticsearchRocketController
        ],
        providers: [
            elasticsearch_base_service_1.ElasticsearchBaseService,
            elasticsearch_debris_service_1.ElasticsearchDebrisService,
            elasticsearch_satellite_service_1.ElasticsearchSatelliteService,
            elasticsearch_freq_service_1.ElasticsearchFreqService,
            elasticsearch_orbit_service_1.ElasticsearchOrbitService,
            elasticsearch_constellation_service_1.ElasticsearchConstellationService,
            elasticsearch_debris_event_service_1.ElasticsearchDebrisEventService,
            elasticsearch_correlation_service_1.ElasticsearchCorrelationService,
            elasticsearch_loophole_service_1.ElasticsearchLoopholeService,
            elasticsearch_news_service_1.ElasticsearchNewsService,
            elasticsearch_launch_service_1.ElasticsearchLaunchService,
            elasticsearch_rocket_service_1.ElasticsearchRocketService,
            translation_service_1.TranslationService
        ],
        exports: [
            elasticsearch_debris_service_1.ElasticsearchDebrisService,
            elasticsearch_satellite_service_1.ElasticsearchSatelliteService,
            elasticsearch_freq_service_1.ElasticsearchFreqService,
            elasticsearch_orbit_service_1.ElasticsearchOrbitService,
            elasticsearch_constellation_service_1.ElasticsearchConstellationService,
            elasticsearch_debris_event_service_1.ElasticsearchDebrisEventService,
            elasticsearch_correlation_service_1.ElasticsearchCorrelationService,
            elasticsearch_loophole_service_1.ElasticsearchLoopholeService,
            elasticsearch_news_service_1.ElasticsearchNewsService,
            elasticsearch_launch_service_1.ElasticsearchLaunchService,
            elasticsearch_rocket_service_1.ElasticsearchRocketService
        ],
    })
], ESModule);
exports.ESModule = ESModule;
//# sourceMappingURL=elasticsearch.module.js.map