"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DebrisQueryDto = exports.FieldMatchConfig = exports.MatchType = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
var MatchType;
(function (MatchType) {
    MatchType["EXACT"] = "exact";
    MatchType["FUZZY"] = "fuzzy";
})(MatchType = exports.MatchType || (exports.MatchType = {}));
class FieldMatchConfig {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '匹配类型',
        enum: MatchType,
        example: 'exact'
    }),
    (0, class_validator_1.IsEnum)(MatchType),
    __metadata("design:type", String)
], FieldMatchConfig.prototype, "matchType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '字段值',
        example: '1999-025EZ'
    }),
    (0, class_validator_1.IsNotEmpty)({ message: '字段值不能为空' }),
    __metadata("design:type", Object)
], FieldMatchConfig.prototype, "value", void 0);
exports.FieldMatchConfig = FieldMatchConfig;
class DebrisQueryDto {
    constructor() {
        this.page = 1;
        this.limit = 10;
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '页码',
        required: false,
        default: 1,
        minimum: 1
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], DebrisQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每页数量',
        required: false,
        default: 10,
        minimum: 1
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], DebrisQueryDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '排序配置',
        required: false,
        example: { field: 'norad_id', order: 'desc' }
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], DebrisQueryDto.prototype, "sort", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '关键词搜索',
        required: false,
        example: 'International Space Station'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DebrisQueryDto.prototype, "keyword", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'COSPAR ID查询配置',
        required: false,
        type: FieldMatchConfig,
        example: {
            matchType: 'exact',
            value: '1998-067A'
        }
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], DebrisQueryDto.prototype, "cospar_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'NORAD ID查询配置',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], DebrisQueryDto.prototype, "norad_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '碎片名称',
        required: false,
        example: 'ISS'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DebrisQueryDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '所属国家查询配置',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], DebrisQueryDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '发射日期查询配置',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], DebrisQueryDto.prototype, "launch_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '发射场查询配置',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], DebrisQueryDto.prototype, "launch_site", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '雷达散射截面大小查询配置',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], DebrisQueryDto.prototype, "rcs_size", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '坠毁日期查询配置',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], DebrisQueryDto.prototype, "decay", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '首次观测时间查询配置',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], DebrisQueryDto.prototype, "first_epoch", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '对象类型查询配置',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], DebrisQueryDto.prototype, "object_class", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '任务名称',
        required: false,
        example: 'International Space Station'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DebrisQueryDto.prototype, "mission", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '轨道周期范围（分钟）',
        required: false,
        example: { min: 90, max: 120 }
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], DebrisQueryDto.prototype, "period_minutes_range", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '轨道倾角范围（度）',
        required: false,
        example: { min: 45, max: 60 }
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], DebrisQueryDto.prototype, "incl_degrees_range", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '远地点高度范围（km）',
        required: false,
        example: { min: 500, max: 800 }
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], DebrisQueryDto.prototype, "apogee_km_range", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '近地点高度范围（km）',
        required: false,
        example: { min: 400, max: 600 }
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], DebrisQueryDto.prototype, "perigee_km_range", void 0);
exports.DebrisQueryDto = DebrisQueryDto;
//# sourceMappingURL=debris-query.dto.js.map