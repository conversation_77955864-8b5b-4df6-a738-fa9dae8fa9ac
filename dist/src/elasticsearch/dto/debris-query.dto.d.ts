export declare enum MatchType {
    EXACT = "exact",
    FUZZY = "fuzzy"
}
export declare class FieldMatchConfig {
    matchType: MatchType;
    value: string | number;
}
export declare class DebrisQueryDto {
    page?: number;
    limit?: number;
    sort?: {
        field: string;
        order: 'asc' | 'desc';
    };
    keyword?: string;
    cospar_id?: FieldMatchConfig;
    norad_id?: FieldMatchConfig;
    name?: string;
    country?: FieldMatchConfig;
    launch_date?: FieldMatchConfig;
    launch_site?: FieldMatchConfig;
    rcs_size?: FieldMatchConfig;
    decay?: FieldMatchConfig;
    first_epoch?: FieldMatchConfig;
    object_class?: FieldMatchConfig;
    mission?: string;
    period_minutes_range?: {
        min?: number;
        max?: number;
    };
    incl_degrees_range?: {
        min?: number;
        max?: number;
    };
    apogee_km_range?: {
        min?: number;
        max?: number;
    };
    perigee_km_range?: {
        min?: number;
        max?: number;
    };
}
