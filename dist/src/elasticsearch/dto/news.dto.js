"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HotThemesDto = exports.ExtractThemesDto = exports.TranslateNewsDto = exports.LLMMode = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
var LLMMode;
(function (LLMMode) {
    LLMMode["DEFAULT"] = "default";
    LLMMode["HIGH_QUALITY"] = "high_quality";
    LLMMode["FAST"] = "fast";
})(LLMMode = exports.LLMMode || (exports.LLMMode = {}));
class TranslateNewsDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每批处理的文档数量，建议根据文档大小适当设置，过大可能导致请求超时，过小会增加调用次数',
        default: 10,
        required: false,
        minimum: 1,
        maximum: 50,
        example: 20,
        type: Number
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], TranslateNewsDto.prototype, "batchSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最大处理文档数，0表示不限制处理数量，可用于限制单次API调用处理的总文档数量，控制执行时间',
        default: 0,
        required: false,
        minimum: 0,
        example: 100,
        type: Number
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], TranslateNewsDto.prototype, "maxDocs", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否强制重新翻译已翻译的内容，默认false只翻译未翻译内容，设为true会重新翻译所有内容（包括已有翻译的）',
        default: false,
        required: false,
        example: false,
        type: Boolean
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Type)(() => Boolean),
    __metadata("design:type", Boolean)
], TranslateNewsDto.prototype, "forceRetranslate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否强制重新爬取内容，默认false只爬取未爬取过的新闻，设为true会重新爬取所有有info_source的新闻内容（包括之前爬取失败的）',
        default: false,
        required: false,
        example: false,
        type: Boolean
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Type)(() => Boolean),
    __metadata("design:type", Boolean)
], TranslateNewsDto.prototype, "forceRefetchContent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '指定要处理的索引名称数组，支持精确指定索引名称或使用通配符匹配，不指定则处理所有以news_开头的索引',
        required: false,
        isArray: true,
        type: [String],
        example: ['news_2023_04', 'news_2023_05']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], TranslateNewsDto.prototype, "specificIndexes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '大模型模式选择：default（默认，qwen-turbo）、high_quality（高质量，qwen-max-latest）、fast（快速，qwen-turbo，更高并发）',
        required: false,
        enum: LLMMode,
        default: LLMMode.DEFAULT,
        example: LLMMode.DEFAULT
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(LLMMode),
    __metadata("design:type", String)
], TranslateNewsDto.prototype, "llmMode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '自定义模型名称，如果指定则覆盖llmMode设置，支持qwen-turbo、qwen-max-latest等',
        required: false,
        type: String,
        example: 'qwen-turbo'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TranslateNewsDto.prototype, "customModel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否自动提取主题词和类别，默认true。设为true时翻译完成后会自动提取主题词并识别内容类型（科普/军事），设为false时只进行翻译',
        default: true,
        required: false,
        example: true,
        type: Boolean
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Type)(() => Boolean),
    __metadata("design:type", Boolean)
], TranslateNewsDto.prototype, "autoExtractThemes", void 0);
exports.TranslateNewsDto = TranslateNewsDto;
class ExtractThemesDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每批处理的文档数量，建议根据文档大小适当设置，过大可能导致请求超时，过小会增加调用次数',
        default: 10,
        required: false,
        minimum: 1,
        maximum: 50,
        example: 20,
        type: Number
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], ExtractThemesDto.prototype, "batchSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最大处理文档数，0表示不限制处理数量，可用于限制单次API调用处理的总文档数量，控制执行时间',
        default: 0,
        required: false,
        minimum: 0,
        example: 100,
        type: Number
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], ExtractThemesDto.prototype, "maxDocs", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否强制重新提取已有主题词的文档，默认false只提取没有主题词的文档（包括中文和英文文档），设为true会重新提取所有文档的主题词和内容类型',
        default: false,
        required: false,
        example: false,
        type: Boolean
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Type)(() => Boolean),
    __metadata("design:type", Boolean)
], ExtractThemesDto.prototype, "forceReextract", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '指定要处理的索引名称数组，支持精确指定索引名称或使用通配符匹配，不指定则处理所有以news_开头的索引',
        required: false,
        isArray: true,
        type: [String],
        example: ['news_2023_04', 'news_2023_05']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ExtractThemesDto.prototype, "specificIndexes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '大模型模式选择：default（默认，qwen-turbo）、high_quality（高质量，qwen-max-latest）、fast（快速，qwen-turbo，更高并发）',
        required: false,
        enum: LLMMode,
        default: LLMMode.DEFAULT,
        example: LLMMode.DEFAULT
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(LLMMode),
    __metadata("design:type", String)
], ExtractThemesDto.prototype, "llmMode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '自定义模型名称，如果指定则覆盖llmMode设置，支持qwen-turbo、qwen-max-latest等',
        required: false,
        type: String,
        example: 'qwen-turbo'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExtractThemesDto.prototype, "customModel", void 0);
exports.ExtractThemesDto = ExtractThemesDto;
class HotThemesDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '返回前N个热门主题词',
        default: 10,
        required: false,
        minimum: 1,
        maximum: 100,
        example: 10,
        type: Number
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], HotThemesDto.prototype, "topN", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '主题词最小出现次数，用于过滤出现频率过低的主题词',
        default: 1,
        required: false,
        minimum: 1,
        example: 2,
        type: Number
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], HotThemesDto.prototype, "minCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '指定要处理的索引名称数组，支持精确指定索引名称或使用通配符匹配，不指定则处理所有以news_开头的索引',
        required: false,
        isArray: true,
        type: [String],
        example: ['news_2023_04', 'news_2023_05']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], HotThemesDto.prototype, "specificIndexes", void 0);
exports.HotThemesDto = HotThemesDto;
//# sourceMappingURL=news.dto.js.map