"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SatelliteQueryDto = exports.SortConfig = exports.FieldMatchConfig = exports.OrbitType = exports.MatchType = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
var MatchType;
(function (MatchType) {
    MatchType["EXACT"] = "exact";
    MatchType["FUZZY"] = "fuzzy";
})(MatchType = exports.MatchType || (exports.MatchType = {}));
var OrbitType;
(function (OrbitType) {
    OrbitType["LEO"] = "LEO";
    OrbitType["MEO"] = "MEO";
    OrbitType["GEO"] = "GEO";
})(OrbitType = exports.OrbitType || (exports.OrbitType = {}));
class FieldMatchConfig {
    constructor() {
        this.similarity_threshold = 0.6;
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '匹配类型',
        enum: MatchType,
        example: 'exact'
    }),
    (0, class_validator_1.IsEnum)(MatchType),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], FieldMatchConfig.prototype, "matchType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '字段值',
        example: 'HELIOS 2A',
        oneOf: [
            { type: 'string' },
            { type: 'number' }
        ]
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Object)
], FieldMatchConfig.prototype, "value", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '相似度阈值（0-1之间），只返回相似度高于该阈值的结果',
        required: false,
        default: 0.6,
        minimum: 0,
        maximum: 1,
        type: Number
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], FieldMatchConfig.prototype, "similarity_threshold", void 0);
exports.FieldMatchConfig = FieldMatchConfig;
class SortConfig {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '排序字段',
        example: 'update_time'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SortConfig.prototype, "field", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '排序方向',
        enum: ['asc', 'desc'],
        example: 'desc'
    }),
    (0, class_validator_1.IsEnum)(['asc', 'desc']),
    __metadata("design:type", String)
], SortConfig.prototype, "order", void 0);
exports.SortConfig = SortConfig;
class SatelliteQueryDto {
    constructor() {
        this.page = 1;
        this.limit = 10;
        this.similarity_threshold = 0.6;
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '页码',
        required: false,
        default: 1,
        minimum: 1
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], SatelliteQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每页数量',
        required: false,
        default: 10,
        minimum: 1
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], SatelliteQueryDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '关键词搜索',
        required: false,
        example: 'ISS',
        type: String
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteQueryDto.prototype, "keyword", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '相似度阈值（0-1之间），只返回相似度高于该阈值的结果',
        required: false,
        default: 0.6,
        minimum: 0,
        maximum: 1,
        type: Number
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], SatelliteQueryDto.prototype, "similarity_threshold", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '排序配置',
        required: false,
        type: SortConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SortConfig),
    __metadata("design:type", SortConfig)
], SatelliteQueryDto.prototype, "sort", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星名称查询配置',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], SatelliteQueryDto.prototype, "satellite_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星别名查询配置',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], SatelliteQueryDto.prototype, "alternative_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'COSPAR ID查询配置',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], SatelliteQueryDto.prototype, "cospar_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '注册国家查询配置',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], SatelliteQueryDto.prototype, "country_of_registry", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '所有者查询配置',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], SatelliteQueryDto.prototype, "owner", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '状态查询配置',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], SatelliteQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'NORAD ID查询配置',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], SatelliteQueryDto.prototype, "norad_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '发射日期查询配置',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], SatelliteQueryDto.prototype, "launch_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '部署日期查询配置',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], SatelliteQueryDto.prototype, "deployed_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '轨道类型',
        required: false,
        enum: OrbitType,
        example: 'LEO'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(OrbitType),
    __metadata("design:type", String)
], SatelliteQueryDto.prototype, "orbit_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最小轨道高度（km）',
        required: false,
        type: Number,
        example: 400
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], SatelliteQueryDto.prototype, "min_altitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最大轨道高度（km）',
        required: false,
        type: Number,
        example: 1000
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], SatelliteQueryDto.prototype, "max_altitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最小倾角（度）',
        required: false,
        type: Number,
        example: 45
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], SatelliteQueryDto.prototype, "min_inclination", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最大倾角（度）',
        required: false,
        type: Number,
        example: 90
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], SatelliteQueryDto.prototype, "max_inclination", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最小轨道周期（分钟）',
        required: false,
        type: Number,
        example: 90
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], SatelliteQueryDto.prototype, "min_period", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最大轨道周期（分钟）',
        required: false,
        type: Number,
        example: 120
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], SatelliteQueryDto.prototype, "max_period", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最小远地点高度（km）',
        required: false,
        type: Number,
        example: 400
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], SatelliteQueryDto.prototype, "min_apogee", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最大远地点高度（km）',
        required: false,
        type: Number,
        example: 1000
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], SatelliteQueryDto.prototype, "max_apogee", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最小近地点高度（km）',
        required: false,
        type: Number,
        example: 400
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], SatelliteQueryDto.prototype, "min_perigee", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最大近地点高度（km）',
        required: false,
        type: Number,
        example: 1000
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], SatelliteQueryDto.prototype, "max_perigee", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '发射日期开始',
        required: false,
        type: String,
        example: '2020-01-01'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], SatelliteQueryDto.prototype, "launch_date_start", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '发射日期结束',
        required: false,
        type: String,
        example: '2023-12-31'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], SatelliteQueryDto.prototype, "launch_date_end", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '发射场',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], SatelliteQueryDto.prototype, "launch_site", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '发射火箭',
        required: false,
        type: FieldMatchConfig
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FieldMatchConfig),
    __metadata("design:type", FieldMatchConfig)
], SatelliteQueryDto.prototype, "launch_vehicle", void 0);
exports.SatelliteQueryDto = SatelliteQueryDto;
//# sourceMappingURL=satellite-query.dto.js.map