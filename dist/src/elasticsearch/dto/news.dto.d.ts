export declare enum LLMMode {
    DEFAULT = "default",
    HIGH_QUALITY = "high_quality",
    FAST = "fast"
}
export declare class TranslateNewsDto {
    batchSize?: number;
    maxDocs?: number;
    forceRetranslate?: boolean;
    forceRefetchContent?: boolean;
    specificIndexes?: string[];
    llmMode?: LLMMode;
    customModel?: string;
    autoExtractThemes?: boolean;
}
export declare class ExtractThemesDto {
    batchSize?: number;
    maxDocs?: number;
    forceReextract?: boolean;
    specificIndexes?: string[];
    llmMode?: LLMMode;
    customModel?: string;
}
export declare class HotThemesDto {
    topN?: number;
    minCount?: number;
    specificIndexes?: string[];
}
