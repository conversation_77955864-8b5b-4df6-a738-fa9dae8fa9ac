export declare enum MatchType {
    EXACT = "exact",
    FUZZY = "fuzzy"
}
export declare enum OrbitType {
    LEO = "LEO",
    MEO = "MEO",
    GEO = "GEO"
}
export declare class FieldMatchConfig {
    matchType: MatchType;
    value: string | number;
    similarity_threshold?: number;
}
export declare class SortConfig {
    field: string;
    order: 'asc' | 'desc';
}
export declare class SatelliteQueryDto {
    page?: number;
    limit?: number;
    keyword?: string;
    similarity_threshold?: number;
    sort?: SortConfig;
    satellite_name?: FieldMatchConfig;
    alternative_name?: FieldMatchConfig;
    cospar_id?: FieldMatchConfig;
    country_of_registry?: FieldMatchConfig;
    owner?: FieldMatchConfig;
    status?: FieldMatchConfig;
    norad_id?: FieldMatchConfig;
    launch_date?: FieldMatchConfig;
    deployed_date?: FieldMatchConfig;
    orbit_type?: OrbitType;
    min_altitude?: number;
    max_altitude?: number;
    min_inclination?: number;
    max_inclination?: number;
    min_period?: number;
    max_period?: number;
    min_apogee?: number;
    max_apogee?: number;
    min_perigee?: number;
    max_perigee?: number;
    launch_date_start?: string;
    launch_date_end?: string;
    launch_site?: FieldMatchConfig;
    launch_vehicle?: FieldMatchConfig;
}
