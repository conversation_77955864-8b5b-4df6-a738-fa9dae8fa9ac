"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SatelliteService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SatelliteService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const satellite_entity_1 = require("../entities/satellite.entity");
const elasticsearch_satellite_service_1 = require("../elasticsearch/services/elasticsearch.satellite.service");
const aggregation_task_service_1 = require("./aggregation-task.service");
let SatelliteService = SatelliteService_1 = class SatelliteService {
    constructor(satelliteRepository, elasticsearchSatelliteService, aggregationTaskService) {
        this.satelliteRepository = satelliteRepository;
        this.elasticsearchSatelliteService = elasticsearchSatelliteService;
        this.aggregationTaskService = aggregationTaskService;
        this.logger = new common_1.Logger(SatelliteService_1.name);
    }
    async searchSatelliteInfoLocal(query) {
        try {
            this.logger.debug(`开始在本地数据库中查询卫星信息: ${JSON.stringify(query)}`);
            const qb = this.satelliteRepository.createQueryBuilder('satellite');
            if (query.keyword) {
                qb.where(`
          EXISTS (
            SELECT FROM jsonb_array_elements(satellite.satellite_name) AS name
            WHERE (name->>'value')::text ILIKE :keyword
          ) OR EXISTS (
            SELECT FROM jsonb_array_elements(satellite.alternative_name) AS name
            WHERE (name->>'value')::text ILIKE :keyword
          )
        `, { keyword: `%${query.keyword}%` });
            }
            if (query.norad_id) {
                qb.andWhere(`
          EXISTS (
            SELECT FROM jsonb_array_elements(satellite.norad_id) AS item
            WHERE (item->>'value')::text = :norad_id
          )
        `, { norad_id: query.norad_id });
            }
            if (query.cospar_id) {
                qb.andWhere(`
          EXISTS (
            SELECT FROM jsonb_array_elements(satellite.cospar_id) AS item
            WHERE (item->>'value')::text = :cospar_id
          )
        `, { cospar_id: query.cospar_id });
            }
            if (query.status) {
                qb.andWhere(`
          EXISTS (
            SELECT FROM jsonb_array_elements(satellite.status) AS item
            WHERE (item->>'value')::text ILIKE :status
          )
        `, { status: `%${query.status}%` });
            }
            const totalCount = await qb.getCount();
            const page = query.page || 1;
            const limit = query.limit || 10;
            const offset = (page - 1) * limit;
            if (query.sort_by && query.sort_dir) {
                qb.orderBy(`(
          SELECT (jsonb_array_elements(satellite.${query.sort_by})->>'value')::text 
          LIMIT 1
        )`, query.sort_dir === 'asc' ? 'ASC' : 'DESC');
            }
            else {
                qb.orderBy('satellite.id', 'DESC');
            }
            const satellites = await qb
                .skip(offset)
                .take(limit)
                .getMany();
            return {
                total: totalCount,
                page,
                limit,
                hits: satellites,
            };
        }
        catch (error) {
            this.logger.error(`本地数据库查询卫星信息失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async syncAllSatellitesFromES() {
        try {
            this.logger.log('开始从ES同步所有卫星数据到本地数据库');
            const satelliteNames = await this.elasticsearchSatelliteService.getSatelliteNames();
            this.logger.log(`共获取到 ${satelliteNames.length} 个卫星名称，开始同步`);
            const batchSize = 50;
            let processedCount = 0;
            for (let i = 0; i < satelliteNames.length; i += batchSize) {
                const batchNames = satelliteNames.slice(i, i + batchSize);
                await this.syncSatelliteBatch(batchNames);
                processedCount += batchNames.length;
                this.logger.log(`已同步 ${processedCount} / ${satelliteNames.length} 个卫星数据`);
            }
            this.logger.log('所有卫星数据同步完成');
        }
        catch (error) {
            this.logger.error(`同步卫星数据失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async syncSatelliteBatch(satelliteNames) {
        for (const name of satelliteNames) {
            try {
                await this.syncSatelliteByName(name);
            }
            catch (error) {
                this.logger.error(`同步卫星 "${name}" 失败: ${error.message}`);
            }
        }
    }
    async syncSatelliteByName(satelliteName) {
        try {
            this.logger.debug(`开始同步卫星 "${satelliteName}" 的数据`);
            const result = await this.elasticsearchSatelliteService.searchSatelliteInfo({
                keyword: satelliteName,
                similarity_threshold: 0.8,
            });
            if (!result || !result.hits || result.hits.length === 0) {
                this.logger.warn(`未找到卫星 "${satelliteName}" 的数据`);
                return;
            }
            for (const hit of result.hits) {
                const docKey = this.generateDocumentKey(hit);
                const existingSatellite = await this.findSatelliteByKey(docKey);
                if (existingSatellite) {
                    this.logger.debug(`更新卫星 "${satelliteName}" 的数据`);
                    hit.id = existingSatellite.id;
                    await this.satelliteRepository.save(hit);
                }
                else {
                    this.logger.debug(`创建卫星 "${satelliteName}" 的新记录`);
                    await this.satelliteRepository.save(hit);
                }
            }
            this.logger.debug(`卫星 "${satelliteName}" 的数据同步完成`);
        }
        catch (error) {
            this.logger.error(`同步卫星 "${satelliteName}" 失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findSatelliteByKey(docKey) {
        const [noradId, cosparId, name] = docKey.split('|');
        const qb = this.satelliteRepository.createQueryBuilder('satellite');
        if (noradId && noradId !== 'null') {
            qb.andWhere(`
        EXISTS (
          SELECT FROM jsonb_array_elements(satellite.norad_id) AS item
          WHERE (item->>'value')::text = :noradId
        )
      `, { noradId });
        }
        if (cosparId && cosparId !== 'null') {
            qb.andWhere(`
        EXISTS (
          SELECT FROM jsonb_array_elements(satellite.cospar_id) AS item
          WHERE (item->>'value')::text = :cosparId
        )
      `, { cosparId });
        }
        if (name && name !== 'null') {
            qb.andWhere(`
        EXISTS (
          SELECT FROM jsonb_array_elements(satellite.satellite_name) AS item
          WHERE (item->>'value')::text = :name
        )
      `, { name });
        }
        const result = await qb.getOne();
        return result || undefined;
    }
    generateDocumentKey(doc) {
        let noradId = 'null';
        let cosparId = 'null';
        let name = 'null';
        if (doc.norad_id && doc.norad_id.length > 0 && doc.norad_id[0].value) {
            noradId = doc.norad_id[0].value;
        }
        if (doc.cospar_id && doc.cospar_id.length > 0 && doc.cospar_id[0].value) {
            cosparId = doc.cospar_id[0].value;
        }
        if (doc.satellite_name && doc.satellite_name.length > 0 && doc.satellite_name[0].value) {
            name = doc.satellite_name[0].value;
        }
        return `${noradId}|${cosparId}|${name}`;
    }
    async getSatelliteNamesLocal() {
        try {
            this.logger.debug('从本地数据库获取卫星名称集合');
            const result = await this.satelliteRepository.query(`
        SELECT DISTINCT jsonb_array_elements(satellite_name)->>'value' as name
        FROM satellites
        WHERE jsonb_array_length(satellite_name) > 0
        UNION
        SELECT DISTINCT jsonb_array_elements(alternative_name)->>'value' as name
        FROM satellites
        WHERE jsonb_array_length(alternative_name) > 0
      `);
            const names = result
                .map((item) => item.name)
                .filter((name) => name && name !== 'None' && name !== 'null' && name !== 'undefined')
                .sort();
            this.logger.debug(`从本地数据库获取到 ${names.length} 个卫星名称`);
            return names;
        }
        catch (error) {
            this.logger.error(`从本地数据库获取卫星名称失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getSatelliteStatusesLocal() {
        try {
            this.logger.debug('从本地数据库获取卫星状态集合');
            const result = await this.satelliteRepository.query(`
        SELECT DISTINCT jsonb_array_elements(status)->>'value' as status
        FROM satellites
        WHERE jsonb_array_length(status) > 0
      `);
            const statusMap = {
                'ACTIVE': '活跃',
                'ALIVE': '活跃',
                'OPERATIONAL': '运行中',
                'DEAD': '失效',
                'DECAYED': '已衰减',
                'DEPLOYMENT': '部署',
                'DEPLOYMENT FAILURE': '部署失败',
                'DEPLOYMENT PROHIBITED': '禁止部署',
                'FUTURE': '计划中',
                'IN ORBIT': '在轨道上',
                'LAUNCH FAILURE': '发射失败',
                'NO SIGNAL': '无信号',
                'NOT LAUNCHED': '未发射',
                'ON SPACECRAFT': '在航天器上',
                'REENTRY': '再入大气层',
                'CANCELLED': '已取消'
            };
            const statuses = result
                .map((item) => {
                const status = item.status;
                if (!status || status === 'None' || status === 'null' || status === 'undefined') {
                    return null;
                }
                const normalizedStatus = status.toUpperCase();
                let zh = statusMap[normalizedStatus] || '未知';
                return { en: status, zh };
            })
                .filter((item) => item !== null)
                .sort((a, b) => a.en.localeCompare(b.en));
            this.logger.debug(`从本地数据库获取到 ${statuses.length} 个卫星状态`);
            return statuses;
        }
        catch (error) {
            this.logger.error(`从本地数据库获取卫星状态失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async aggregateSatelliteData(options) {
        let task = null;
        try {
            this.logger.log('开始聚合卫星数据');
            const saveToDatabase = (options === null || options === void 0 ? void 0 : options.saveToDatabase) !== false;
            const limit = (options === null || options === void 0 ? void 0 : options.limit) || 0;
            const keyword = (options === null || options === void 0 ? void 0 : options.keyword) || '';
            let taskType = 'full';
            if (limit > 0) {
                taskType = 'partial';
            }
            if (keyword) {
                taskType = 'keyword';
            }
            task = await this.aggregationTaskService.createTask({
                task_type: taskType,
                parameters: options || {},
            });
            await this.aggregationTaskService.startTask(task.id);
            this.logger.log(`创建聚合任务记录，ID: ${task.id}, 类型: ${taskType}`);
            const indices = [
                'satsinfo_gunter',
                'satsinfo_n2yo',
                'satsinfo_nanosats',
                'satsinfo_satnogs',
                'satsinfo_ucs'
            ];
            const query = {
                bool: {
                    must: []
                }
            };
            if (keyword) {
                query.bool.must.push({
                    multi_match: {
                        query: keyword,
                        fields: [
                            'satellite_name^3',
                            'alternative_name^2',
                            'cospar_id^2',
                            'norad_id^2',
                            'owner',
                            'purpose',
                            'detailed_purpose',
                            'country_of_registry'
                        ],
                        type: 'best_fields',
                        fuzziness: 'AUTO'
                    }
                });
            }
            let allSearchResults = [];
            const batchSize = 1000;
            this.logger.log('使用Scroll API获取所有卫星数据');
            for (const index of indices) {
                this.logger.log(`开始处理索引: ${index}`);
                try {
                    const indexResults = await this.elasticsearchSatelliteService.scrollSearch({
                        indices: [index],
                        query,
                        size: batchSize
                    });
                    this.logger.log(`从索引 ${index} 获取了 ${indexResults.length} 条记录`);
                    allSearchResults = allSearchResults.concat(indexResults);
                    if (limit > 0 && allSearchResults.length >= limit) {
                        this.logger.log(`已达到用户设置的限制 ${limit}，停止获取更多数据`);
                        allSearchResults = allSearchResults.slice(0, limit);
                        break;
                    }
                    const progress = limit > 0 ? Math.min(50, (allSearchResults.length / limit) * 50) : 25;
                    await this.aggregationTaskService.updateTaskProgress(task.id, progress, allSearchResults.length);
                }
                catch (error) {
                    this.logger.error(`处理索引 ${index} 时出错: ${error.message}`, error.stack);
                }
            }
            this.logger.log(`所有索引处理完成，总共获取 ${allSearchResults.length} 条记录`);
            if (allSearchResults.length === 0) {
                this.logger.log('未找到符合条件的卫星数据');
                await this.aggregationTaskService.completeTask(task.id, 0);
                return { success: true, totalAggregated: 0, message: '未找到符合条件的卫星数据' };
            }
            this.logger.log('开始对卫星数据进行分组和合并');
            const satelliteGroups = [];
            for (const record of allSearchResults) {
                const source = record._source;
                const index = record._index;
                source._index = index;
                const noradId = String(source.norad_id || '').trim();
                const cosparId = String(source.cospar_id || '').trim();
                const satelliteName = String(source.satellite_name || '').trim();
                let foundMatch = false;
                for (let i = 0; i < satelliteGroups.length; i++) {
                    const group = satelliteGroups[i];
                    if (this.canMergeWithGroup(source, group)) {
                        group.push(source);
                        foundMatch = true;
                        break;
                    }
                }
                if (!foundMatch) {
                    satelliteGroups.push([source]);
                }
                if (satelliteGroups.length % 1000 === 0) {
                    this.logger.log(`已处理 ${satelliteGroups.length} 组卫星数据`);
                }
            }
            this.logger.log(`根据标识字段分组后，共有 ${satelliteGroups.length} 组卫星数据`);
            const aggregatedSatellites = [];
            for (let groupIndex = 0; groupIndex < satelliteGroups.length; groupIndex++) {
                const groupRecords = satelliteGroups[groupIndex];
                if ((groupIndex + 1) % 1000 === 0) {
                    this.logger.log(`已聚合 ${groupIndex + 1}/${satelliteGroups.length} 组卫星数据`);
                }
                const aggregatedSatellite = {
                    satellite_name: [],
                    alternative_name: [],
                    cospar_id: [],
                    country_of_registry: [],
                    owner: [],
                    users: [],
                    status: [],
                    norad_id: [],
                    launch_info: [],
                    orbit_info: [],
                    mass_kg: [],
                    power_watts: [],
                    lifetime_years: [],
                    contractor: [],
                    purpose: [],
                    detailed_purpose: [],
                    payload: [],
                    payload_description: [],
                    update_time: [],
                    _sources: []
                };
                const fieldValues = new Map();
                for (const record of groupRecords) {
                    const source = record._index.replace('satsinfo_', '');
                    if (!aggregatedSatellite._sources.includes(source)) {
                        aggregatedSatellite._sources.push(source);
                    }
                    for (const field of Object.keys(aggregatedSatellite)) {
                        if (field === '_sources' || field.startsWith('_'))
                            continue;
                        const value = record[field];
                        if (value !== undefined && value !== null && value !== '') {
                            if (!fieldValues.has(field)) {
                                fieldValues.set(field, new Set());
                            }
                            const valueStr = JSON.stringify(value);
                            if (!fieldValues.get(field).has(valueStr)) {
                                fieldValues.get(field).add(valueStr);
                                aggregatedSatellite[field].push({
                                    value,
                                    sources: [source]
                                });
                            }
                            else {
                                const existingItem = aggregatedSatellite[field].find((item) => JSON.stringify(item.value) === valueStr);
                                if (existingItem && !existingItem.sources.includes(source)) {
                                    existingItem.sources.push(source);
                                }
                            }
                        }
                    }
                }
                aggregatedSatellites.push(aggregatedSatellite);
            }
            this.logger.log(`聚合完成，共 ${aggregatedSatellites.length} 条卫星数据`);
            await this.aggregationTaskService.updateTaskProgress(task.id, 75, allSearchResults.length);
            if (!saveToDatabase) {
                return {
                    success: true,
                    totalAggregated: aggregatedSatellites.length,
                    message: `成功聚合 ${aggregatedSatellites.length} 条卫星数据（未保存到数据库）`
                };
            }
            this.logger.log(`开始将聚合结果保存到数据库`);
            const saveBatchSize = 500;
            let savedCount = 0;
            for (let i = 0; i < aggregatedSatellites.length; i += saveBatchSize) {
                const batch = aggregatedSatellites.slice(i, i + saveBatchSize);
                const entities = batch.map(data => {
                    const entity = new satellite_entity_1.Satellite();
                    for (const field of Object.keys(data)) {
                        entity[field] = data[field];
                    }
                    return entity;
                });
                await this.satelliteRepository.save(entities);
                savedCount += batch.length;
                this.logger.log(`已保存 ${savedCount}/${aggregatedSatellites.length} 条卫星数据到数据库`);
            }
            this.logger.log(`成功将 ${savedCount} 条聚合后的卫星数据保存到数据库`);
            this.logger.log(`卫星数据聚合完成，共处理 ${allSearchResults.length} 条记录，聚合为 ${aggregatedSatellites.length} 条记录`);
            await this.aggregationTaskService.completeTask(task.id, aggregatedSatellites.length);
            return {
                success: true,
                totalAggregated: aggregatedSatellites.length,
                message: `成功聚合 ${aggregatedSatellites.length} 条卫星数据`
            };
        }
        catch (error) {
            this.logger.error(`聚合卫星数据失败: ${error.message}`, error.stack);
            if (task && task.id) {
                await this.aggregationTaskService.failTask(task.id, error.message);
            }
            throw new Error(`聚合卫星数据失败: ${error.message}`);
        }
    }
    canMergeWithGroup(record, group) {
        if (!record || !group || group.length === 0)
            return false;
        const recordNoradId = String(record.norad_id || '').trim();
        const recordCosparId = String(record.cospar_id || '').trim();
        const recordName = String(record.satellite_name || '').trim();
        for (const groupRecord of group) {
            const groupNoradId = String(groupRecord.norad_id || '').trim();
            if (recordNoradId && groupNoradId && recordNoradId !== groupNoradId) {
                return false;
            }
        }
        for (const groupRecord of group) {
            const groupNoradId = String(groupRecord.norad_id || '').trim();
            const groupCosparId = String(groupRecord.cospar_id || '').trim();
            if ((!recordNoradId || !groupNoradId) &&
                recordCosparId && groupCosparId &&
                recordCosparId !== groupCosparId) {
                return false;
            }
        }
        let anyGroupNoradIdEmpty = false;
        let anyGroupCosparIdEmpty = false;
        for (const groupRecord of group) {
            const groupNoradId = String(groupRecord.norad_id || '').trim();
            const groupCosparId = String(groupRecord.cospar_id || '').trim();
            const groupName = String(groupRecord.satellite_name || '').trim();
            if (!groupNoradId)
                anyGroupNoradIdEmpty = true;
            if (!groupCosparId)
                anyGroupCosparIdEmpty = true;
            if (recordNoradId && groupNoradId) {
                if (recordNoradId === groupNoradId) {
                    return true;
                }
            }
        }
        if (!recordNoradId || anyGroupNoradIdEmpty) {
            for (const groupRecord of group) {
                const groupNoradId = String(groupRecord.norad_id || '').trim();
                const groupCosparId = String(groupRecord.cospar_id || '').trim();
                if (groupNoradId && recordNoradId && groupNoradId !== recordNoradId) {
                    continue;
                }
                if (recordCosparId && groupCosparId) {
                    if (recordCosparId === groupCosparId) {
                        return true;
                    }
                }
            }
        }
        if ((!recordCosparId || anyGroupCosparIdEmpty) && (!recordNoradId || anyGroupNoradIdEmpty)) {
            for (const groupRecord of group) {
                const groupNoradId = String(groupRecord.norad_id || '').trim();
                const groupCosparId = String(groupRecord.cospar_id || '').trim();
                const groupName = String(groupRecord.satellite_name || '').trim();
                if (groupNoradId && recordNoradId && groupNoradId !== recordNoradId) {
                    continue;
                }
                if (groupCosparId && recordCosparId && groupCosparId !== recordCosparId) {
                    continue;
                }
                if (recordName && groupName) {
                    if (recordName === groupName) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
    async findSatelliteByNoradId(noradId) {
        if (!noradId)
            return undefined;
        const qb = this.satelliteRepository.createQueryBuilder('satellite');
        qb.where(`
      EXISTS (
        SELECT FROM jsonb_array_elements(satellite.norad_id) AS item
        WHERE (item->>'value')::text = :noradId
      )
    `, { noradId });
        const result = await qb.getOne();
        return result || undefined;
    }
    async findSatelliteByCosparId(cosparId) {
        if (!cosparId)
            return undefined;
        const qb = this.satelliteRepository.createQueryBuilder('satellite');
        qb.where(`
      EXISTS (
        SELECT FROM jsonb_array_elements(satellite.cospar_id) AS item
        WHERE (item->>'value')::text = :cosparId
      )
    `, { cosparId });
        const result = await qb.getOne();
        return result || undefined;
    }
    async findSatelliteByName(name) {
        if (!name)
            return undefined;
        const qb = this.satelliteRepository.createQueryBuilder('satellite');
        qb.where(`
      EXISTS (
        SELECT FROM jsonb_array_elements(satellite.satellite_name) AS item
        WHERE (item->>'value')::text = :name
      )
    `, { name });
        const result = await qb.getOne();
        return result || undefined;
    }
    async getSatelliteById(id) {
        try {
            if (id === null || id === undefined || isNaN(id)) {
                this.logger.warn(`无效的卫星ID: ${id}`);
                throw new Error(`无效的卫星ID: ${id}`);
            }
            this.logger.debug(`根据ID获取卫星详细信息: ${id}`);
            const satellite = await this.satelliteRepository.findOne({ where: { id } });
            if (!satellite) {
                this.logger.warn(`未找到ID为 ${id} 的卫星信息`);
                throw new Error(`未找到ID为 ${id} 的卫星信息`);
            }
            this.logger.debug(`成功获取ID为 ${id} 的卫星信息`);
            return satellite;
        }
        catch (error) {
            this.logger.error(`获取卫星详细信息失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async clearSatelliteData() {
        try {
            this.logger.log('开始清空卫星数据表');
            const result = await this.satelliteRepository.clear();
            this.logger.log('卫星数据表清空完成');
            return {
                success: true,
                message: '卫星数据表清空成功'
            };
        }
        catch (error) {
            this.logger.error(`清空卫星数据表失败: ${error.message}`, error.stack);
            return {
                success: false,
                message: `清空失败: ${error.message}`
            };
        }
    }
    async testAggregationLogic() {
        try {
            this.logger.log('开始测试卫星数据聚合逻辑');
            await this.clearSatelliteData();
            this.logger.log('已清空数据库，准备创建测试数据');
            const testData = [
                {
                    _index: 'satsinfo_n2yo',
                    _source: {
                        norad_id: '25544',
                        cospar_id: '1998-067A',
                        satellite_name: 'ISS (ZARYA)',
                        status: 'active'
                    }
                },
                {
                    _index: 'satsinfo_satnogs',
                    _source: {
                        norad_id: '25544',
                        satellite_name: 'International Space Station',
                        status: 'operational'
                    }
                },
                {
                    _index: 'satsinfo_gunter',
                    _source: {
                        cospar_id: '2020-001A',
                        satellite_name: 'Starlink-1',
                        status: 'active'
                    }
                },
                {
                    _index: 'satsinfo_ucs',
                    _source: {
                        cospar_id: '2020-001A',
                        satellite_name: 'Starlink Group 1',
                        purpose: 'Communications'
                    }
                },
                {
                    _index: 'satsinfo_n2yo',
                    _source: {
                        norad_id: '12345',
                        cospar_id: '1990-001A',
                        satellite_name: 'GPS IIR-1',
                        status: 'active'
                    }
                },
                {
                    _index: 'satsinfo_ucs',
                    _source: {
                        norad_id: '54321',
                        cospar_id: '1990-002A',
                        satellite_name: 'GPS IIR-1',
                        purpose: 'Navigation'
                    }
                },
                {
                    _index: 'satsinfo_nanosats',
                    _source: {
                        satellite_name: 'CubeSat-1',
                        status: 'planned'
                    }
                },
                {
                    _index: 'satsinfo_gunter',
                    _source: {
                        norad_id: '98765',
                        satellite_name: 'CubeSat-1',
                        purpose: 'Technology demonstration'
                    }
                },
                {
                    _index: 'satsinfo_n2yo',
                    _source: {
                        norad_id: '11111',
                        cospar_id: '2000-001A',
                        satellite_name: 'Unique Satellite',
                        status: 'active'
                    }
                }
            ];
            const mockSearchResults = testData.map(item => ({
                _index: item._index,
                _source: item._source
            }));
            const originalSearchRawData = this.elasticsearchSatelliteService.searchRawData;
            const originalCanMergeWithGroup = this.canMergeWithGroup;
            this.canMergeWithGroup = (record, group) => {
                const recordNoradId = String(record.norad_id || '').trim();
                const recordCosparId = String(record.cospar_id || '').trim();
                const recordName = String(record.satellite_name || '').trim();
                this.logger.debug(`尝试匹配记录: norad_id=${recordNoradId}, cospar_id=${recordCosparId}, name=${recordName}`);
                let anyGroupNoradIdEmpty = false;
                let anyGroupCosparIdEmpty = false;
                for (const groupRecord of group) {
                    const groupNoradId = String(groupRecord.norad_id || '').trim();
                    const groupCosparId = String(groupRecord.cospar_id || '').trim();
                    const groupName = String(groupRecord.satellite_name || '').trim();
                    this.logger.debug(`与组记录比较: norad_id=${groupNoradId}, cospar_id=${groupCosparId}, name=${groupName}`);
                    if (!groupNoradId)
                        anyGroupNoradIdEmpty = true;
                    if (!groupCosparId)
                        anyGroupCosparIdEmpty = true;
                    if (recordNoradId && groupNoradId) {
                        if (recordNoradId === groupNoradId) {
                            this.logger.debug(`匹配成功: norad_id相同 (${recordNoradId})`);
                            return true;
                        }
                        this.logger.debug(`norad_id不同，继续检查下一条记录`);
                        continue;
                    }
                }
                if (!recordNoradId || anyGroupNoradIdEmpty) {
                    this.logger.debug(`检查cospar_id匹配: recordNoradId=${recordNoradId}, anyGroupNoradIdEmpty=${anyGroupNoradIdEmpty}`);
                    for (const groupRecord of group) {
                        const groupNoradId = String(groupRecord.norad_id || '').trim();
                        const groupCosparId = String(groupRecord.cospar_id || '').trim();
                        if (groupNoradId && recordNoradId && groupNoradId !== recordNoradId) {
                            this.logger.debug(`norad_id不同，跳过此记录`);
                            continue;
                        }
                        if (recordCosparId && groupCosparId) {
                            if (recordCosparId === groupCosparId) {
                                this.logger.debug(`匹配成功: cospar_id相同 (${recordCosparId})`);
                                return true;
                            }
                            this.logger.debug(`cospar_id不同，继续检查下一条记录`);
                            continue;
                        }
                    }
                }
                if ((!recordCosparId || anyGroupCosparIdEmpty) && (!recordNoradId || anyGroupNoradIdEmpty)) {
                    this.logger.debug(`检查satellite_name匹配: recordCosparId=${recordCosparId}, anyGroupCosparIdEmpty=${anyGroupCosparIdEmpty}`);
                    for (const groupRecord of group) {
                        const groupNoradId = String(groupRecord.norad_id || '').trim();
                        const groupCosparId = String(groupRecord.cospar_id || '').trim();
                        const groupName = String(groupRecord.satellite_name || '').trim();
                        if (groupNoradId && recordNoradId && groupNoradId !== recordNoradId) {
                            this.logger.debug(`norad_id不同，跳过此记录`);
                            continue;
                        }
                        if (groupCosparId && recordCosparId && groupCosparId !== recordCosparId) {
                            this.logger.debug(`cospar_id不同，跳过此记录`);
                            continue;
                        }
                        if (recordName && groupName) {
                            if (recordName === groupName) {
                                this.logger.debug(`匹配成功: satellite_name相同 (${recordName})`);
                                return true;
                            }
                            this.logger.debug(`satellite_name不同，不能合并`);
                        }
                    }
                }
                this.logger.debug(`未找到匹配，不能合并`);
                return false;
            };
            this.elasticsearchSatelliteService.searchRawData = async () => {
                return mockSearchResults;
            };
            this.logger.log('开始执行聚合操作');
            const result = await this.aggregateSatelliteData({
                saveToDatabase: true
            });
            this.elasticsearchSatelliteService.searchRawData = originalSearchRawData;
            this.canMergeWithGroup = originalCanMergeWithGroup;
            this.logger.log(`聚合操作完成，结果: ${JSON.stringify(result)}`);
            const satellites = await this.satelliteRepository.find();
            this.logger.log(`数据库中的卫星记录数: ${satellites.length}`);
            for (let i = 0; i < satellites.length; i++) {
                const satellite = satellites[i];
                this.logger.log(`\n卫星记录 #${i + 1}:`);
                this.logger.log(`数据源: ${JSON.stringify(satellite._sources)}`);
                if (satellite.norad_id && satellite.norad_id.length > 0) {
                    this.logger.log(`NORAD ID: ${JSON.stringify(satellite.norad_id)}`);
                }
                if (satellite.cospar_id && satellite.cospar_id.length > 0) {
                    this.logger.log(`COSPAR ID: ${JSON.stringify(satellite.cospar_id)}`);
                }
                if (satellite.satellite_name && satellite.satellite_name.length > 0) {
                    this.logger.log(`卫星名称: ${JSON.stringify(satellite.satellite_name)}`);
                }
            }
            const iss = satellites.find(s => s.norad_id &&
                s.norad_id.some(n => n.value === '25544'));
            if (iss) {
                this.logger.log('\n验证测试用例1 (相同的norad_id):');
                this.logger.log(`找到ISS卫星记录，数据源: ${JSON.stringify(iss._sources)}`);
                const hasN2yo = iss._sources.includes('n2yo');
                const hasSatnogs = iss._sources.includes('satnogs');
                this.logger.log(`包含n2yo数据源: ${hasN2yo}`);
                this.logger.log(`包含satnogs数据源: ${hasSatnogs}`);
                if (!hasN2yo || !hasSatnogs) {
                    throw new Error('测试用例1失败: ISS卫星记录未包含所有预期的数据源');
                }
            }
            else {
                throw new Error('测试用例1失败: 未找到ISS卫星记录');
            }
            const starlink = satellites.find(s => s.cospar_id &&
                s.cospar_id.some(c => c.value === '2020-001A'));
            if (starlink) {
                this.logger.log('\n验证测试用例2 (相同的cospar_id):');
                this.logger.log(`找到Starlink卫星记录，数据源: ${JSON.stringify(starlink._sources)}`);
                const hasGunter = starlink._sources.includes('gunter');
                const hasUcs = starlink._sources.includes('ucs');
                this.logger.log(`包含gunter数据源: ${hasGunter}`);
                this.logger.log(`包含ucs数据源: ${hasUcs}`);
                if (!hasGunter || !hasUcs) {
                    throw new Error('测试用例2失败: Starlink卫星记录未包含所有预期的数据源');
                }
            }
            else {
                throw new Error('测试用例2失败: 未找到Starlink卫星记录');
            }
            const gpsRecords = satellites.filter(s => s.satellite_name &&
                s.satellite_name.some(n => n.value === 'GPS IIR-1'));
            this.logger.log('\n验证测试用例3 (不同的norad_id):');
            this.logger.log(`找到 ${gpsRecords.length} 条GPS卫星记录`);
            if (gpsRecords.length !== 2) {
                throw new Error(`测试用例3失败: 应该有2条独立的GPS卫星记录，实际有 ${gpsRecords.length} 条`);
            }
            const cubesatRecords = satellites.filter(s => s.satellite_name &&
                s.satellite_name.some(n => n.value === 'CubeSat-1'));
            this.logger.log('\n验证测试用例4 (相同的名称，一个有norad_id):');
            this.logger.log(`找到 ${cubesatRecords.length} 条CubeSat卫星记录`);
            if (cubesatRecords.length !== 1) {
                throw new Error(`测试用例4失败: 应该有1条聚合的CubeSat卫星记录，实际有 ${cubesatRecords.length} 条`);
            }
            const uniqueSatellite = satellites.find(s => s.satellite_name &&
                s.satellite_name.some(n => n.value === 'Unique Satellite'));
            if (uniqueSatellite) {
                this.logger.log('\n验证测试用例5 (完全不同的卫星):');
                this.logger.log(`找到Unique Satellite卫星记录，数据源: ${JSON.stringify(uniqueSatellite._sources)}`);
                if (uniqueSatellite._sources.length !== 1 || !uniqueSatellite._sources.includes('n2yo')) {
                    throw new Error('测试用例5失败: Unique Satellite卫星记录应该只包含n2yo数据源');
                }
            }
            else {
                throw new Error('测试用例5失败: 未找到Unique Satellite卫星记录');
            }
            this.logger.log('所有测试用例验证通过');
            return {
                success: true,
                message: '卫星数据聚合逻辑测试成功，所有测试用例通过'
            };
        }
        catch (error) {
            this.logger.error(`卫星数据聚合逻辑测试失败: ${error.message}`, error.stack);
            return {
                success: false,
                message: `卫星数据聚合逻辑测试失败: ${error.message}`
            };
        }
    }
    async testCustomAggregation(testDataFile) {
        try {
            this.logger.log(`开始测试自定义卫星数据聚合逻辑，使用文件: ${testDataFile}`);
            await this.clearSatelliteData();
            this.logger.log('已清空数据库，准备加载测试数据');
            const fs = require('fs');
            const testData = JSON.parse(fs.readFileSync(testDataFile, 'utf8'));
            this.logger.log(`成功加载测试数据，共 ${testData.length} 条记录`);
            const originalSearchRawData = this.elasticsearchSatelliteService.searchRawData;
            this.elasticsearchSatelliteService.searchRawData = async () => {
                return testData;
            };
            this.logger.log('开始执行聚合操作');
            const result = await this.aggregateSatelliteData({
                saveToDatabase: true
            });
            this.elasticsearchSatelliteService.searchRawData = originalSearchRawData;
            this.logger.log(`聚合操作完成，结果: ${JSON.stringify(result)}`);
            const satellites = await this.satelliteRepository.find();
            this.logger.log(`数据库中的卫星记录数: ${satellites.length}`);
            for (let i = 0; i < satellites.length; i++) {
                const satellite = satellites[i];
                this.logger.log(`\n卫星记录 #${i + 1}:`);
                this.logger.log(`数据源: ${JSON.stringify(satellite._sources)}`);
                if (satellite.norad_id && satellite.norad_id.length > 0) {
                    this.logger.log(`NORAD ID: ${JSON.stringify(satellite.norad_id)}`);
                }
                if (satellite.cospar_id && satellite.cospar_id.length > 0) {
                    this.logger.log(`COSPAR ID: ${JSON.stringify(satellite.cospar_id)}`);
                }
                if (satellite.satellite_name && satellite.satellite_name.length > 0) {
                    this.logger.log(`卫星名称: ${JSON.stringify(satellite.satellite_name)}`);
                }
            }
            const onewebSatellite = satellites.find(s => s.norad_id &&
                s.norad_id.some(n => String(n.value) === '55803'));
            if (onewebSatellite) {
                this.logger.log('\n验证OneWeb-0639卫星聚合:');
                this.logger.log(`找到OneWeb-0639卫星记录，数据源: ${JSON.stringify(onewebSatellite._sources)}`);
                const hasSatnogs = onewebSatellite._sources.includes('satnogs');
                const hasN2yo = onewebSatellite._sources.includes('n2yo');
                const hasUcs = onewebSatellite._sources.includes('ucs');
                this.logger.log(`包含satnogs数据源: ${hasSatnogs}`);
                this.logger.log(`包含n2yo数据源: ${hasN2yo}`);
                this.logger.log(`包含ucs数据源: ${hasUcs}`);
                if (!hasSatnogs || !hasN2yo || !hasUcs) {
                    throw new Error('OneWeb-0639卫星记录未包含所有预期的数据源');
                }
                const hasCorrectNoradId = onewebSatellite.norad_id.some(n => String(n.value) === '55803');
                if (!hasCorrectNoradId) {
                    throw new Error('OneWeb-0639卫星记录未包含正确的NORAD ID');
                }
                const hasCorrectCosparId = onewebSatellite.cospar_id.some(c => String(c.value) === '2023-029H');
                if (!hasCorrectCosparId) {
                    throw new Error('OneWeb-0639卫星记录未包含正确的COSPAR ID');
                }
                this.logger.log('OneWeb-0639卫星聚合验证通过');
                return {
                    success: true,
                    message: '自定义卫星数据聚合测试成功',
                    satellite: onewebSatellite
                };
            }
            else {
                this.logger.error('未找到OneWeb-0639卫星记录，当前所有卫星的NORAD ID:');
                for (const satellite of satellites) {
                    if (satellite.norad_id) {
                        this.logger.error(`卫星ID ${satellite.id} 的NORAD ID: ${JSON.stringify(satellite.norad_id)}`);
                    }
                }
                throw new Error('未找到OneWeb-0639卫星记录');
            }
        }
        catch (error) {
            this.logger.error(`自定义卫星数据聚合测试失败: ${error.message}`, error.stack);
            return {
                success: false,
                message: `自定义卫星数据聚合测试失败: ${error.message}`
            };
        }
    }
    async incrementalAggregateSatelliteData(options) {
        let task = null;
        try {
            this.logger.log('开始增量聚合卫星数据');
            const saveToDatabase = (options === null || options === void 0 ? void 0 : options.saveToDatabase) !== false;
            task = await this.aggregationTaskService.createTask({
                task_type: 'incremental',
                parameters: options || {},
            });
            await this.aggregationTaskService.startTask(task.id);
            this.logger.log(`创建增量聚合任务记录，ID: ${task.id}`);
            const indices = [
                'satsinfo_gunter',
                'satsinfo_n2yo',
                'satsinfo_nanosats',
                'satsinfo_satnogs',
                'satsinfo_ucs'
            ];
            const query = {
                bool: {
                    must: []
                }
            };
            this.logger.log('从本地数据库获取现有卫星的标识信息');
            const existingSatellites = await this.satelliteRepository
                .createQueryBuilder('satellite')
                .select([
                'satellite.id',
                'satellite.norad_id',
                'satellite.cospar_id',
                'satellite.satellite_name',
                'satellite.alternative_name'
            ])
                .getMany();
            this.logger.log(`从本地数据库获取到 ${existingSatellites.length} 条卫星记录`);
            const existingNoradIds = new Set();
            const existingCosparIds = new Set();
            const existingSatelliteNames = new Set();
            const existingAlternativeNames = new Set();
            for (const satellite of existingSatellites) {
                if (satellite.norad_id && Array.isArray(satellite.norad_id)) {
                    for (const item of satellite.norad_id) {
                        if (item.value) {
                            existingNoradIds.add(String(item.value).trim().toLowerCase());
                        }
                    }
                }
                if (satellite.cospar_id && Array.isArray(satellite.cospar_id)) {
                    for (const item of satellite.cospar_id) {
                        if (item.value) {
                            existingCosparIds.add(String(item.value).trim().toLowerCase());
                        }
                    }
                }
                if (satellite.satellite_name && Array.isArray(satellite.satellite_name)) {
                    for (const item of satellite.satellite_name) {
                        if (item.value) {
                            existingSatelliteNames.add(String(item.value).trim().toLowerCase());
                        }
                    }
                }
                if (satellite.alternative_name && Array.isArray(satellite.alternative_name)) {
                    for (const item of satellite.alternative_name) {
                        if (item.value) {
                            existingAlternativeNames.add(String(item.value).trim().toLowerCase());
                        }
                    }
                }
            }
            this.logger.log(`创建查找表完成: ${existingNoradIds.size} 个NORAD ID, ${existingCosparIds.size} 个COSPAR ID, ${existingSatelliteNames.size} 个卫星名称, ${existingAlternativeNames.size} 个卫星别名`);
            let allSearchResults = [];
            const batchSize = 1000;
            this.logger.log('使用Scroll API获取所有卫星数据');
            for (const index of indices) {
                this.logger.log(`开始处理索引: ${index}`);
                try {
                    const indexResults = await this.elasticsearchSatelliteService.scrollSearch({
                        indices: [index],
                        query,
                        size: batchSize
                    });
                    this.logger.log(`从索引 ${index} 获取了 ${indexResults.length} 条记录`);
                    allSearchResults = allSearchResults.concat(indexResults);
                    const progress = 25 * (indices.indexOf(index) + 1) / indices.length;
                    await this.aggregationTaskService.updateTaskProgress(task.id, progress, allSearchResults.length);
                }
                catch (error) {
                    this.logger.error(`处理索引 ${index} 时出错: ${error.message}`, error.stack);
                }
            }
            this.logger.log(`所有索引处理完成，总共获取 ${allSearchResults.length} 条记录`);
            if (allSearchResults.length === 0) {
                this.logger.log('未找到任何卫星数据');
                await this.aggregationTaskService.completeTask(task.id, 0);
                return { success: true, totalNewAggregated: 0, message: '未找到任何卫星数据' };
            }
            this.logger.log('开始筛选新的卫星数据');
            const newSatelliteRecords = [];
            for (const record of allSearchResults) {
                const source = record._source;
                const index = record._index;
                source._index = index;
                const noradId = source.norad_id ? String(source.norad_id).trim().toLowerCase() : '';
                const cosparId = source.cospar_id ? String(source.cospar_id).trim().toLowerCase() : '';
                const satelliteName = source.satellite_name ? String(source.satellite_name).trim().toLowerCase() : '';
                const alternativeName = source.alternative_name ? String(source.alternative_name).trim().toLowerCase() : '';
                const isNewSatellite = ((noradId || cosparId || satelliteName || alternativeName) &&
                    (!noradId || !existingNoradIds.has(noradId)) &&
                    (!cosparId || !existingCosparIds.has(cosparId)) &&
                    (!satelliteName || !existingSatelliteNames.has(satelliteName)) &&
                    (!alternativeName || !existingAlternativeNames.has(alternativeName)));
                if (isNewSatellite) {
                    newSatelliteRecords.push(source);
                }
            }
            this.logger.log(`筛选完成，找到 ${newSatelliteRecords.length} 条新的卫星数据`);
            await this.aggregationTaskService.updateTaskProgress(task.id, 50, newSatelliteRecords.length);
            if (newSatelliteRecords.length === 0) {
                this.logger.log('未找到新的卫星数据');
                await this.aggregationTaskService.completeTask(task.id, 0);
                return { success: true, totalNewAggregated: 0, message: '未找到新的卫星数据' };
            }
            this.logger.log('开始对新的卫星数据进行分组和合并');
            const satelliteGroups = [];
            for (const record of newSatelliteRecords) {
                let foundMatch = false;
                for (let i = 0; i < satelliteGroups.length; i++) {
                    const group = satelliteGroups[i];
                    if (this.canMergeWithGroup(record, group)) {
                        group.push(record);
                        foundMatch = true;
                        break;
                    }
                }
                if (!foundMatch) {
                    satelliteGroups.push([record]);
                }
            }
            this.logger.log(`根据标识字段分组后，共有 ${satelliteGroups.length} 组新的卫星数据`);
            const aggregatedSatellites = [];
            for (let groupIndex = 0; groupIndex < satelliteGroups.length; groupIndex++) {
                const groupRecords = satelliteGroups[groupIndex];
                const aggregatedSatellite = {
                    satellite_name: [],
                    alternative_name: [],
                    cospar_id: [],
                    country_of_registry: [],
                    owner: [],
                    users: [],
                    status: [],
                    norad_id: [],
                    launch_info: [],
                    orbit_info: [],
                    mass_kg: [],
                    power_watts: [],
                    lifetime_years: [],
                    contractor: [],
                    purpose: [],
                    detailed_purpose: [],
                    payload: [],
                    payload_description: [],
                    update_time: [],
                    _sources: []
                };
                const fieldValues = new Map();
                for (const record of groupRecords) {
                    const source = record._index.replace('satsinfo_', '');
                    if (!aggregatedSatellite._sources.includes(source)) {
                        aggregatedSatellite._sources.push(source);
                    }
                    for (const field of Object.keys(aggregatedSatellite)) {
                        if (field === '_sources' || field.startsWith('_'))
                            continue;
                        const value = record[field];
                        if (value !== undefined && value !== null && value !== '') {
                            if (!fieldValues.has(field)) {
                                fieldValues.set(field, new Set());
                            }
                            const valueStr = JSON.stringify(value);
                            if (!fieldValues.get(field).has(valueStr)) {
                                fieldValues.get(field).add(valueStr);
                                aggregatedSatellite[field].push({
                                    value,
                                    sources: [source]
                                });
                            }
                            else {
                                const existingItem = aggregatedSatellite[field].find((item) => JSON.stringify(item.value) === valueStr);
                                if (existingItem && !existingItem.sources.includes(source)) {
                                    existingItem.sources.push(source);
                                }
                            }
                        }
                    }
                }
                aggregatedSatellites.push(aggregatedSatellite);
            }
            this.logger.log(`聚合完成，共 ${aggregatedSatellites.length} 条新的卫星数据`);
            await this.aggregationTaskService.updateTaskProgress(task.id, 75, newSatelliteRecords.length);
            if (!saveToDatabase) {
                return {
                    success: true,
                    totalNewAggregated: aggregatedSatellites.length,
                    message: `成功聚合 ${aggregatedSatellites.length} 条新的卫星数据（未保存到数据库）`
                };
            }
            this.logger.log(`开始将聚合结果保存到数据库`);
            const saveBatchSize = 500;
            let savedCount = 0;
            for (let i = 0; i < aggregatedSatellites.length; i += saveBatchSize) {
                const batch = aggregatedSatellites.slice(i, i + saveBatchSize);
                const entities = batch.map(data => {
                    const entity = new satellite_entity_1.Satellite();
                    for (const field of Object.keys(data)) {
                        entity[field] = data[field];
                    }
                    return entity;
                });
                await this.satelliteRepository.save(entities);
                savedCount += batch.length;
                this.logger.log(`已保存 ${savedCount}/${aggregatedSatellites.length} 条新的卫星数据到数据库`);
            }
            this.logger.log(`成功将 ${savedCount} 条聚合后的新卫星数据保存到数据库`);
            this.logger.log(`增量卫星数据聚合完成，共处理 ${newSatelliteRecords.length} 条记录，聚合为 ${aggregatedSatellites.length} 条记录`);
            await this.aggregationTaskService.completeTask(task.id, aggregatedSatellites.length);
            return {
                success: true,
                totalNewAggregated: aggregatedSatellites.length,
                message: `成功聚合 ${aggregatedSatellites.length} 条新的卫星数据`
            };
        }
        catch (error) {
            this.logger.error(`增量聚合卫星数据失败: ${error.message}`, error.stack);
            if (task && task.id) {
                await this.aggregationTaskService.failTask(task.id, error.message);
            }
            throw new Error(`增量聚合卫星数据失败: ${error.message}`);
        }
    }
    async getSatelliteOrbitClassesLocal() {
        this.logger.debug('开始获取卫星轨道高度（本地数据库）');
        try {
            const standardOrbitClasses = [
                'LEO', 'GEO', 'MEO', 'HEO', 'SSO', 'Elliptical', 'Heliocentric',
                'Polar', 'Molniya', 'Tundra', 'Cislunar', 'Deep Space', 'Lagrangian', 'GTO', 'Inclined'
            ];
            const query = `
        WITH orbit_classes AS (
          SELECT DISTINCT jsonb_extract_path_text(jsonb_array_elements(orbit_info), 'value', 'orbit_class') as orbit_class 
          FROM satellites 
          WHERE orbit_info IS NOT NULL
        ) 
        SELECT orbit_class 
        FROM orbit_classes 
        WHERE orbit_class IN ('${standardOrbitClasses.join("','")}')
        ORDER BY orbit_class;
      `;
            const result = await this.satelliteRepository.query(query);
            const orbitClassMap = {
                'LEO': '低地球轨道',
                'GEO': '地球同步轨道',
                'MEO': '中地球轨道',
                'HEO': '高椭圆轨道',
                'SSO': '太阳同步轨道',
                'Elliptical': '椭圆轨道',
                'Heliocentric': '日心轨道',
                'Polar': '极地轨道',
                'Molniya': '莫尔尼亚轨道',
                'Tundra': '苔原轨道',
                'Cislunar': '地月轨道',
                'Deep Space': '深空轨道',
                'Lagrangian': '拉格朗日点轨道',
                'GTO': '地球同步转移轨道',
                'Inclined': '倾斜轨道'
            };
            const orbitClasses = result.map((item) => ({
                en: item.orbit_class,
                zh: orbitClassMap[item.orbit_class] || item.orbit_class
            }));
            this.logger.debug(`成功获取卫星轨道高度（本地数据库），共 ${orbitClasses.length} 种轨道高度`);
            return orbitClasses;
        }
        catch (error) {
            this.logger.error(`获取卫星轨道高度（本地数据库）失败: ${error.message}`);
            return [];
        }
    }
    async getSatelliteConstellationsLocal() {
        this.logger.debug('开始获取卫星星座集合（本地数据库）');
        try {
            const query = `
        WITH constellation_values AS (
          SELECT jsonb_array_elements(constellation) ->> 'value' as constellation_name
          FROM satellites 
          WHERE constellation IS NOT NULL AND jsonb_array_length(constellation) > 0
        ) 
        SELECT DISTINCT constellation_name
        FROM constellation_values
        WHERE constellation_name IS NOT NULL
        ORDER BY constellation_name;
      `;
            const result = await this.satelliteRepository.query(query);
            const constellationMap = {
                'Starlink': '星链',
                'OneWeb': '万维网',
                'Iridium': '铱星',
                'Globalstar': '全球星',
                'O3b': 'O3b',
                'Inmarsat': '国际海事卫星',
                'Orbcomm': '轨道通信',
                'Planet': '行星实验室',
                'Planet Dove': '行星实验室 Dove',
                'Planet Flock': '行星实验室 Flock',
                'Planet SkySat': '行星实验室 SkySat',
                'Galileo': '伽利略',
                'GPS': '全球定位系统',
                'GLONASS': '格洛纳斯',
                'BeiDou': '北斗',
                'BeiDou Compass': '北斗导航',
                'QZSS': '准天顶卫星系统',
                'NavIC': '印度区域导航卫星系统',
                'NavIC IRNSS': '印度区域导航卫星系统',
                'Telesat': 'Telesat',
                'Kuiper': 'Kuiper',
                'SES': 'SES',
                'Eutelsat': '欧洲通信卫星',
                'Intelsat': '国际通信卫星',
                'GOES': '静止气象卫星',
                'Meteosat': '气象卫星',
                'Himawari': '向日葵',
                'FengYun': '风云',
                'GaoFen': '高分',
                'Landsat': '陆地卫星',
                'Sentinel': '哨兵'
            };
            const constellations = result.map((item) => ({
                en: item.constellation_name,
                zh: constellationMap[item.constellation_name] || item.constellation_name
            }));
            this.logger.debug(`成功获取卫星星座集合，共 ${constellations.length} 个星座`);
            return constellations;
        }
        catch (error) {
            this.logger.error(`获取卫星星座集合失败: ${error.message}`);
            return [];
        }
    }
    async getSatelliteOrbitTypesLocal() {
        this.logger.debug('开始获取卫星轨道类型（本地数据库）');
        try {
            const query = `
        WITH orbit_types AS (
          SELECT DISTINCT jsonb_extract_path_text(jsonb_array_elements(orbit_info), 'value', 'orbit_type') as orbit_type 
          FROM satellites 
          WHERE orbit_info IS NOT NULL
        ) 
        SELECT orbit_type 
        FROM orbit_types 
        WHERE orbit_type IS NOT NULL AND orbit_type != ''
        ORDER BY orbit_type;
      `;
            const result = await this.satelliteRepository.query(query);
            const orbitTypeMap = {
                'Polar': '极地轨道',
                'Elliptical': '椭圆轨道',
                'Deep Highly Eccentric': '深度高偏心轨道',
                'Cislunar': '地月轨道',
                'Sun-Synchronous': '太阳同步轨道',
                'Non-Polar Inclined': '非极地倾斜轨道',
                'Retrograde': '逆行轨道',
                'Sun-Synchronous near polar': '近极太阳同步轨道',
                'Molniya': '莫尔尼亚轨道',
                'Equatorial': '赤道轨道',
                'Circular': '圆形轨道',
                'Inclined': '倾斜轨道',
                'Highly Elliptical': '高椭圆轨道'
            };
            const orbitTypes = result.map((item) => ({
                en: item.orbit_type,
                zh: orbitTypeMap[item.orbit_type] || item.orbit_type
            }));
            this.logger.debug(`成功获取卫星轨道类型（本地数据库），共 ${orbitTypes.length} 种轨道类型`);
            return orbitTypes;
        }
        catch (error) {
            this.logger.error(`获取卫星轨道类型（本地数据库）失败: ${error.message}`);
            return [];
        }
    }
    async updateSatelliteConstellationInfo() {
        this.logger.debug('开始从ES数据库获取星座信息并更新本地数据库');
        try {
            const nameBasedResult = await this.updateConstellationFromSatelliteName();
            const esBasedResult = await this.updateConstellationFromESData();
            const totalUpdated = nameBasedResult.updated + esBasedResult.updated;
            return {
                success: true,
                message: `成功更新${totalUpdated}个卫星的星座信息`,
                updated: totalUpdated
            };
        }
        catch (error) {
            this.logger.error(`更新卫星星座信息失败: ${error.message}`, error.stack);
            return {
                success: false,
                message: `更新卫星星座信息失败: ${error.message}`,
                updated: 0
            };
        }
    }
    async updateConstellationFromSatelliteName() {
        this.logger.debug('开始从卫星名称中提取星座信息');
        try {
            const identifySatellites = `
        -- 首先识别所有卫星名称包含Starlink等特定星座名称的卫星ID
        SELECT DISTINCT s.id,
          CASE
            WHEN EXISTS (
              SELECT 1
              FROM jsonb_array_elements(
                CASE WHEN s.satellite_name IS NULL OR s.satellite_name = '[]'::jsonb THEN '[]'::jsonb
                ELSE s.satellite_name END
              ) AS name
              WHERE name->>'value' LIKE 'Starlink%'
            ) OR EXISTS (
              SELECT 1
              FROM jsonb_array_elements(
                CASE WHEN s.alternative_name IS NULL OR s.alternative_name = '[]'::jsonb THEN '[]'::jsonb
                ELSE s.alternative_name END
              ) AS alt_name
              WHERE alt_name->>'value' LIKE 'Starlink%'
            ) THEN 'Starlink'
            
            WHEN EXISTS (
              SELECT 1
              FROM jsonb_array_elements(
                CASE WHEN s.satellite_name IS NULL OR s.satellite_name = '[]'::jsonb THEN '[]'::jsonb
                ELSE s.satellite_name END
              ) AS name
              WHERE name->>'value' LIKE 'OneWeb%'
            ) OR EXISTS (
              SELECT 1
              FROM jsonb_array_elements(
                CASE WHEN s.alternative_name IS NULL OR s.alternative_name = '[]'::jsonb THEN '[]'::jsonb
                ELSE s.alternative_name END
              ) AS alt_name
              WHERE alt_name->>'value' LIKE 'OneWeb%'
            ) THEN 'OneWeb'
            
            -- 其他星座模式，复制前面的逻辑
            WHEN EXISTS (
              SELECT 1
              FROM jsonb_array_elements(
                CASE WHEN s.satellite_name IS NULL OR s.satellite_name = '[]'::jsonb THEN '[]'::jsonb
                ELSE s.satellite_name END
              ) AS name
              WHERE name->>'value' LIKE 'Iridium%'
            ) OR EXISTS (
              SELECT 1
              FROM jsonb_array_elements(
                CASE WHEN s.alternative_name IS NULL OR s.alternative_name = '[]'::jsonb THEN '[]'::jsonb
                ELSE s.alternative_name END
              ) AS alt_name
              WHERE alt_name->>'value' LIKE 'Iridium%'
            ) THEN 'Iridium'
            
            -- 在此添加其他星座模式...
            ELSE NULL
          END AS constellation_name
        FROM satellites s
        WHERE 
          -- 至少satellite_name或alternative_name有一个不为空
          (s.satellite_name IS NOT NULL AND s.satellite_name != '[]'::jsonb) OR 
          (s.alternative_name IS NOT NULL AND s.alternative_name != '[]'::jsonb)
      `;
            const satellites = await this.satelliteRepository.query(identifySatellites);
            this.logger.debug(`识别出${satellites.length}个需要检查星座信息的卫星`);
            let updatedCount = 0;
            for (const sat of satellites) {
                if (!sat.constellation_name)
                    continue;
                const currSat = await this.satelliteRepository.findOne({
                    where: { id: sat.id },
                    select: ['id', 'constellation']
                });
                let needsUpdate = false;
                if (!currSat) {
                    continue;
                }
                else if (!currSat.constellation || currSat.constellation.length === 0) {
                    needsUpdate = true;
                }
                else {
                    const isRelated = currSat.constellation.some(item => item.value.toLowerCase().includes(sat.constellation_name.toLowerCase()) ||
                        sat.constellation_name.toLowerCase().includes(item.value.toLowerCase()));
                    needsUpdate = !isRelated;
                }
                if (needsUpdate) {
                    await this.satelliteRepository.update({ id: sat.id }, {
                        constellation: [
                            { value: sat.constellation_name, sources: ['name_extraction'] }
                        ]
                    });
                    updatedCount++;
                }
            }
            this.logger.debug(`从卫星名称中提取星座信息完成，更新了${updatedCount}个卫星的星座信息`);
            return { updated: updatedCount };
        }
        catch (error) {
            this.logger.error(`从卫星名称中提取星座信息失败: ${error.message}`, error.stack);
            return { updated: 0 };
        }
    }
    async updateConstellationFromESData() {
        this.logger.debug('开始从ES数据库中获取星座成员信息');
        try {
            const { Client } = require('@elastic/elasticsearch');
            const client = new Client({
                node: 'http://123.57.173.156:9200',
                auth: {
                    username: 'web_readonly',
                    password: 'web@readonly4all'
                }
            });
            const { body } = await client.search({
                index: 'constell_n2yo',
                size: 1000,
                body: {
                    query: {
                        match_all: {}
                    },
                    _source: ['constellation_name', 'sats_included']
                }
            });
            let totalUpdated = 0;
            for (const hit of body.hits.hits) {
                const constellationName = hit._source.constellation_name;
                const satellites = hit._source.sats_included || [];
                if (!constellationName || satellites.length === 0) {
                    continue;
                }
                this.logger.debug(`处理星座 ${constellationName}，包含 ${satellites.length} 个卫星`);
                for (const sat of satellites) {
                    const { norad_id, cospar_id, name } = sat;
                    const conditions = [];
                    const params = [];
                    if (norad_id) {
                        conditions.push(`EXISTS (
              SELECT FROM jsonb_array_elements(s.norad_id) AS n 
              WHERE (n->>'value')::text = $${params.length + 1}
            )`);
                        params.push(norad_id.toString());
                    }
                    if (cospar_id) {
                        conditions.push(`EXISTS (
              SELECT FROM jsonb_array_elements(s.cospar_id) AS c 
              WHERE (c->>'value')::text = $${params.length + 1}
            )`);
                        params.push(cospar_id);
                    }
                    if (name) {
                        conditions.push(`EXISTS (
              SELECT FROM jsonb_array_elements(s.satellite_name) AS sn 
              WHERE (sn->>'value')::text ILIKE $${params.length + 1}
            )`);
                        params.push(`%${name}%`);
                    }
                    if (conditions.length === 0) {
                        continue;
                    }
                    const query = `
            WITH matched_satellites AS (
              SELECT id
              FROM satellites s
              WHERE ${conditions.join(' OR ')}
              AND (s.constellation IS NULL OR s.constellation = '[]'::jsonb)
            ),
            updates AS (
              UPDATE satellites s
              SET constellation = jsonb_build_array(
                jsonb_build_object(
                  'value', $${params.length + 1},
                  'sources', jsonb_build_array('constell_n2yo')
                )
              )
              FROM matched_satellites m
              WHERE s.id = m.id
              RETURNING s.id
            )
            SELECT COUNT(*) as updated FROM updates;
          `;
                    params.push(constellationName);
                    const result = await this.satelliteRepository.query(query, params);
                    const updated = parseInt(result[0].updated, 10);
                    if (updated > 0) {
                        this.logger.debug(`更新了 ${updated} 个卫星的星座信息为 ${constellationName}`);
                        totalUpdated += updated;
                    }
                }
            }
            this.logger.debug(`从ES数据库中获取星座成员信息完成，更新了${totalUpdated}个卫星的星座信息`);
            return { updated: totalUpdated };
        }
        catch (error) {
            this.logger.error(`从ES数据库中获取星座成员信息失败: ${error.message}`, error.stack);
            return { updated: 0 };
        }
    }
    async updateSatelliteOrbitInfoFromTLE() {
        this.logger.debug('开始从ES数据库的orbital_tle索引更新卫星轨道信息');
        const startTime = Date.now();
        try {
            const { Client } = require('@elastic/elasticsearch');
            const client = new Client({
                node: 'http://123.57.173.156:9200',
                auth: {
                    username: 'web_readonly',
                    password: 'web@readonly4all'
                }
            });
            const batchSize = 50;
            let totalProcessed = 0;
            let updatedCount = 0;
            this.logger.debug('正在获取所有卫星数据...');
            const allSatellites = await this.satelliteRepository.find();
            this.logger.debug(`共找到 ${allSatellites.length} 个卫星`);
            for (let i = 0; i < allSatellites.length; i += batchSize) {
                const batchStartTime = Date.now();
                const satelliteBatch = allSatellites.slice(i, i + batchSize);
                this.logger.debug(`开始处理第 ${Math.floor(i / batchSize) + 1} 批卫星数据，共 ${satelliteBatch.length} 条`);
                const satellitesWithIdentifiers = satelliteBatch.filter(satellite => {
                    const noradIds = this.extractValues(satellite.norad_id);
                    const cosparIds = this.extractValues(satellite.cospar_id);
                    const names = this.extractValues(satellite.satellite_name);
                    return noradIds.length > 0 || cosparIds.length > 0 || names.length > 0;
                });
                if (satellitesWithIdentifiers.length === 0) {
                    this.logger.debug('该批次中没有卫星具有可用的标识符，跳过');
                    totalProcessed += satelliteBatch.length;
                    continue;
                }
                const msearchBody = [];
                const satelliteMap = new Map();
                satellitesWithIdentifiers.forEach((satellite, index) => {
                    const noradIds = this.extractValues(satellite.norad_id);
                    const cosparIds = this.extractValues(satellite.cospar_id);
                    const names = this.extractValues(satellite.satellite_name);
                    msearchBody.push({});
                    const should = [];
                    noradIds.forEach(noradId => {
                        should.push({
                            term: {
                                norad_id: noradId
                            }
                        });
                    });
                    cosparIds.forEach(cosparId => {
                        should.push({
                            term: {
                                cospar_id: cosparId
                            }
                        });
                    });
                    names.forEach(name => {
                        should.push({
                            match: {
                                satellite_name: name
                            }
                        });
                        should.push({
                            match: {
                                name: name
                            }
                        });
                    });
                    msearchBody.push({
                        query: {
                            bool: {
                                should,
                                minimum_should_match: 1
                            }
                        },
                        sort: [
                            { time: { order: 'desc' } }
                        ],
                        size: 1
                    });
                    satelliteMap.set(index, satellite);
                });
                this.logger.debug(`正在执行批量查询，共 ${satellitesWithIdentifiers.length} 个查询`);
                const response = await client.msearch({
                    index: 'orbital_tle',
                    body: msearchBody
                });
                const result = response.body || response;
                if (!result || !result.responses) {
                    this.logger.warn('ES查询返回了无效的响应格式');
                    totalProcessed += satelliteBatch.length;
                    continue;
                }
                const updatedSatellites = [];
                result.responses.forEach((resp, index) => {
                    const satellite = satelliteMap.get(index);
                    if (!satellite)
                        return;
                    if (!resp.hits || resp.hits.total.value === 0 || !resp.hits.hits || resp.hits.hits.length === 0) {
                        this.logger.debug(`卫星ID ${satellite.id} 在ES中未找到匹配的轨道数据`);
                        return;
                    }
                    const tleData = resp.hits.hits[0]._source;
                    if (!this.validateTLEData(tleData)) {
                        this.logger.debug(`卫星ID ${satellite.id} 的TLE数据缺少必要字段，跳过`);
                        return;
                    }
                    const newOrbitInfo = this.mapTLEToOrbitInfo(tleData);
                    if (this.needsOrbitInfoUpdate(satellite, newOrbitInfo)) {
                        const mergedOrbitInfo = this.mergeOrbitInfo(satellite.orbit_info, newOrbitInfo);
                        satellite.orbit_info = mergedOrbitInfo;
                        updatedSatellites.push(satellite);
                        this.logger.debug(`卫星ID ${satellite.id} 需要更新轨道信息`);
                    }
                    else {
                        this.logger.debug(`卫星ID ${satellite.id} 已有完整的轨道信息，无需更新`);
                    }
                });
                if (updatedSatellites.length > 0) {
                    this.logger.debug(`正在保存 ${updatedSatellites.length} 个更新的卫星数据`);
                    await this.satelliteRepository.save(updatedSatellites);
                    updatedCount += updatedSatellites.length;
                }
                totalProcessed += satelliteBatch.length;
                const batchEndTime = Date.now();
                const batchDuration = (batchEndTime - batchStartTime) / 1000;
                this.logger.debug(`第 ${Math.floor(i / batchSize) + 1} 批处理完成，共更新 ${updatedSatellites.length} 个卫星，耗时 ${batchDuration.toFixed(2)} 秒`);
                this.logger.debug(`总进度: ${Math.round((totalProcessed / allSatellites.length) * 100)}% (${totalProcessed}/${allSatellites.length})`);
            }
            const endTime = Date.now();
            const duration = (endTime - startTime) / 1000;
            this.logger.debug(`轨道信息更新完成，共更新 ${updatedCount} 个卫星，总耗时 ${duration.toFixed(2)} 秒`);
            return {
                success: true,
                message: `成功更新 ${updatedCount} 个卫星的轨道信息，耗时 ${duration.toFixed(2)} 秒`,
                updated: updatedCount
            };
        }
        catch (error) {
            const endTime = Date.now();
            const duration = (endTime - startTime) / 1000;
            this.logger.error(`更新卫星轨道信息失败: ${error.message}`, error.stack);
            return {
                success: false,
                message: `更新卫星轨道信息失败: ${error.message}，耗时 ${duration.toFixed(2)} 秒`,
                updated: 0
            };
        }
    }
    validateTLEData(tleData) {
        if (!tleData)
            return false;
        const requiredFields = ['norad_id', 'orbital_elements'];
        for (const field of requiredFields) {
            if (!tleData[field])
                return false;
        }
        if (tleData.orbital_elements) {
            const requiredOrbitalFields = ['inc_deg', 'ecc', 'sema_km'];
            for (const field of requiredOrbitalFields) {
                if (tleData.orbital_elements[field] === undefined)
                    return false;
            }
        }
        return true;
    }
    needsOrbitInfoUpdate(satellite, newOrbitInfo) {
        if (!satellite.orbit_info || satellite.orbit_info.length === 0) {
            return true;
        }
        const existingFields = new Set();
        if (Array.isArray(satellite.orbit_info)) {
            for (const item of satellite.orbit_info) {
                if (item.value) {
                    Object.keys(item.value).forEach(key => existingFields.add(key));
                }
            }
        }
        const importantFields = [
            'orbit_class', 'orbit_type', 'perigee_km', 'apogee_km', 'eccentricity',
            'incl_degrees', 'period_minutes', 'orbit_overview'
        ];
        for (const field of importantFields) {
            if (!existingFields.has(field)) {
                return true;
            }
        }
        for (const item of satellite.orbit_info) {
            if (item.value) {
                for (const field of importantFields) {
                    if (item.value[field] === undefined || item.value[field] === null || item.value[field] === '') {
                        return true;
                    }
                }
            }
        }
        return false;
    }
    mergeOrbitInfo(existingInfo, newInfo) {
        if (!existingInfo || !Array.isArray(existingInfo) || existingInfo.length === 0) {
            return newInfo;
        }
        if (!newInfo || !Array.isArray(newInfo) || newInfo.length === 0) {
            return existingInfo;
        }
        const mergedInfo = JSON.parse(JSON.stringify(existingInfo));
        const existingFields = new Set();
        for (const item of mergedInfo) {
            if (item.value) {
                Object.keys(item.value).forEach(key => existingFields.add(key));
            }
        }
        for (const newItem of newInfo) {
            if (!newItem.value)
                continue;
            const newItemFields = Object.keys(newItem.value);
            const sourcesToAdd = new Set(newItem.sources || []);
            for (const field of newItemFields) {
                const fieldMissing = !existingFields.has(field);
                const fieldValueInvalid = mergedInfo.some((item) => item.value && item.value[field] !== undefined &&
                    (item.value[field] === null || item.value[field] === ''));
                if (fieldMissing || fieldValueInvalid) {
                    let fieldAdded = false;
                    for (const item of mergedInfo) {
                        if (item.value) {
                            if (fieldMissing) {
                                item.value[field] = newItem.value[field];
                                existingFields.add(field);
                                fieldAdded = true;
                            }
                            else if (fieldValueInvalid && (item.value[field] === null || item.value[field] === '')) {
                                item.value[field] = newItem.value[field];
                                fieldAdded = true;
                            }
                            if (fieldAdded && Array.isArray(item.sources)) {
                                for (const source of sourcesToAdd) {
                                    if (!item.sources.includes(source)) {
                                        item.sources.push(source);
                                    }
                                }
                                break;
                            }
                        }
                    }
                    if (!fieldAdded && mergedInfo.length === 0) {
                        const newElement = {
                            value: { [field]: newItem.value[field] },
                            sources: Array.from(sourcesToAdd)
                        };
                        mergedInfo.push(newElement);
                        existingFields.add(field);
                    }
                }
            }
        }
        return mergedInfo;
    }
    extractValues(field) {
        if (!field || !Array.isArray(field) || field.length === 0) {
            return [];
        }
        return field
            .filter(item => item && item.value)
            .map(item => item.value.toString());
    }
    mapTLEToOrbitInfo(tleData) {
        const orbitInfo = {
            value: {
                orbit_overview: this.generateOrbitOverview(tleData),
                orbit_class: this.determineOrbitClass(tleData),
                orbit_type: this.determineOrbitType(tleData),
                longitude_of_geo_degrees: tleData.mean_motion && Math.abs(tleData.mean_motion - 1.0) < 0.01
                    ? tleData.mean_anomaly || 0
                    : 0,
                perigee_km: tleData.perigee ? tleData.perigee :
                    (tleData.semi_major_axis ? (tleData.semi_major_axis * (1 - tleData.eccentricity) - 6371) : null),
                apogee_km: tleData.apogee ? tleData.apogee :
                    (tleData.semi_major_axis ? (tleData.semi_major_axis * (1 + tleData.eccentricity) - 6371) : null),
                eccentricity: tleData.eccentricity || 0,
                incl_degrees: tleData.inclination || 0,
                period_minutes: tleData.period ? tleData.period :
                    (tleData.mean_motion ? (1440 / tleData.mean_motion) : null),
                decay_date: tleData.decay_date || null,
                deployed_date: tleData.launch_date || null
            },
            sources: ['orbital_tle']
        };
        const keys = Object.keys(orbitInfo.value);
        for (const key of keys) {
            if (orbitInfo.value[key] === null || orbitInfo.value[key] === undefined) {
                delete orbitInfo.value[key];
            }
        }
        return [orbitInfo];
    }
    generateOrbitOverview(tleData) {
        if (!tleData)
            return '';
        let overview = '';
        if (tleData.perigee && tleData.apogee) {
            overview += `${Math.round(tleData.perigee)} km × ${Math.round(tleData.apogee)} km`;
            if (tleData.inclination) {
                overview += `, ${tleData.inclination.toFixed(1)}°`;
            }
        }
        else if (tleData.semi_major_axis && tleData.eccentricity !== undefined) {
            const perigee = Math.round(tleData.semi_major_axis * (1 - tleData.eccentricity) - 6371);
            const apogee = Math.round(tleData.semi_major_axis * (1 + tleData.eccentricity) - 6371);
            overview += `${perigee} km × ${apogee} km`;
            if (tleData.inclination) {
                overview += `, ${tleData.inclination.toFixed(1)}°`;
            }
        }
        else if (tleData.mean_altitude && tleData.inclination) {
            overview += `${Math.round(tleData.mean_altitude)} km, ${tleData.inclination.toFixed(1)}°`;
        }
        if (tleData.mean_motion && Math.abs(tleData.mean_motion - 1.0) < 0.01) {
            if (overview) {
                overview += ' (GEO)';
            }
            else {
                overview = 'GEO';
            }
        }
        return overview || '';
    }
    determineOrbitType(tleData) {
        if (!tleData)
            return '';
        if (tleData.inclination !== undefined) {
            if (tleData.inclination > 80 && tleData.inclination < 100) {
                return 'Polar';
            }
            if (tleData.inclination < 20) {
                return 'Equatorial';
            }
            return 'Inclined';
        }
        if (tleData.eccentricity !== undefined) {
            if (tleData.eccentricity < 0.01) {
                return 'Circular';
            }
            if (tleData.eccentricity < 0.3) {
                return 'Elliptical';
            }
            return 'Highly Elliptical';
        }
        return '';
    }
    determineOrbitClass(tleData) {
        if (!tleData)
            return '';
        let meanAltitude = 0;
        if (tleData.mean_altitude) {
            meanAltitude = tleData.mean_altitude;
        }
        else if (tleData.perigee && tleData.apogee) {
            meanAltitude = (tleData.perigee + tleData.apogee) / 2;
        }
        else if (tleData.semi_major_axis) {
            meanAltitude = tleData.semi_major_axis - 6371;
        }
        else if (tleData.mean_motion) {
            const semiMajorAxis = Math.pow(6371 + 1.0 / Math.pow(tleData.mean_motion / (24 * 3600), 2 / 3), 1 / 3);
            meanAltitude = semiMajorAxis - 6371;
        }
        if (meanAltitude < 2000) {
            return 'LEO';
        }
        else if (meanAltitude < 35786) {
            return 'MEO';
        }
        else if (meanAltitude >= 35786 && meanAltitude <= 35790) {
            return 'GEO';
        }
        else {
            return 'HEO';
        }
    }
    async updateSatelliteUsersFromES() {
        try {
            this.logger.log('开始从ES中更新卫星users字段到本地数据库');
            const indices = [
                'satsinfo_gunter',
                'satsinfo_n2yo',
                'satsinfo_nanosats',
                'satsinfo_satnogs',
                'satsinfo_ucs'
            ];
            const query = {
                bool: {
                    must: [
                        {
                            exists: {
                                field: 'users'
                            }
                        }
                    ]
                }
            };
            let allSearchResults = [];
            const batchSize = 1000;
            for (const index of indices) {
                this.logger.log(`开始处理索引: ${index}`);
                try {
                    const indexResults = await this.elasticsearchSatelliteService.scrollSearch({
                        indices: [index],
                        query,
                        size: batchSize
                    });
                    this.logger.log(`从索引 ${index} 获取了 ${indexResults.length} 条有users字段的记录`);
                    allSearchResults = allSearchResults.concat(indexResults);
                }
                catch (error) {
                    this.logger.error(`处理索引 ${index} 时出错: ${error.message}`, error.stack);
                }
            }
            this.logger.log(`所有索引处理完成，总共获取 ${allSearchResults.length} 条有users字段的记录`);
            if (allSearchResults.length === 0) {
                this.logger.log('未找到任何有users字段的卫星数据');
                return { success: true, updated: 0, message: '未找到任何有users字段的卫星数据' };
            }
            let updatedCount = 0;
            const processBatchSize = 50;
            for (let i = 0; i < allSearchResults.length; i += processBatchSize) {
                const batch = allSearchResults.slice(i, i + processBatchSize);
                this.logger.debug(`处理第 ${Math.floor(i / processBatchSize) + 1}/${Math.ceil(allSearchResults.length / processBatchSize)} 批数据`);
                const updatePromises = batch.map(async (record) => {
                    const source = record._source;
                    const index = record._index.replace('satsinfo_', '');
                    if (!source.users)
                        return false;
                    const noradId = source.norad_id ? String(source.norad_id).trim() : '';
                    const cosparId = source.cospar_id ? String(source.cospar_id).trim() : '';
                    const satelliteName = source.satellite_name ? String(source.satellite_name).trim() : '';
                    let localSatellite;
                    if (noradId) {
                        localSatellite = await this.findSatelliteByNoradId(noradId);
                    }
                    if (!localSatellite && cosparId) {
                        localSatellite = await this.findSatelliteByCosparId(cosparId);
                    }
                    if (!localSatellite && satelliteName) {
                        localSatellite = await this.findSatelliteByName(satelliteName);
                    }
                    if (localSatellite) {
                        if (!localSatellite.users) {
                            localSatellite.users = [];
                        }
                        const usersValues = Array.isArray(source.users) ? source.users : [source.users];
                        for (const userValue of usersValues) {
                            const existingUserItem = localSatellite.users.find((item) => JSON.stringify(item.value) === JSON.stringify(userValue));
                            if (existingUserItem) {
                                if (!existingUserItem.sources.includes(index)) {
                                    existingUserItem.sources.push(index);
                                }
                            }
                            else {
                                localSatellite.users.push({
                                    value: userValue,
                                    sources: [index]
                                });
                            }
                        }
                        await this.satelliteRepository.save(localSatellite);
                        return true;
                    }
                    return false;
                });
                const updateResults = await Promise.all(updatePromises);
                updatedCount += updateResults.filter(Boolean).length;
                this.logger.debug(`已更新 ${updatedCount} 条记录`);
            }
            this.logger.log(`卫星users字段更新完成，共更新 ${updatedCount} 条记录`);
            return {
                success: true,
                updated: updatedCount,
                message: `成功从ES更新 ${updatedCount} 条卫星的users字段`
            };
        }
        catch (error) {
            this.logger.error(`更新卫星users字段失败: ${error.message}`, error.stack);
            return {
                success: false,
                updated: 0,
                message: `更新卫星users字段失败: ${error.message}`
            };
        }
    }
};
SatelliteService = SatelliteService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(satellite_entity_1.Satellite)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        elasticsearch_satellite_service_1.ElasticsearchSatelliteService,
        aggregation_task_service_1.AggregationTaskService])
], SatelliteService);
exports.SatelliteService = SatelliteService;
//# sourceMappingURL=satellite.service.js.map