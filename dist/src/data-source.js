"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppDataSource = void 0;
const typeorm_1 = require("typeorm");
const satellite_entity_1 = require("./entities/satellite.entity");
const user_entity_1 = require("./entities/user.entity");
const _1710754801389_AddUsersField_1 = require("./migrations/1710754801389-AddUsersField");
const _1710900000000_AddUserApprovalStatus_1 = require("./migrations/1710900000000-AddUserApprovalStatus");
const _1748500000000_UpdateApprovalFieldComments_1 = require("./migrations/1748500000000-UpdateApprovalFieldComments");
require('dotenv').config();
exports.AppDataSource = new typeorm_1.DataSource({
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    database: process.env.DB_DATABASE || 'spacedata',
    entities: [satellite_entity_1.Satellite, user_entity_1.User],
    migrations: [_1710754801389_AddUsersField_1.AddUsersField1710754801389, _1710900000000_AddUserApprovalStatus_1.AddUserApprovalStatus1710900000000, _1748500000000_UpdateApprovalFieldComments_1.UpdateApprovalFieldComments1748500000000],
    synchronize: false,
});
//# sourceMappingURL=data-source.js.map