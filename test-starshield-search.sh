#!/bin/bash

# 测试"星盾"关键词搜索
BASE_URL="http://localhost:3001/api/es/news"

# 从token.json文件中提取token
if [ -f "token.json" ]; then
    TOKEN=$(cat token.json | jq -r '.access_token')
else
    echo "错误：未找到token.json文件，请先运行 ./get-admin-token.sh 获取token"
    exit 1
fi

echo "=== 测试星盾关键词搜索 ==="
echo

echo "测试1: 搜索关键词 '星盾'"
curl -X POST "${BASE_URL}/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "keywords": ["星盾"],
    "page": 1,
    "limit": 10
  }' | jq '.'

echo
echo "---"
echo

echo "测试2: 搜索关键词 'Starshield'"
curl -X POST "${BASE_URL}/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "keywords": ["Starshield"],
    "page": 1,
    "limit": 10
  }' | jq '.'

echo
echo "---"
echo

echo "测试3: 搜索关键词 'starshield'（小写）"
curl -X POST "${BASE_URL}/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "keywords": ["starshield"],
    "page": 1,
    "limit": 10
  }' | jq '.'

echo
echo "测试完成！" 