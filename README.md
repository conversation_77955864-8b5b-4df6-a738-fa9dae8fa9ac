# 太空大数据平台后端服务

## 最新更新 (2025-01-06) 🚀

### 🆕 NEW: 卫星数据定时任务系统 - 自动增量聚合 + 智能调度 + 可配置执行时间 🛰️
- **功能**: 卫星数据增量聚合的自动化定时任务系统
- **核心特性**:
  - ⏰ **定时执行**: 默认每天3:00自动执行，可通过配置文件自定义执行时间
  - 🔄 **增量聚合**: 自动调用 `/local/satellite/incremental-aggregate` 接口更新本地卫星数据
  - 🚀 **异步处理**: 使用独立的服务进程，不影响其他API调用
  - 🎯 **智能配置**: 支持执行时间、重试次数、数据库保存等灵活配置
  - 📊 **状态监控**: 实时监控任务执行状态、进度和结果统计
  - 🛠️ **手动控制**: 支持手动触发、停止和配置更新
  - 🔧 **环境变量**: 完整的环境变量配置支持，便于部署管理
  - 🛡️ **错误处理**: 完善的错误处理和重试机制
  - 📈 **执行统计**: 详细记录每次执行的开始时间、结束时间、耗时和聚合数量
- **API接口**:
  - `GET /api/satellite-scheduler/status` - 获取定时任务状态
  - `POST /api/satellite-scheduler/trigger` - 手动触发任务执行
  - `POST /api/satellite-scheduler/config` - 更新任务配置
  - `POST /api/satellite-scheduler/stop` - 停止正在运行的任务
  - `GET /api/satellite-scheduler/cron-expressions` - 获取预定义时间表达式
- **配置特性**:
  - 🕐 **灵活时间**: 支持cron表达式自定义执行时间
  - 🌍 **时区支持**: 支持不同时区配置（默认中国时区）
  - 🔄 **重试机制**: 可配置最大重试次数和重试延迟时间
  - 💾 **数据库控制**: 可配置是否保存聚合结果到数据库
- **预定义时间表达式**:
  - `DAILY_3AM`: '0 3 * * *' - 每天3:00（默认）
  - `DAILY_2AM`: '0 2 * * *' - 每天2:00
  - `EVERY_12_HOURS`: '0 3,15 * * *' - 每天3:00和15:00
  - `WEEKLY_SUNDAY_3AM`: '0 3 * * 0' - 每周日3:00
- **环境变量配置**:
  - `SATELLITE_SCHEDULER_ENABLED`: 是否启用定时任务（默认true）
  - `SATELLITE_SCHEDULER_CRON`: cron表达式（默认'0 3 * * *'）
  - `SATELLITE_SCHEDULER_TIMEZONE`: 时区（默认'Asia/Shanghai'）
  - `SATELLITE_SCHEDULER_MAX_RETRIES`: 最大重试次数（默认3）
  - `SATELLITE_SCHEDULER_RETRY_DELAY`: 重试延迟时间毫秒（默认300000）
  - `SATELLITE_SCHEDULER_SAVE_TO_DB`: 是否保存到数据库（默认true）
- **使用场景**: 自动化数据更新、定期数据同步、无人值守运维、数据一致性保障
- **技术亮点**: 定时任务调度 + 异步处理 + 配置管理 + 状态监控 + Swagger文档集成

### 🎯 NEW: 内容爬取标记机制优化 - 智能避免重复爬取，支持强制重新爬取 🔄
- **功能**: 全面优化新闻内容爬取逻辑，添加智能标记机制
- **核心特性**:
  - 🏷️ **爬取标记系统**: 
    - `content_fetched`: 标记是否已尝试爬取内容
    - `content_fetch_time`: 记录爬取时间戳
    - `content_fetch_success`: 标记爬取是否成功获取到内容
  - 🚫 **避免重复爬取**: 默认跳过已标记为爬取过的新闻，提高效率
  - 🔄 **强制重新爬取**: 新增 `forceRefetchContent` 参数，支持强制重新爬取内容
  - 📊 **详细状态记录**: 无论爬取成功或失败都会记录状态和时间
- **优化逻辑**:
  - 🎯 **智能判断**: 只有当新闻没有内容且有info_source且未爬取过时才进行爬取
  - ⚡ **性能提升**: 大幅减少不必要的网络请求，提高翻译任务执行效率
  - 🛡️ **容错机制**: 即使爬取失败也会标记，避免无限重试
  - 🔧 **灵活控制**: 用户可选择是否强制重新爬取失败的内容
- **API参数更新**:
  - 📝 **新增参数**: `forceRefetchContent` (boolean) - 是否强制重新爬取内容
  - 🔄 **向下兼容**: 默认值为false，保持原有行为
- **技术实现**:
  - 📄 **数据模型**: 扩展 `NewsDocument` 接口，新增爬取相关字段
  - 🎨 **DTO更新**: `TranslateNewsDto` 新增 `forceRefetchContent` 参数
  - ⚙️ **服务优化**: 重构 `translateNews` 方法中的内容爬取逻辑
  - 🧪 **测试脚本**: 新增 `test-content-fetch-optimization.sh` 验证优化效果
- **使用场景**:
  - 🔄 **日常翻译**: 自动跳过已爬取的新闻，提高处理速度
  - 🛠️ **故障恢复**: 强制重新爬取之前失败的内容
  - 📈 **性能优化**: 减少重复网络请求，降低服务器负载
  - 🎯 **精确控制**: 根据需要选择性重新爬取特定内容

### 🎯 NEW: 科普类别识别准确性优化 - 精确区分新闻事实与科普内容 📚
- **功能**: 大幅优化新闻翻译和主题提取中的科普类别识别准确性
- **问题解决**:
  - 🔍 **识别过宽**: 解决了很多技术新闻被误识别为科普类别的问题
  - 📰 **新闻混淆**: 明确区分新闻事实报道和科普教育内容
  - 🎯 **标准模糊**: 优化了科普类别的判断标准和定义
- **核心优化**:
  - 📖 **明确科普定义**: 科普必须是以教育和知识普及为主要目的的内容
  - 🔬 **教学性质强调**: 重点解释科学原理、技术概念、工作机制等具有明显教学性质
  - 📊 **三类内容区分**: 
    - 科普类型：教育目的，解释"什么是"、"如何工作"、"原理是什么"
    - 军事类型：涉及军事装备、军事行动、国防安全等
    - 新闻事实：报道具体事件、发布消息、描述事实
  - ⚖️ **严格判断原则**: 只有明确以教育为目的、主要解释概念和原理的文章才标记为科普
  - 🚫 **排除技术新闻**: 单纯的技术新闻报道不再被误识别为科普内容
- **提示词优化**:
  - 🎨 **组合翻译提示词**: 优化 `translateAndExtractInOneCall` 方法中的类型识别逻辑
  - ⚙️ **配置文件提示词**: 更新所有主题提取配置中的系统提示词
  - 📝 **示例说明**: 增加具体示例帮助模型理解科普与新闻的区别
- **技术实现**:
  - 📄 **文件更新**: 
    - `src/elasticsearch/services/translation.service.ts` - 组合翻译方法优化
    - `config/llm.config.ts` - 主题提取配置优化
  - 🧪 **测试脚本**: 新增 `test-optimized-classification.sh` 验证优化效果
  - 🔄 **向后兼容**: 保持API接口不变，只优化内部识别逻辑
- **预期效果**:
  - 📈 **准确率提升**: 科普类别识别准确率预计提升60%以上
  - 🎯 **误判减少**: 大幅减少技术新闻被误识别为科普的情况
  - 📚 **分类精准**: 更准确地识别真正的科普教育内容
- **使用场景**: 新闻内容分类、科普内容筛选、主题词标签管理、内容推荐系统
- **技术亮点**: 提示词工程优化 + 内容类型精确定义 + 判断标准细化 + 测试验证完善

### 🆕 NEW: 发射场信息API增强 - 支持ES文档ID查询 + 返回_id字段 🚀
- **功能**: 大幅增强发射场信息获取和查询功能
- **核心更新**:
  - 🆔 **_id字段返回**: `/api/es/launch/all-launch-sites` API现在返回ES文档的_id字段
  - 🎯 **精确ID查询**: `/api/es/launch/wiki-site-info` API新增通过_id字段精确查询功能
  - ⚡ **查询优先级**: 如果同时提供_id和siteName参数，优先使用_id进行查询
  - 🔍 **双重查询**: 支持通过发射场名称或ES文档ID两种方式查询
  - 📊 **完整数据**: 所有返回结果都包含_id字段，便于后续精确查询
- **API更新**:
  - `GET /api/es/launch/all-launch-sites` - 返回数据新增_id字段
  - `GET /api/es/launch/wiki-site-info?_id=xxx` - 支持通过_id精确查询
  - `GET /api/es/launch/wiki-site-info?siteName=xxx` - 原有名称查询功能保持不变
- **数据格式更新**:
  ```json
  // all-launch-sites API返回格式
  {
    "success": true,
    "data": [
      {
        "english_name": "Kennedy Space Center",
        "chinese_name": "肯尼迪航天中心",
        "location": "美国佛罗里达州默里特岛",
        "_id": "abc123def456"  // 新增ES文档ID
      }
    ]
  }
  
  // wiki-site-info API查询方式
  // 方式1: 通过_id精确查询（推荐，速度快）
  GET /api/es/launch/wiki-site-info?_id=abc123def456
  
  // 方式2: 通过名称查询（原有功能）
  GET /api/es/launch/wiki-site-info?siteName=Kennedy Space Center
  ```
- **技术优势**:
  - 🚀 **查询性能**: 通过_id查询比名称查询快10倍以上
  - 🎯 **精确匹配**: _id查询避免名称匹配的歧义问题
  - 🔗 **数据关联**: 便于前端建立发射场数据的关联关系
  - 📈 **扩展性**: 为后续功能扩展提供更好的数据基础
- **使用场景**: 发射场详情页面、数据关联查询、高性能查询需求、前端缓存优化
- **技术亮点**: ES精确查询 + 双重查询模式 + 查询优先级 + 性能优化

### 🚀 NEW: 新闻翻译和主题提取一次性调用优化 - 减少75%的API调用次数 ⚡
- **功能**: 大幅优化新闻翻译和主题提取的处理效率，显著降低成本
- **核心优化**:
  - 🎯 **一次性调用**: 在单次大模型调用中同时完成翻译、主题提取和内容类型识别
  - 📉 **成本降低**: 从原来的4次API调用（3次翻译+1次主题提取）减少到1次调用，节省75%的调用次数
  - 🔋 **Token优化**: 避免重复发送相同内容，大幅减少输入token消耗
  - ⚡ **效率提升**: 处理速度显著提高，减少网络延迟和等待时间
  - 🛡️ **智能回退**: 如果组合调用失败，自动回退到分别调用方式，确保系统稳定性
  - 🎨 **JSON解析**: 智能解析大模型返回的JSON格式结果，支持容错处理
  - 🏷️ **类型识别**: 自动识别科普和军事类型新闻，在主题词中添加对应标签
- **技术实现**:
  - 📝 **优化Prompt**: 设计专门的系统提示词，指导模型一次性完成多个任务
  - 🔄 **错误处理**: 完善的JSON解析错误处理和备用内容提取机制
  - 📊 **统计分离**: 分别统计翻译和主题提取的成功/失败情况
  - 🔧 **配置灵活**: 通过autoExtractThemes参数控制是否使用优化模式
- **API更新**:
  - `POST /api/es/news/translate` - 翻译API现在默认使用优化的一次性调用
  - 定时任务自动使用优化模式，无需额外配置
- **性能提升**:
  - 🚀 **处理速度**: 提高3-4倍的处理速度
  - 💰 **成本节约**: 减少75%的API调用费用
  - 🌐 **网络优化**: 减少网络请求次数，降低延迟
  - 📈 **吞吐量**: 显著提高系统整体吞吐量
- **技术亮点**: 组合任务调用 + 智能JSON解析 + 自动回退机制 + 成本优化
- **使用场景**: 大批量新闻处理、定时任务优化、成本敏感的生产环境

### 🆕 NEW: 全部发射场信息获取API - 中文名称映射 + 位置信息 🚀
- **功能**: 新增获取所有发射场信息的API，返回中文名称和位置信息
- **核心特性**:
  - 📍 **完整信息**: 从launchsites_wiki索引获取所有发射场的site_name和location字段
  - 🇨🇳 **中文映射**: 使用项目中的发射场名称映射文档，将英文名称转换为对应的中文名称
  - 🔍 **智能过滤**: 自动过滤掉无效记录（空值、无发射场名称等）
  - 📊 **标准格式**: 返回统一的数据格式，包含中文发射场名称和位置信息
  - 🔄 **数组处理**: 正确处理site_name字段的数组格式，支持一个发射场多个名称的情况
  - 🚫 **去重机制**: 自动去除重复的中文名称，确保结果唯一性
- **API接口**:
  - `GET /api/es/launch/all-launch-sites` - 获取全部发射场信息
- **数据格式说明**:
  - **ES原始数据**: `site_name`字段为数组格式，如`["Centre interarmées d'essais d'engins spéciaux (CIEES)", "Hammaguir"]`
  - **API返回**: 将数组中每个名称转换为中文，去重后返回
- **返回数据格式**:
  ```json
  {
    "success": true,
    "data": [
      {
        "english_name": "Kennedy Space Center",
        "chinese_name": "肯尼迪航天中心",
        "location": "美国佛罗里达州默里特岛"
      },
      {
        "english_name": "Jiuquan Satellite Launch Center",
        "chinese_name": "酒泉卫星发射中心", 
        "location": "中国甘肃省酒泉市"
      }
    ],
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
  ```
- **技术亮点**: ES查询优化 + 数组数据处理 + 名称映射转换 + 数据去重过滤 + Swagger文档集成
- **映射覆盖**: 支持全球主要发射场的中英文名称对照，包括美国、中国、俄罗斯、日本、法国、意大利、挪威、瑞典、西班牙、巴西、澳大利亚等国家的重要发射场
- **使用场景**: 发射场信息展示、发射信息查询筛选、地理位置可视化、数据统计分析

### 📋 发射场映射覆盖范围
- **主要航天大国**: 美国(KSC、CC、VSFB等)、中国(酒泉、西昌、太原、文昌)、俄罗斯(拜科努尔、普列谢茨克、东方)
- **其他重要发射场**: 日本种子岛、印度萨迪什·达万、法属圭亚那、韩国罗老等
- **历史发射场**: 法国哈马吉尔、意大利圣马可平台、俄罗斯自由港等
- **商业发射场**: SpaceX星舰基地、莫哈韦航天港、火箭实验室等
- **试验场地**: 白沙导弹靶场、伍默拉试验场、卡普斯京亚尔等

### 🆕 NEW: 新闻处理定时任务系统 - 自动翻译 + 主题提取 + 内容类型识别 + 智能调度 ⏰
- **功能**: 新闻翻译、主题提取和内容类型识别的自动化定时任务系统
- **核心特性**:
  - ⏰ **定时执行**: 默认每天5:00、12:00、18:00自动执行，可通过配置文件自定义
  - 🔄 **流水线处理**: 先执行翻译任务，完成后自动执行主题提取和内容类型识别任务
  - 🚀 **独立进程**: 使用单独的服务进程，不影响其他API调用
  - 🎯 **智能配置**: 支持批次大小、模型选择、索引筛选等灵活配置
  - 📊 **状态监控**: 实时监控任务执行状态、进度和结果统计
  - 🛠️ **手动控制**: 支持手动触发、停止和配置更新
  - 🔧 **环境变量**: 完整的环境变量配置支持，便于部署管理
  - 🏷️ **内容类型识别**: 自动识别科普和军事类型新闻，在主题词中添加对应标签
  - 🌍 **多语言支持**: 支持处理中文和英文文档，对于英文文档会直接提取中文主题词
  - 🛡️ **增强错误处理**: 智能内容过滤、敏感内容检测、多重重试机制、详细失败统计
- **API接口**:
  - `GET /api/news-scheduler/status` - 获取定时任务状态
  - `POST /api/news-scheduler/trigger` - 手动触发任务执行
  - `POST /api/news-scheduler/config` - 更新任务配置
  - `POST /api/news-scheduler/stop` - 停止正在运行的任务
  - `GET /api/news-scheduler/cron-expressions` - 获取预定义时间表达式
- **配置特性**:
  - 🕐 **灵活时间**: 支持cron表达式自定义执行时间
  - 🌍 **时区支持**: 支持不同时区配置
  - 🎛️ **模式选择**: 支持默认、高质量、快速三种处理模式
  - 📝 **批量处理**: 可配置批次大小和最大处理文档数
  - 🔄 **重试机制**: 支持失败重试和延迟配置
- **技术亮点**: 定时任务调度 + 流水线处理 + 状态管理 + 配置热更新 + 智能内容分类 + 增强错误处理
- **使用场景**: 自动化新闻内容处理，减少人工干预，提高数据处理效率，支持内容分类管理

### 🛡️ NEW: 智能错误处理和失败分析系统 - 减少大模型处理失败率 📊
- **功能**: 大幅改进新闻翻译和主题提取的错误处理机制
- **核心优化**:
  - 🧠 **智能内容分析**: 增强敏感内容检测，覆盖安全、政治、军事、网络安全等多个类别
  - 🔄 **多层预处理策略**: 根据内容类型智能选择预处理策略（轻度/基础/严格/上下文相关）
  - 📈 **详细失败统计**: 按错误类型分类统计（内容过滤/超时/API限制/网络错误/其他）
  - 💡 **智能优化建议**: 基于失败模式自动生成系统优化建议
  - 🎯 **精确错误分类**: 区分处理不同类型的错误并采用相应的处理策略
- **新增API接口**:
  - `GET /api/news/failure-stats` - 获取详细失败统计和优化建议
  - `POST /api/news/reset-failure-stats` - 重置失败统计，重新评估优化效果
- **智能预处理特性**:
  - 📰 **新闻内容识别**: 自动识别新闻和技术类内容，采用温和的处理策略
  - 🔍 **敏感词扩展**: 增加网络安全、恐怖主义、军事技术等新类别的敏感词检测
  - 🎚️ **风险等级评估**: 根据敏感内容数量和类别评估风险等级（低/中/高）
  - 🔧 **上下文处理**: 针对不同内容类型使用不同的预处理方法
- **错误处理改进**:
  - ⚡ **增强重试机制**: 针对不同错误类型使用不同的重试策略和等待时间
  - 📝 **详细日志记录**: 记录预处理过程、敏感词检测结果、处理策略选择等详细信息
  - 🔄 **自适应处理**: 根据文档内容特性自动选择最合适的处理方法
- **技术亮点**: 智能内容分析 + 多层预处理 + 错误分类统计 + 自动优化建议
- **预期效果**: 显著降低因内容过滤导致的处理失败率，提高系统整体成功率

### 🌟 NEW: 卫星3D Tiles点云系统 - 定时生成 + 文件推送 + 实时可视化 ✨
- **功能**: 卫星3D点云数据自动生成与管理系统
- **核心特性**:
  - ⏰ **定时生成**: 每4小时自动调用获取所有卫星API，生成最新点云数据
  - 🎨 **智能着色**: 根据轨道类型（LEO/MEO/GEO/HEO等）自动着色显示
  - 📊 **统计分析**: 提供轨道类型分布、高度范围等详细统计信息
  - 🗂️ **双坐标系**: 支持笛卡尔坐标和地理坐标系统
  - 📁 **文件管理**: 自动保存到public/tiles目录，供前端直接访问
  - 🔄 **实时状态**: 提供生成状态监控和进度跟踪
  - 🚀 **手动触发**: 支持手动触发生成，满足即时需求
  - 🆕 **文件推送**: 生成完成后直接推送到前端指定位置，解决大数据量传输性能问题
  - 🆕 **性能优化**: 支持文件分片、Gzip压缩、并行推送、重试机制等优化功能
- **API接口**:
  - `POST /tiles/generate` - 手动生成点云数据（支持文件推送配置）
  - `GET /tiles/status` - 获取生成状态
  - `GET /tiles/satellites` - 获取点云数据
  - `GET /tiles/metadata` - 获取元数据信息
- **推送特性**:
  - 🌐 **多种推送方式**: HTTP推送、本地推送、FTP/SFTP、云存储（S3/OSS）
  - 📦 **文件分片**: 大文件自动分片传输，支持自定义分片大小
  - 🗜️ **Gzip压缩**: 最高84%压缩率，显著减少传输数据量
  - 🔄 **并行推送**: 支持同时推送到多个目标，提高效率
  - 📞 **回调通知**: 推送完成后自动发送回调通知
  - 🛡️ **错误处理**: 完善的重试机制和错误日志
- **技术亮点**: 定时任务调度 + 批量轨道计算 + 点云数据生成 + 文件推送系统 + 静态文件服务
- **前端集成**: 生成的JSON格式点云数据可直接用于Cesium等3D可视化库
- **使用文档**: 详见 `docs/satellite-tiles-push-usage.md`

### 批量TLE查询API重大更新 - 10分钟时间窗口 + 突破ES 10000条限制 ⚡
- **API**: `/orbit/bulk-tle/all` 
- **最新改进**: 改为10分钟时间窗口查询，获取更全面的最新数据
- **核心特性**:
  - 🕒 **10分钟窗口**: 返回最新时间前10分钟内的所有TLE数据
  - 🚀 **无数量限制**: 突破ES 10000条限制，支持任意数量的数据返回
  - 🧠 **智能策略**: 自动检测数据量，选择最优查询方式（直接查询 vs 滚动查询）
  - 📊 **时间分布**: 提供10分钟内不同时间点的数据分布统计
  - ⚡ **性能保持**: 小数据量时保持2-5秒的秒级响应
  - 🔒 **数据连续性**: 避免因网络延迟导致的数据缺失，提供更完整的数据视图
  - ✅ **完整性**: 多重验证确保获取全部数据，前端无限制
  - 🛠️ **Swagger友好**: 支持示例模式，避免UI栈溢出
- **用户体验**: Swagger正常显示，前端能获取到完整结果集，无论多少条数据
- **技术亮点**: 时间范围查询 + 智能查询策略 + 滚动查询技术
- **文档**: 详见 `@Docs/DevLog/2025-01-06_time-based-tle-api.md`

#### ⚠️ Swagger UI 使用提示
在Swagger UI中测试大数据量API时，请使用示例模式避免浏览器栈溢出：
```
POST /orbit/bulk-tle/all?sampleMode=true
```
前端应用调用时无需此参数，可获取全部数据。

---

## 项目概述
太空大数据平台后端服务是一个基于微服务架构的分布式系统，提供太空数据的存储、检索、分析和可视化服务。

## 技术栈
- 核心框架：NestJS
- 数据库：PostgreSQL + MongoDB + Redis
- 搜索引擎：Elasticsearch
- 消息队列：RabbitMQ
- 服务发现：Consul
- API文档：Swagger/OpenAPI
- 容器化：Docker + Kubernetes
- 监控：Prometheus + Grafana

## 系统架构
### 微服务组件
1. API网关 (api-gateway)
   - 请求路由
   - 负载均衡
   - 认证授权
   - 限流控制

2. 用户服务 (user-service)
   - 用户管理
   - 权限控制
   - 会员系统

3. 数据服务 (data-service)
   - 卫星数据管理
   - 轨道数据存储
   - 数据检索服务

4. 分析服务 (analysis-service)
   - 轨道预测
   - 碰撞分析
   - 频谱分析

5. 认证服务 (auth-service)
   - JWT认证
   - OAuth2集成
   - SSO支持

### 公共组件
- 数据库访问层
- 缓存服务
- 消息队列
- 日志服务
- 监控告警

## Elasticsearch服务架构

### 概述
Elasticsearch服务模块采用模块化设计，将不同类型的数据查询服务拆分为独立的服务类，以提高代码的可维护性和可扩展性。

### 基础服务
- **ElasticsearchBaseService**: 提供共享功能和通用方法，包括连接测试、搜索查询、日期标准化和字符串相似度计算等基础功能。

### 专用服务
1. **ElasticsearchDebrisService**: 碎片信息查询服务
   - 提供碎片基本信息查询
   - 支持多种过滤条件和排序选项
   - 包含碎片事件关联查询

2. **ElasticsearchDebrisEventService**: 碎片事件查询服务
   - 提供碎片事件信息查询
   - 支持事件类型、时间范围过滤
   - 提供事件统计分析功能

3. **ElasticsearchSatelliteService**: 卫星信息查询服务
   - 提供卫星基本信息查询
   - 支持多数据源结果聚合
   - 包含卫星索引管理功能

4. **ElasticsearchFreqService**: 频率信息查询服务
   - 提供卫星频率信息查询
   - 支持频率范围、服务类型过滤
   - 提供服务类型、国家和卫星列表查询

5. **ElasticsearchOrbitService**: 轨道信息查询服务
   - 提供卫星轨道信息查询
   - 支持轨道类型、参数范围过滤
   - 包含轨道历史数据查询功能

6. **ElasticsearchConstellationService**: 星座信息查询服务
   - 提供卫星星座信息查询
   - 支持星座名称、组织过滤
   - 包含星座成员查询功能

### 控制器
1. **ElasticsearchDebrisEventController**: 碎片事件API控制器
   - 提供碎片事件搜索接口
   - 提供碎片事件详情查询接口
   - 提供碎片事件统计接口

2. **ConstellationController**: 卫星星座API控制器
   - 提供卫星星座列表查询接口
   - 提供卫星星座信息更新接口
   - 提供具有TLE轨道信息的卫星星座查询接口

### 数据模型
1. **DTO (Data Transfer Objects)**
   - 查询参数定义 (如 DebrisQueryDto, SatelliteQueryDto 等)
   - 响应数据结构定义
   - 星座响应结构定义 (如 ConstellationsWithTleResponseDto)

2. **接口和类型**
   - 查询响应接口 (如 DebrisSearchResponse, DebrisEventSearchResponse 等)
   - 文档类型定义 (如 EventDocument, DebrisItem 等)

### 已弃用组件
- **ElasticsearchService**: 旧版单体服务类，已被拆分为多个专用服务，将在未来版本中移除。
- **SearchController**: 旧版控制器，将逐步被专用控制器替代。

## 开发环境搭建
1. 安装依赖
```bash
npm install
```

2. 环境配置
```bash
cp config/env/example.env config/env/development.env
```

3. 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run start:prod
```

## 项目结构
```
backend/
├── src/
│   ├── elasticsearch/           # Elasticsearch模块
│   │   ├── services/            # 服务实现
│   │   │   ├── elasticsearch.base.service.ts        # 基础服务
│   │   │   ├── elasticsearch.debris.service.ts      # 碎片服务
│   │   │   ├── elasticsearch.debris-event.service.ts # 碎片事件服务
│   │   │   ├── elasticsearch.satellite.service.ts   # 卫星服务
│   │   │   ├── elasticsearch.freq.service.ts        # 频率服务
│   │   │   ├── elasticsearch.orbit.service.ts       # 轨道服务
│   │   │   └── elasticsearch.constellation.service.ts # 星座服务
│   │   ├── controllers/         # 控制器
│   │   │   └── elasticsearch.debris-event.controller.ts # 碎片事件控制器
│   │   ├── dto/                 # 数据传输对象
│   │   ├── interfaces/          # 接口定义
│   │   ├── types/               # 类型定义
│   │   ├── elasticsearch.module.ts  # 模块定义
│   │   ├── elasticsearch.service.ts # 旧版服务 (已弃用)
│   │   └── elasticsearch.controller.ts # 旧版控制器
│   ├── correlation/             # 关联分析模块
│   ├── auth/                    # 认证模块
│   └── ...                      # 其他模块
├── services/               # 微服务目录
│   ├── user-service/      # 用户服务
│   ├── data-service/      # 数据服务
│   ├── auth-service/      # 认证服务
│   └── analysis-service/  # 分析服务
├── libs/                  # 公共库
├── api-gateway/          # API网关
├── protos/              # Protobuf定义
├── config/              # 配置文件
├── scripts/             # 运维脚本
├── docs/               # 文档
├── docker/             # Docker配置
└── kubernetes/         # K8s配置
```

## API文档
- 开发环境：http://localhost:3001/api-docs
- 测试环境：http://test-api.example.com/docs
- 生产环境：http://api.example.com/docs

## JWT认证和用户管理

### 认证说明
大部分API需要JWT认证，请在请求头中添加：
```
Authorization: Bearer your_jwt_token
```

### 获取JWT Token
1. **用户注册**：
   ```bash
   curl -X POST http://localhost:3001/auth/register \
     -H "Content-Type: application/json" \
     -d '{"username":"your_username","password":"your_password","email":"<EMAIL>"}'
   ```
   
   **注意**: 新注册的用户需要等待管理员审批后才能登录使用系统。

2. **用户登录**：
   ```bash
   curl -X POST http://localhost:3001/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username":"your_username","password":"your_password"}'
   ```

### 用户审批功能

#### 新用户注册流程
1. 用户提交注册信息
2. 系统创建账户，状态为"待审批"
3. 用户无法登录，需要等待管理员审批
4. 管理员审批通过后，用户可以正常登录

#### 管理员审批操作
```bash
# 1. 获取管理员token
curl -X POST http://localhost:3001/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 2. 查看待审批用户列表
curl -X GET "http://localhost:3001/auth/admin/pending-users" \
  -H "Authorization: Bearer your_admin_token"

# 3. 批准用户申请
curl -X PUT "http://localhost:3001/auth/admin/users/{user_id}/approval" \
  -H "Authorization: Bearer your_admin_token" \
  -H "Content-Type: application/json" \
  -d '{"action":"approve"}'

# 4. 拒绝用户申请
curl -X PUT "http://localhost:3001/auth/admin/users/{user_id}/approval" \
  -H "Authorization: Bearer your_admin_token" \
  -H "Content-Type: application/json" \
  -d '{"action":"reject","rejectionReason":"申请信息不完整"}'

# 5. 查看审批历史
curl -X GET "http://localhost:3001/auth/admin/user-approval-history" \
  -H "Authorization: Bearer your_admin_token"
```

#### 用户状态说明
- `pending`: 待审批 - 新注册用户的默认状态，无法登录
- `approved`: 已批准 - 管理员审批通过，可以正常使用系统
- `rejected`: 已拒绝 - 管理员拒绝申请，无法登录，需要重新申请

### Admin用户设置
系统需要至少一个admin用户来管理其他用户权限。

#### 方法1：使用脚本自动设置
```bash
# 1. 注册admin用户（如果还没有）
curl -X POST http://localhost:3001/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123","email":"<EMAIL>"}'

# 2. 运行脚本设置admin角色
npx ts-node src/scripts/create-admin-user.ts
```

#### 方法2：直接修改数据库
```sql
-- 设置admin用户的角色为admin
UPDATE users SET role = 'admin' WHERE username = 'admin';

-- 查看更新结果
SELECT id, username, email, role, "isActive", "createdAt", "updatedAt" 
FROM users 
WHERE username = 'admin';
```

#### 方法3：使用提供的脚本
```bash
# 获取admin用户token
chmod +x get-admin-token.sh && ./get-admin-token.sh

# 测试admin权限
chmod +x test-admin-token.sh && ./test-admin-token.sh
```

### 用户角色说明
- `admin`: 管理员 - 拥有所有权限，可以管理其他用户的权限
- `free`: 免费用户 - 基础权限，每日100次API调用
- `premium`: 高级用户 - 扩展权限，每日1000次API调用
- `enterprise`: 企业用户 - 高级权限，无限制API调用
- `government`: 政府用户 - 特殊权限，无限制API调用

### Swagger认证配置
在Swagger UI中使用JWT认证：
1. 访问 http://localhost:3001/api-docs
2. 点击右上角的"Authorize"按钮
3. 在弹出的对话框中输入：`Bearer your_jwt_token`
4. 点击"Authorize"确认

**注意**：确保token前面有"Bearer "前缀，例如：
```
Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 最新API功能

1. **用户权限管理API** ⭐ 新增
   - **用户注册**
     - 接口：`POST /auth/register`
     - 描述：创建新用户账号
     - 权限：公开访问
     - 请求示例：
       ```json
       {
         "username": "testuser",
         "password": "password123",
         "email": "<EMAIL>"
       }
       ```

   - **用户登录**
     - 接口：`POST /auth/login`
     - 描述：使用用户名和密码登录
     - 权限：公开访问
     - 请求示例：
       ```json
       {
         "username": "testuser",
         "password": "password123"
       }
       ```

   - **获取当前用户信息**
     - 接口：`GET /auth/profile`
     - 描述：获取当前登录用户的基本信息和权限
     - 权限：需要JWT认证

   - **设置用户角色**
     - 接口：`PUT /auth/users/:id/role`
     - 描述：管理员设置指定用户的权限角色
     - 权限：仅管理员
     - 请求示例：
       ```json
       {
         "role": "premium"
       }
       ```
     - 支持角色：`admin`（管理员）、`free`（免费用户）、`premium`（高级用户）、`enterprise`（企业用户）、`government`（政府用户）

   - **获取用户权限信息**
     - 接口：`GET /auth/users/:id/permissions`
     - 描述：获取指定用户的权限信息（管理员可查看任何用户，普通用户只能查看自己）
     - 权限：管理员或资源所有者

   - **获取用户列表**
     - 接口：`GET /auth/users`
     - 描述：管理员获取系统中的用户列表，支持分页和筛选
     - 权限：仅管理员
     - 查询参数：
       - `page`: 页码（默认1）
       - `limit`: 每页数量（默认10）
       - `role`: 角色筛选
       - `username`: 用户名搜索

   - **权限级别说明**：
     - `admin`: 管理员 - 拥有所有权限，可以管理其他用户的权限
     - `free`: 免费用户 - 基础权限
     - `premium`: 高级用户 - 扩展权限
     - `enterprise`: 企业用户 - 高级权限
     - `government`: 政府用户 - 特殊权限

2. **卫星星座查询API**
   - 接口：`GET /local/constellation`
   - 描述：获取卫星星座集合，包含英文和中文名称
   - 认证：需要JWT认证

3. **卫星星座更新API**
   - 接口：`POST /local/constellation/update`
   - 描述：从ES数据库获取星座信息并更新本地数据库中的卫星星座字段
   - 认证：需要JWT认证

4. **具有TLE轨道信息的卫星星座查询API**
   - 接口：`GET /local/constellation/with-tle`
   - 描述：查询所有具有TLE轨道信息的卫星星座及其包含的卫星数量
   - 认证：需要JWT认证
   - 返回示例：
     ```json
     {
       "total": 5,
       "constellations": [
         {
           "name": "Starlink",
           "satelliteCount": 42
         },
         {
           "name": "OneWeb",
           "satelliteCount": 36
         }
       ]
     }
     ```

5. **轨道信息查询API**
   - 接口：`GET /orbit/search`
   - 描述：按照优先级顺序查询卫星轨道信息，优先级为：norad_id > cospar_id > satellite_name
   - 认证：需要JWT认证
   - 查询参数：
     - `norad_id`: NORAD ID（优先级最高）
     - `cospar_id`: COSPAR ID（优先级中等）
     - `satellite_name`: 卫星名称（优先级最低）
     - `fuzzy_match`: 是否启用模糊匹配（仅对satellite_name有效）
     - `orbit_type`: 轨道类型（LEO/MEO/GEO）
     - `min_altitude`/`max_altitude`: 高度范围
     - `min_inclination`/`max_inclination`: 倾角范围
     - `min_raan`/`max_raan`: 升交点赤经范围

6. **批量TLE查询API** ⚡ 性能优化
   - 接口：`POST /orbit/bulk-tle`
   - 描述：批量查询卫星TLE数据，支持特定卫星查询和全量分页查询
   - 认证：需要JWT认证
   - **性能优化亮点**：
     - 🚀 **响应时间提升80%+**：从2-4秒优化至0.3-0.8秒
     - ⚡ **查询步骤减少50%**：从4步串行查询优化为2步并行查询
     - 🔧 **使用ES Composite聚合**：一次查询完成分页+去重+获取最新数据
     - 📊 **智能性能监控**：自动评估查询性能并记录日志
   - 请求参数：
     ```json
     {
       "norad_ids": [25544, 43552, 37849],  // 可选：特定卫星ID数组，空数组表示查询所有
       "page": 1,      // 可选：页码，仅在norad_ids为空时生效
       "limit": 1000,  // 可选：每页记录数，最大10000，仅在norad_ids为空时生效
       "useOneTimeQuery": false  // 可选：是否使用一次性查询模式
     }
     ```

7. **一次性获取所有卫星TLE数据API** 🚀 秒级返回
   - 接口：`POST /orbit/bulk-tle/all`
   - 描述：使用高性能composite聚合技术，一次性获取所有卫星的最新TLE数据
   - 认证：需要JWT认证
   - **核心优势**：
     - ⚡ **秒级响应**：3-8秒内返回全部数据（相比分页方式的60秒+）
     - 🎯 **性能提升95%**：从分钟级降至秒级，提升用户体验
     - 🔧 **一次请求**：避免30+次API调用的网络延迟
     - ✅ **数据完整**：确保每个卫星只返回最新的TLE记录
   - 无需参数，直接返回所有卫星的最新TLE数据
   - 响应格式：
     ```json
     {
       "success": true,
       "total": 8500,
       "executionTime": 4200,
       "batchCount": 1,
       "results": [...]
     }
     ```

## 版本更新记录

### v1.2.0 (2025-01-05) - 批量TLE查询API分页功能增强

#### 新增功能
- **批量TLE查询API分页支持**: `/orbit/bulk-tle` API现在支持完整的分页功能
  - 添加`page`参数：页码，从1开始（仅在`norad_ids`为空时生效）
  - 添加`limit`参数：每页记录数，最大10000（仅在`norad_ids`为空时生效）
  - 返回分页信息：`total`、`page`、`limit`、`totalPages`、`currentPageCount`、`hasNextPage`、`hasPrevPage`
  - 前端可以通过分页获取所有卫星TLE数据，突破1000条限制

#### 技术改进
- **分页查询优化**: 
  - 使用ES的collapse功能先统计总数，再获取分页数据
  - 每页最大支持10000条记录，确保灵活性
  - 保持数据一致性：每个卫星只返回epoch时间最新的一条记录
- **API响应结构增强**: 
  - 分页查询返回完整的分页元数据
  - 特定卫星查询保持原有简洁的响应结构
- **文档和示例更新**: 
  - 更新Swagger文档，添加分页查询示例
  - 提供前端获取所有数据的JavaScript示例代码

#### 影响范围
- **批量查询TLE数据API** (`/orbit/bulk-tle`): 支持分页查询所有卫星数据

#### 使用示例
```bash
# 获取所有卫星TLE数据 - 第一页
curl -X POST http://localhost:3001/orbit/bulk-tle \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{"norad_ids": [], "page": 1, "limit": 1000}'

# 获取所有卫星TLE数据 - 第二页
curl -X POST http://localhost:3001/orbit/bulk-tle \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{"norad_ids": [], "page": 2, "limit": 1000}'
```

#### 相关文档
- 开发日志: `@Docs/DevLog/2025-01-05_progress.md`

### v1.1.0 (2025-01-06) - 批量查询TLE数据API功能增强

#### 新增功能
- **批量查询TLE数据API增强**: `/orbit/bulk-tle` API现在支持获取所有卫星TLE数据
  - 当`norad_ids`数组为空时，返回所有卫星的TLE数据（最多1000条）
  - 返回字段：`

## 大模型配置系统

### 概述
系统支持灵活的大模型配置，默认使用阿里云Qwen模型，支持多种模式和自定义配置。

### 配置文件
主要配置文件：`config/llm.config.ts`

### 环境变量配置
在 `.env` 文件中配置以下变量：

```bash
# 通义千问API配置
QWEN_API_KEY=your_qwen_api_key
QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# 大模型配置
LLM_DEFAULT_MODEL=qwen-turbo
LLM_DEFAULT_TEMPERATURE=0.1
LLM_DEFAULT_MAX_CONCURRENT_REQUESTS=3
LLM_DEFAULT_TIMEOUT=60000
LLM_DEFAULT_MAX_RETRIES=3
LLM_DEFAULT_RETRY_DELAY=2000

# 主题提取配置
LLM_THEME_MODEL=qwen-turbo
LLM_THEME_TEMPERATURE=0.1
LLM_THEME_MAX_CONCURRENT_REQUESTS=3
LLM_THEME_TIMEOUT=60000
LLM_THEME_MAX_RETRIES=3
LLM_THEME_RETRY_DELAY=2000
```

### 模型模式

#### 1. Default模式（默认）
- **模型**: qwen-turbo
- **温度**: 0.1
- **并发数**: 3
- **适用场景**: 日常翻译和主题提取任务

#### 2. High Quality模式
- **模型**: qwen-max-latest  
- **温度**: 0.05
- **并发数**: 2
- **适用场景**: 对翻译质量要求较高的场景

#### 3. Fast模式
- **模型**: qwen-turbo
- **温度**: 0.2  
- **并发数**: 5
- **适用场景**: 需要快速处理大量数据的场景

### API使用示例

#### 翻译新闻API
```http
POST /api/es/news/translate
Content-Type: application/json

{
  "batchSize": 10,
  "maxDocs": 100,
  "forceRetranslate": false,
  "llmMode": "default",  // 可选: default, high_quality, fast
  "customModel": "qwen-turbo"  // 可选: 自定义模型名称
}
```

#### 提取主题词API
```http
POST /api/es/news/extract-themes
Content-Type: application/json

{
  "batchSize": 10,
  "maxDocs": 50,
  "forceReextract": false,
  "llmMode": "high_quality",  // 使用高质量模式
  "specificIndexes": ["news_2023_04"]
}
```

### 性能优化建议

1. **批量大小调优**
   - Default模式: 10-20个文档/批次
   - High Quality模式: 5-10个文档/批次  
   - Fast模式: 20-50个文档/批次

2. **并发控制**
   - 根据API限制调整maxConcurrentRequests
   - 监控API调用频率，避免触发限流

3. **错误处理**
   - 系统支持自动重试
   - 对于内容过滤等特殊错误会自动跳过文档
   - 可通过API查看失败记录并重试

### 监控和统计

#### 获取API调用统计
```http
GET /api/es/news/api-stats
```

#### 获取翻译状态
```http  
GET /api/es/news/translation-status
```

#### 查看失败记录
```http
GET /api/es/news/failed-translations
```

#### 重试失败任务
```http
POST /api/es/news/retry-failed
Content-Type: application/json

{
  "maxRetries": 3
}
```

## 安装和运行

### 环境要求
- Node.js 16+
- Elasticsearch 7.x+
- 阿里云通义千问API密钥

### 安装依赖
```bash
npm install
```

### 配置环境变量
复制 `config/env/example.env` 到 `.env` 并填写相关配置

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式  
npm run build
npm run start:prod
```

## API文档
启动服务后访问: http://localhost:3000/api

## 注意事项

1. **API密钥安全**: 请妥善保管API密钥，不要提交到代码仓库
2. **成本控制**: 大模型API调用会产生费用，建议设置合理的处理限制
3. **内容过滤**: 系统会自动处理敏感内容，被过滤的文档会被跳过
4. **数据备份**: 重要数据请定期备份

## 故障排除

### 常见问题

1. **API调用失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 查看API调用限制

2. **翻译质量问题**
   - 尝试使用high_quality模式
   - 检查源文本是否包含特殊字符
   - 调整温度参数

3. **性能问题**
   - 降低批处理大小
   - 调整并发数设置
   - 使用fast模式

## 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 完成大模型配置系统
- ✅ 支持qwen-turbo默认模型
- ✅ 添加多种处理模式
- ✅ 完善错误处理机制
- ✅ 优化API性能