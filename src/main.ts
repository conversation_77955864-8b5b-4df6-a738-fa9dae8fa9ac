import { NestFactory } from '@nestjs/core';
import { ValidationPipe, ValidationError, BadRequestException } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { GlobalExceptionFilter } from './global-exception.filter';
import * as winston from 'winston';
import { WinstonModule } from 'nest-winston';
import { utilities as nestWinstonModuleUtilities } from 'nest-winston/dist/winston.utilities';
import * as fs from 'fs';
import * as path from 'path';
import { TranslateNewsDto } from './elasticsearch/dto/news.dto';

async function bootstrap() {
  try {
    // 确保日志目录存在
    const logDir = path.join(__dirname, '..', 'logs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // 配置 Winston 日志
    const logger = WinstonModule.createLogger({
      transports: [
        // 错误日志
        new winston.transports.File({
          filename: path.join(logDir, 'error.log'),
          level: 'error',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          )
        }),
        // 所有日志
        new winston.transports.File({
          filename: path.join(logDir, 'combined.log'),
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          )
        }),
        // 控制台输出
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.ms(),
            nestWinstonModuleUtilities.format.nestLike('SpaceData', {
              prettyPrint: true,
              colors: true
            })
          )
        })
      ]
    });

    const app = await NestFactory.create(AppModule, {
      logger
    });

    // 启用 CORS，添加详细配置
    app.enableCors({
      origin: true, // 允许所有来源，开发环境使用
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
      allowedHeaders: ['Content-Type', 'Accept', 'Authorization'],
      exposedHeaders: ['Content-Range', 'X-Content-Range'],
      credentials: true,
      preflightContinue: false,
      optionsSuccessStatus: 204
    });

    // 配置请求体大小限制
    app.use(require('body-parser').json({ limit: '50mb' }));
    app.use(require('body-parser').urlencoded({ limit: '50mb', extended: true }));

    // 配置静态文件服务，用于提供3D Tiles点云文件
    const express = require('express');
    const publicPath = path.join(__dirname, '..', 'public');
    if (!fs.existsSync(publicPath)) {
      fs.mkdirSync(publicPath, { recursive: true });
    }
    app.use('/public', express.static(publicPath));

    // 启用全局验证管道，添加更严格的验证选项
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true, // 去除未定义的属性
      forbidNonWhitelisted: false, // 允许未定义的属性
      transform: true, // 自动转换类型
      transformOptions: {
        enableImplicitConversion: true, // 启用隐式转换
      },
      validationError: {
        target: false, // 不返回原始数据
        value: true, // 返回错误的值，方便调试
      },
      stopAtFirstError: false, // 收集所有错误
      enableDebugMessages: true, // 启用调试消息
      exceptionFactory: (errors: ValidationError[]) => {
        const messages = errors.map(error => {
          if (error.children && error.children.length > 0) {
            // 处理嵌套对象的验证错误
            return error.children.map(child => 
              Object.values(child.constraints || {})
            ).flat();
          }
          return Object.values(error.constraints || {});
        }).flat();
        return new BadRequestException(messages);
      },
    }));

    // 配置全局异常过滤器
    app.useGlobalFilters(new GlobalExceptionFilter());

    // 配置 Swagger 文档
    const config = new DocumentBuilder()
      .setTitle('太空大数据平台API')
      .setDescription(`
# 太空大数据平台后端API文档

## 功能模块
- 认证：用户注册、登录等认证相关功能
- 星座信息：星座数据查询、列表获取等
- 碎片信息：空间碎片数据查询、碎片事件检索等
- 卫星信息：卫星数据查询等

## 认证说明
大部分API需要JWT认证，请在请求头中添加：
\`\`\`
Authorization: Bearer your_jwt_token
\`\`\`

## 数据格式
- 请求/响应数据格式：application/json
- 时间格式：ISO 8601 (YYYY-MM-DDTHH:mm:ss.sssZ)
- 距离单位：千米(km)
- 速度单位：千米/秒(km/s)
- 角度单位：度(degree)

## 错误处理
所有错误响应格式统一为：
\`\`\`json
{
  "statusCode": number,
  "message": string | string[],
  "error": string
}
\`\`\`

## 使用限制
- 免费用户：每日100次API调用
- 标准用户：每日1000次API调用
- 企业用户：无限制
      `)
      .setVersion('1.0')
      .addTag('认证', '用户注册、登录等认证相关接口')
      .addTag('星座信息', '星座信息查询相关接口')
      .addTag('碎片信息', '碎片信息查询相关接口')
      .addTag('卫星信息', '卫星信息查询相关接口')
      .addTag('本地卫星信息', '本地数据库中卫星信息查询相关接口')
      .addTag('聚合任务管理', '卫星数据聚合任务监控与管理相关接口')
      .addTag('频率数据', '频率信息查询相关接口')
      .addTag('轨道数据', '轨道计算与分析相关接口')
      .addTag('漏洞信息', '漏洞信息查询相关接口')
      .addTag('新闻分析', '新闻数据分析与翻译相关接口')
      .addTag('卫星3D Tiles点云', '卫星3D点云数据生成与管理相关接口')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: '输入JWT token',
          in: 'header'
        },
        'JWT-auth'
      )
      .build();
    
    // 修改Swagger文档选项
    const options = {
      include: [],
      deepScanRoutes: true,
      ignoreGlobalPrefix: false,
      includeNotTagged: false, // 不包含未标记的接口
      extraModels: [TranslateNewsDto], // 添加额外模型
      operationIdFactory: (controllerKey: string, methodKey: string) => methodKey, // 使用方法名称作为操作ID
    };
    
    const document = SwaggerModule.createDocument(app, config, options);
    
    // 简化移除循环引用的函数，避免对安全配置造成修改
    const sanitizeDocument = (doc: any) => {
      // 确保securitySchemes是正确格式
      if (doc.components && doc.components.securitySchemes) {
        // 确保securitySchemes保持原样，不做处理
        const securitySchemes = { ...doc.components.securitySchemes };
        
        // 处理文档其余部分
        const processedDoc = JSON.parse(JSON.stringify(doc, (key, value) => {
          // 避免循环引用
          if (key !== '' && value === doc) {
            return undefined;
          }
          return value;
        }));
        
        // 恢复原始的securitySchemes
        if (processedDoc.components) {
          processedDoc.components.securitySchemes = securitySchemes;
        }
        
        return processedDoc;
      }
      
      // 如果没有安全配置，使用简单的方法去除循环引用
      return JSON.parse(JSON.stringify(doc, (key, value) => {
        if (key !== '' && value === doc) {
          return undefined;
        }
        return value;
      }));
    };
    
    // 应用改进的文档处理函数
    const sanitizedDocument = sanitizeDocument(document);

    // 配置 Swagger UI
    SwaggerModule.setup('api-docs', app, sanitizedDocument, {
      swaggerOptions: {
        persistAuthorization: true,
        docExpansion: 'none',
        filter: true,
        showRequestDuration: true,
        syntaxHighlight: {
          activate: true,
          theme: 'monokai'
        },
        defaultModelsExpandDepth: 1,  // 显示Models部分，这样请求体的模型定义会被显示
        defaultModelExpandDepth: 1,   // 展开模型的一级属性
        defaultModelRendering: 'model', // 使用模型视图而不是示例
        displayRequestDuration: true,  // 显示请求持续时间
        tagsSorter: 'alpha',           // 按字母排序标签
        operationsSorter: 'alpha',     // 按字母排序操作
        tryItOutEnabled: true,         // 默认启用Try it out
        requestSnippetsEnabled: true,  // 启用请求代码片段
        deepLinking: true,             // 启用深度链接
        layout: "StandaloneLayout",    // 使用独立布局
        validatorUrl: "",              // 禁用验证器
        withCredentials: true,         // 启用凭证
        displayOperationId: false,     // 不显示操作ID
        showExtensions: true,          // 显示扩展
        showCommonExtensions: true     // 显示通用扩展
      },
      customSiteTitle: '太空大数据平台 API 文档',
      customfavIcon: 'https://example.com/favicon.ico',
      customCss: '.swagger-ui .topbar { display: none }', // 移除隐藏models的CSS
      explorer: true
    });

    // 配置服务器超时时间
    const server = await app.listen(3001);
    const serverTimeout = parseInt(process.env.SERVER_TIMEOUT || '300000', 10); // 默认5分钟
    const keepAliveTimeout = parseInt(process.env.SERVER_KEEP_ALIVE_TIMEOUT || '305000', 10);
    const headersTimeout = parseInt(process.env.SERVER_HEADERS_TIMEOUT || '306000', 10);
    
    server.setTimeout(serverTimeout);
    server.keepAliveTimeout = keepAliveTimeout;
    server.headersTimeout = headersTimeout;
    
    console.log(`Server timeout configured: ${serverTimeout}ms`);
    console.log(`Keep-alive timeout: ${keepAliveTimeout}ms`);
    console.log(`Headers timeout: ${headersTimeout}ms`);
    
    console.log('Application is running on: http://localhost:3001');
    console.log('API documentation is available at: http://localhost:3001/api-docs');
  } catch (error) {
    console.error('Error during application bootstrap:', error);
    process.exit(1);
  }
}
bootstrap(); 