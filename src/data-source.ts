import { DataSource } from 'typeorm';
import { Satellite } from './entities/satellite.entity';
import { User } from './entities/user.entity';
import { AddUsersField1710754801389 } from './migrations/1710754801389-AddUsersField';
import { AddUserApprovalStatus1710900000000 } from './migrations/1710900000000-AddUserApprovalStatus';
import { UpdateApprovalFieldComments1748500000000 } from './migrations/1748500000000-UpdateApprovalFieldComments';

// 加载环境变量
require('dotenv').config();

export const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  database: process.env.DB_DATABASE || 'spacedata',
  entities: [Satellite, User],
  migrations: [AddUsersField1710754801389, AddUserApprovalStatus1710900000000, UpdateApprovalFieldComments1748500000000],
  synchronize: false,
}); 