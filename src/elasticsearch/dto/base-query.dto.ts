import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsInt, Min, IsString, IsObject } from 'class-validator';
import { Type } from 'class-transformer';

export class BaseQueryDto {
  @ApiProperty({
    description: '页码',
    default: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: '每页数量',
    default: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number = 10;

  @ApiProperty({
    description: '排序字段',
    required: false,
    example: { "news_id": 'desc' },
    default: { "publish_date.year": 'desc', "publish_date.month": 'desc', "publish_date.day": 'desc' }
  })
  @IsOptional()
  @IsObject()
  sort?: Record<string, 'asc' | 'desc'>;

  @ApiProperty({
    description: '搜索关键词',
    required: false,
  })
  @IsOptional()
  @IsString()
  keyword?: string;
}