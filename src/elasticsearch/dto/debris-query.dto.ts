import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsEnum, ValidateNested, IsInt, Min, IsObject, IsNotEmpty } from 'class-validator';
import { Type } from 'class-transformer';
import { BaseQueryDto } from './base-query.dto';

export enum MatchType {
  EXACT = 'exact',
  FUZZY = 'fuzzy'
}

export class FieldMatchConfig {
  @ApiProperty({
    description: '匹配类型',
    enum: MatchType,
    example: 'exact'
  })
  @IsEnum(MatchType)
  matchType!: MatchType;

  @ApiProperty({
    description: '字段值',
    example: '1999-025EZ'
  })
  @IsNotEmpty({ message: '字段值不能为空' })
  value!: string | number;
}

export class DebrisQueryDto {
  @ApiProperty({
    description: '页码',
    required: false,
    default: 1,
    minimum: 1
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({
    description: '每页数量',
    required: false,
    default: 10,
    minimum: 1
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  limit?: number = 10;

  @ApiProperty({
    description: '排序配置',
    required: false,
    example: { field: 'norad_id', order: 'desc' }
  })
  @IsOptional()
  @IsObject()
  sort?: { field: string; order: 'asc' | 'desc' };

  @ApiProperty({
    description: '关键词搜索',
    required: false,
    example: 'International Space Station'
  })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({
    description: 'COSPAR ID查询配置',
    required: false,
    type: FieldMatchConfig,
    example: {
      matchType: 'exact',
      value: '1998-067A'
    }
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => FieldMatchConfig)
  cospar_id?: FieldMatchConfig;

  @ApiProperty({
    description: 'NORAD ID查询配置',
    required: false,
    type: FieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => FieldMatchConfig)
  norad_id?: FieldMatchConfig;

  @ApiProperty({
    description: '碎片名称',
    required: false,
    example: 'ISS'
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: '所属国家查询配置',
    required: false,
    type: FieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => FieldMatchConfig)
  country?: FieldMatchConfig;

  @ApiProperty({
    description: '发射日期查询配置',
    required: false,
    type: FieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => FieldMatchConfig)
  launch_date?: FieldMatchConfig;

  @ApiProperty({
    description: '发射场查询配置',
    required: false,
    type: FieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => FieldMatchConfig)
  launch_site?: FieldMatchConfig;

  @ApiProperty({
    description: '雷达散射截面大小查询配置',
    required: false,
    type: FieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => FieldMatchConfig)
  rcs_size?: FieldMatchConfig;

  @ApiProperty({
    description: '坠毁日期查询配置',
    required: false,
    type: FieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => FieldMatchConfig)
  decay?: FieldMatchConfig;

  @ApiProperty({
    description: '首次观测时间查询配置',
    required: false,
    type: FieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => FieldMatchConfig)
  first_epoch?: FieldMatchConfig;

  @ApiProperty({
    description: '对象类型查询配置',
    required: false,
    type: FieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => FieldMatchConfig)
  object_class?: FieldMatchConfig;

  @ApiProperty({
    description: '任务名称',
    required: false,
    example: 'International Space Station'
  })
  @IsOptional()
  @IsString()
  mission?: string;

  @ApiProperty({
    description: '轨道周期范围（分钟）',
    required: false,
    example: { min: 90, max: 120 }
  })
  @IsOptional()
  @IsObject()
  period_minutes_range?: { min?: number; max?: number };

  @ApiProperty({
    description: '轨道倾角范围（度）',
    required: false,
    example: { min: 45, max: 60 }
  })
  @IsOptional()
  @IsObject()
  incl_degrees_range?: { min?: number; max?: number };

  @ApiProperty({
    description: '远地点高度范围（km）',
    required: false,
    example: { min: 500, max: 800 }
  })
  @IsOptional()
  @IsObject()
  apogee_km_range?: { min?: number; max?: number };

  @ApiProperty({
    description: '近地点高度范围（km）',
    required: false,
    example: { min: 400, max: 600 }
  })
  @IsOptional()
  @IsObject()
  perigee_km_range?: { min?: number; max?: number };
} 