import { Injectable, Logger } from '@nestjs/common';
import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { DebrisQueryDto, FieldMatchConfig, MatchType } from '../dto/debris-query.dto';
import { DebrisSearchResponse, DebrisItemWithMatch } from '../interfaces/debris-search-response.interface';
import { DebrisEventSearchResponse } from '../interfaces/debris-event-search-response.interface';
import { SearchResponse, SearchTotalHits } from '@elastic/elasticsearch/lib/api/types';

/**
 * Elasticsearch碎片查询服务
 * 提供碎片信息和碎片事件的查询功能
 */
@Injectable()
export class ElasticsearchDebrisService {
  private readonly logger = new Logger(ElasticsearchDebrisService.name);
  private readonly debrisDiscosIndex = 'debris_discos';
  private readonly debrisSpacetrackIndex = 'debris_spacetrack';
  private readonly debrisEventIndex = 'event_discos';

  constructor(private readonly esBaseService: ElasticsearchBaseService) {
    // 初始化反向映射表（中文 -> 英文）
    this.reverseCountryNameMap = Object.entries(this.countryNameMap).reduce((acc, [code, name]) => {
      acc[name] = code;
      return acc;
    }, {} as Record<string, string>);
  }

  /**
   * 国家名称映射表（英文 -> 中文）
   * @private
   */
  private readonly countryNameMap: Record<string, string> = {
    'USA': '美国',
    'US': '美国',
    'CIS': '独联体',
    'PRC': '中国',
    'JPN': '日本',
    'UK': '英国',
    'FR': '法国',
    'IN': '印度',
    'IND': '印度',
    'ESA': '欧洲航天局',
    'CA': '加拿大',
    'IT': '意大利',
    'KR': '韩国',
    'ASRA': '澳大利亚',
    'ISR': '以色列',
    'ISRA': '以色列',
    'BR': '巴西',
    'DE': '德国',
    'GER': '德国',
    'ES': '西班牙',
    'UA': '乌克兰',
    'NL': '荷兰',
    'TH': '泰国',
    'IR': '伊朗',
    'IRAN': '伊朗',
    'PK': '巴基斯坦',
    'AE': '阿联酋',
    'SG': '新加坡',
    'MY': '马来西亚',
    'ID': '印度尼西亚',
    'AR': '阿根廷',
    'ARGN': '阿根廷',
    'MX': '墨西哥',
    'CL': '智利',
    'VE': '委内瑞拉',
    'ZA': '南非',
    'NG': '尼日利亚',
    'EG': '埃及',
    'DZ': '阿尔及利亚',
    'MA': '摩洛哥',
    'TN': '突尼斯',
    'KE': '肯尼亚',
    'ET': '埃塞俄比亚',
    'GH': '加纳',
    'SA': '沙特阿拉伯',
    'QA': '卡塔尔',
    'KW': '科威特',
    'BH': '巴林',
    'OM': '阿曼',
    'JO': '约旦',
    'LB': '黎巴嫩',
    'SY': '叙利亚',
    'IQ': '伊拉克',
    'TR': '土耳其',
    'GR': '希腊',
    'PT': '葡萄牙',
    'BE': '比利时',
    'LU': '卢森堡',
    'CH': '瑞士',
    'AT': '奥地利',
    'SE': '瑞典',
    'NO': '挪威',
    'DK': '丹麦',
    'FI': '芬兰',
    'IE': '爱尔兰',
    'IS': '冰岛',
    'PL': '波兰',
    'CZ': '捷克',
    'SK': '斯洛伐克',
    'HU': '匈牙利',
    'RO': '罗马尼亚',
    'BG': '保加利亚',
    'RS': '塞尔维亚',
    'HR': '克罗地亚',
    'SI': '斯洛文尼亚',
    'AL': '阿尔巴尼亚',
    'MK': '北马其顿',
    'ME': '黑山',
    'BA': '波斯尼亚和黑塞哥维那',
    'MD': '摩尔多瓦',
    'BY': '白俄罗斯',
    'LT': '立陶宛',
    'LV': '拉脱维亚',
    'EE': '爱沙尼亚',
    'AM': '亚美尼亚',
    'AZ': '阿塞拜疆',
    'GE': '格鲁吉亚',
    'KZ': '哈萨克斯坦',
    'UZ': '乌兹别克斯坦',
    'TM': '土库曼斯坦',
    'KG': '吉尔吉斯斯坦',
    'TJ': '塔吉克斯坦',
    'MN': '蒙古',
    'KP': '朝鲜',
    'NKOR': '朝鲜',
    'VN': '越南',
    'LA': '老挝',
    'KH': '柬埔寨',
    'MM': '缅甸',
    'BD': '孟加拉国',
    'NP': '尼泊尔',
    'LK': '斯里兰卡',
    'BT': '不丹',
    'MV': '马尔代夫',
    'PH': '菲律宾',
    'BN': '文莱',
    'TL': '东帝汶',
    'PG': '巴布亚新几内亚',
    'FJ': '斐济',
    'SB': '所罗门群岛',
    'VU': '瓦努阿图',
    'NZ': '新西兰',
    'CU': '古巴',
    'DO': '多米尼加共和国',
    'HT': '海地',
    'JM': '牙买加',
    'BS': '巴哈马',
    'BB': '巴巴多斯',
    'TT': '特立尼达和多巴哥',
    'PA': '巴拿马',
    'CR': '哥斯达黎加',
    'NI': '尼加拉瓜',
    'HN': '洪都拉斯',
    'SV': '萨尔瓦多',
    'GT': '危地马拉',
    'BZ': '伯利兹',
    'CO': '哥伦比亚',
    'PE': '秘鲁',
    'EC': '厄瓜多尔',
    'BO': '玻利维亚',
    'PY': '巴拉圭',
    'UY': '乌拉圭',
    'GY': '圭亚那',
    'SR': '苏里南',
    'GF': '法属圭亚那',
    'CSSI': '国际空间站',
    'ISS': '国际空间站',
    'O3B': 'O3b网络',
    'GLOB': '全球星',
    'IRID': '铱星',
    'ONEW': '万维网',
    'MESH': '星链',
    'UNKN': '未知',
    'TBD': '待定',
    'TWN': '台湾',
    'CHBZ': '中巴联合',
    'EUME': '欧洲-中东联合',
    'ITSO': '国际通信卫星组织',
    'ORB': '轨道科学公司',
    'SEAL': '海洋发射平台'
  };

  /**
   * 国家名称反向映射表（中文 -> 英文）
   * @private
   */
  private readonly reverseCountryNameMap: Record<string, string>;

  /**
   * 获取国家代码
   * 如果输入是中文国家名称，返回对应的英文代码
   * 如果输入是英文代码，则原样返回
   * @param countryName 国家名称（中文或英文代码）
   * @returns 标准化的国家代码
   * @private
   */
  private getCountryCode(countryName: string): string {
    // 如果是英文代码，直接返回
    if (this.countryNameMap[countryName]) {
      return countryName;
    }
    
    // 如果是中文名称，返回对应的英文代码
    if (this.reverseCountryNameMap[countryName]) {
      return this.reverseCountryNameMap[countryName];
    }
    
    // 如果都不匹配，返回原始值
    return countryName;
  }

  /**
   * 获取所有可能的国家名称表示（包括中英文）
   * @param countryName 国家名称（中文或英文代码）
   * @returns 所有可能的表示方式数组
   * @private
   */
  private getAllCountryNameVariants(countryName: string): string[] {
    const variants = new Set<string>();
    
    // 添加原始输入
    variants.add(countryName);
    
    // 如果是英文代码，添加对应的中文名称
    if (this.countryNameMap[countryName]) {
      variants.add(this.countryNameMap[countryName]);
    }
    
    // 如果是中文名称，添加对应的英文代码
    if (this.reverseCountryNameMap[countryName]) {
      variants.add(this.reverseCountryNameMap[countryName]);
    }
    
    return Array.from(variants);
  }

  /**
   * 搜索碎片信息
   * @param query 碎片查询参数
   * @returns 碎片搜索结果
   */
  async searchDebris(query: DebrisQueryDto): Promise<DebrisSearchResponse> {
    try {
      this.logger.debug(`搜索碎片信息，参数: ${JSON.stringify(query)}`);
      
      const { page = 1, limit = 10, sort, period_minutes_range, incl_degrees_range, apogee_km_range, perigee_km_range, ...filters } = query;
      // 默认按相关性分数排序，而不是按norad_id排序
      const sortField = sort?.field;
      const sortOrder = sort?.order || 'desc';
      
      // 检查是否只有轨道参数范围条件
      const hasOrbitParamsOnly = (
        (period_minutes_range !== undefined || 
         incl_degrees_range !== undefined || 
         apogee_km_range !== undefined || 
         perigee_km_range !== undefined) && 
        Object.keys(filters).length === 0
      );
      
      this.logger.debug(`是否只有轨道参数范围条件: ${hasOrbitParamsOnly}`);
      
      let aggregatedResults: DebrisItemWithMatch[] = [];
      
      if (hasOrbitParamsOnly) {
        // 如果只有轨道参数范围条件，直接从debris_spacetrack索引中搜索
        this.logger.debug('只有轨道参数范围条件，直接从debris_spacetrack索引中搜索');
        aggregatedResults = await this.searchDebrisByOrbitParams(
          { period_minutes_range, incl_degrees_range, apogee_km_range, perigee_km_range },
          page,
          limit,
          sortField,
          sortOrder
        );
      } else {
        // 构建查询条件
        const must = this.buildDebrisQueryConditions(filters);
        
        // 首先查询debris_discos索引
        let discosResults = await this.searchDiscosIndex(must, page, limit, sortField, sortOrder);
        
        // 如果debris_discos索引中有匹配的文档，则与debris_spacetrack索引中的文档进行聚合
        if (discosResults.length > 0) {
          // 提取所有匹配的cospar_id
          const cosparIds = discosResults.map(item => item.cospar_id).filter((id): id is string => !!id);
          
          this.logger.debug(`从debris_discos结果中提取的cospar_id: ${JSON.stringify(cosparIds)}`);
          
          if (cosparIds.length > 0) {
            // 查询debris_spacetrack索引中匹配的文档
            const spacetrackResults = await this.searchSpacetrackByCosparIds(cosparIds);
            
            this.logger.debug(`在debris_spacetrack中找到匹配的文档数量: ${spacetrackResults.length}`);
            
            // 聚合结果
            aggregatedResults = this.aggregateResults(discosResults, spacetrackResults);
            
            this.logger.debug(`聚合后的结果包含field_values的项目数: ${aggregatedResults.filter(item => item.field_values && Object.keys(item.field_values).length > 0).length}`);
          } else {
            // 如果没有cospar_id，则直接使用discos结果
            this.logger.debug(`没有找到有效的cospar_id，使用原始discos结果`);
            aggregatedResults = discosResults;
          }
        } else {
          // 如果debris_discos索引中没有匹配的文档，则直接查询debris_spacetrack索引
          this.logger.debug(`debris_discos索引中没有匹配的文档，直接查询debris_spacetrack索引`);
          aggregatedResults = await this.searchSpacetrackIndex(must, page, limit, sortField, sortOrder);
        }
      }
      
      // 应用范围过滤
      const filteredResults = this.applyRangeFilters(aggregatedResults, {
          period_minutes_range,
          incl_degrees_range,
          apogee_km_range,
          perigee_km_range
        });
        
      // 计算匹配分数并排序
      const scoredResults = this.calculateMatchScores(filteredResults, filters, {
        period_minutes_range,
        incl_degrees_range,
        apogee_km_range,
        perigee_km_range
      });
      
      // 只保留指定的字段
      const finalResults = scoredResults.map(item => {
        // 保留基本字段和匹配相关字段，不再从field_values提取值到主对象
        return {
          _id: item._id,
          cospar_id: item.cospar_id,
          match_score: item.match_score,
          field_values: item.field_values || {},
          matched_fields: item.matched_fields || {},
          matched_fields_description: item.matched_fields_description || [],
          matched_fields_count: item.matched_fields_count || 0
        };
      });
      
      // 构造返回结果
      return {
        total: finalResults.length,
        page,
        size: limit,
        items: finalResults.slice((page - 1) * limit, page * limit)
      };
    } catch (error) {
      this.logger.error(`搜索碎片信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 查询debris_discos索引
   * @param must 查询条件
   * @param page 页码
   * @param limit 每页数量
   * @param sortField 排序字段，如果为undefined则按相关性排序
   * @param sortOrder 排序顺序
   * @returns 查询结果
   * @private
   */
  private async searchDiscosIndex(must: any[], page: number, limit: number, sortField?: string, sortOrder: string = 'desc'): Promise<DebrisItemWithMatch[]> {
    const searchBody: any = {
      query: {
        bool: {
          must,
        },
      },
      from: 0,
      size: 1000, // 获取足够多的结果用于后续聚合
    };
    
    // 如果有明确的排序字段，则使用该字段排序，否则按相关性分数排序
    if (sortField) {
      searchBody.sort = [
        {
          [sortField]: {
            order: sortOrder,
          },
        },
      ];
    }
    
    this.logger.debug(`查询debris_discos索引，查询体: ${JSON.stringify(searchBody)}`);
    
      const searchResult = await this.esBaseService.search({
      index: this.debrisDiscosIndex,
      body: searchBody,
    });
    
    const totalHits = (searchResult.hits.total as SearchTotalHits).value || 0;
    this.logger.debug(`debris_discos索引查询结果总数: ${totalHits}`);
    
    if (totalHits === 0) {
      this.logger.debug(`在debris_discos索引中未找到匹配的文档`);
      return [];
    }
    
    return searchResult.hits.hits.map((hit: any) => ({
      ...hit._source,
      _id: hit._id,
      _index: this.debrisDiscosIndex,
      match_score: hit._score,
    }));
  }

  /**
   * 查询debris_spacetrack索引
   * @param must 查询条件
   * @param page 页码
   * @param limit 每页数量
   * @param sortField 排序字段，如果为undefined则按相关性排序
   * @param sortOrder 排序顺序
   * @returns 查询结果
   * @private
   */
  private async searchSpacetrackIndex(must: any[], page: number, limit: number, sortField?: string, sortOrder: string = 'desc'): Promise<DebrisItemWithMatch[]> {
    const searchBody: any = {
          query: {
            bool: {
              must,
            },
          },
      from: 0,
      size: 1000, // 获取足够多的结果用于后续处理
    };
    
    // 如果有明确的排序字段，则使用该字段排序，否则按相关性分数排序
    if (sortField) {
      searchBody.sort = [
            {
              [sortField]: {
                order: sortOrder,
              },
            },
      ];
    }
    
    const searchResult = await this.esBaseService.search({
      index: this.debrisSpacetrackIndex,
      body: searchBody,
    });
    
    // 修改返回结果，确保设置field_values字段
    return searchResult.hits.hits.map((hit: any) => {
      const source = hit._source;
      const result: DebrisItemWithMatch = {
        ...source,
        _id: hit._id,
        _index: this.debrisSpacetrackIndex,
        match_score: hit._score,
        source_index: this.debrisSpacetrackIndex,
        source_fields: {},
        field_values: {}
      };
      
      // 为所有字段添加来源记录
      for (const key of Object.keys(source)) {
        if (key === '_id' || key === '_index' || key === 'match_score' || 
            key === 'source_fields' || key === 'source_index' || key === 'field_values') continue;
        
        const value = source[key];
        if (value !== undefined && result.source_fields && result.field_values) {
          result.source_fields[key] = this.debrisSpacetrackIndex;
          result.field_values[key] = [
            { value, source: this.debrisSpacetrackIndex }
          ];
        }
      }
      
      return result;
    });
  }

  /**
   * 根据cospar_id查询debris_spacetrack索引
   * @param cosparIds cospar_id数组
   * @returns 查询结果
   * @private
   */
  private async searchSpacetrackByCosparIds(cosparIds: string[]): Promise<DebrisItemWithMatch[]> {
    this.logger.debug(`根据cospar_id搜索debris_spacetrack索引，cospar_ids: ${JSON.stringify(cosparIds)}`);
    
    // 使用bool查询构建多个should条件，避免terms查询可能的格式问题
    const should = cosparIds.map(cosparId => ({
      term: {
        'cospar_id.keyword': {
          value: cosparId
        }
      }
    }));
    
    // 添加更详细的查询日志
    this.logger.debug(`构建的should查询条件: ${JSON.stringify(should)}`);
    
    const searchBody: any = {
      query: {
        bool: {
          should: should,
          minimum_should_match: 1
        }
      },
      size: 1000 // 获取足够多的结果用于后续聚合
    };
    
    // 按_id降序排序，确保获取最新的文档
    searchBody.sort = [
      {
        _id: {
          order: 'desc'
        }
      }
    ];
    
    try {
      const searchResult = await this.esBaseService.search({
        index: this.debrisSpacetrackIndex,
        body: searchBody
      });
      
      const results = searchResult.hits.hits.map((hit: any) => {
        this.logger.debug(`找到匹配文档: _id=${hit._id}, cospar_id=${hit._source.cospar_id}, source=${JSON.stringify(hit._source)}`);
        
        const source = hit._source;
        const result: DebrisItemWithMatch = {
          ...source,
          _id: hit._id,
          _index: this.debrisSpacetrackIndex,
          match_score: hit._score,
          source_index: this.debrisSpacetrackIndex,
          source_fields: {},
          field_values: {}
        };
        
        // 为所有字段添加来源记录
        for (const key of Object.keys(source)) {
          if (key === '_id' || key === '_index' || key === 'match_score' || 
              key === 'source_fields' || key === 'source_index' || key === 'field_values') continue;
          
          const value = source[key];
          if (value !== undefined && result.source_fields && result.field_values) {
            result.source_fields[key] = this.debrisSpacetrackIndex;
            result.field_values[key] = [
              { value, source: this.debrisSpacetrackIndex }
            ];
          }
        }
        
        return result;
      });
      
      // 打印找到的cospar_id和对应的文档数量
      const foundCosparIds = results.map(item => item.cospar_id);
      const notFoundCosparIds = cosparIds.filter(id => !foundCosparIds.includes(id));
      
      this.logger.debug(`从debris_spacetrack索引中找到${results.length}个匹配文档`);
      this.logger.debug(`找到的cospar_ids: ${JSON.stringify(foundCosparIds)}`);
      this.logger.debug(`未找到的cospar_ids: ${JSON.stringify(notFoundCosparIds)}`);
      
      return results;
    } catch (error) {
      this.logger.error(`搜索debris_spacetrack索引失败: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * 聚合debris_discos和debris_spacetrack的结果
   * @param discosResults debris_discos查询结果
   * @param spacetrackResults debris_spacetrack查询结果
   * @returns 聚合后的结果
   * @private
   */
  private aggregateResults(discosResults: DebrisItemWithMatch[], spacetrackResults: DebrisItemWithMatch[]): DebrisItemWithMatch[] {
    const aggregated: DebrisItemWithMatch[] = [];
    
    this.logger.debug(`开始聚合结果: discos结果数量=${discosResults.length}, spacetrack结果数量=${spacetrackResults.length}`);
    
    // 按cospar_id对spacetrack结果进行分组，并保留_id最大的文档
    const spacetrackByCosparId = new Map<string, DebrisItemWithMatch>();
    
    // 详细记录所有spacetrack文档
    this.logger.debug(`处理前的spacetrack文档: ${JSON.stringify(spacetrackResults.map(item => ({ 
      _id: item._id, 
      cospar_id: item.cospar_id, 
      name: item.name 
    })))}`);
    
    for (const item of spacetrackResults) {
      if (!item.cospar_id) {
        this.logger.debug(`跳过没有cospar_id的spacetrack文档: _id=${item._id}`);
        continue;
      }
      
      const existing = spacetrackByCosparId.get(item.cospar_id);
      if (!existing || item._id > existing._id) {
        spacetrackByCosparId.set(item.cospar_id, item);
        this.logger.debug(`为cospar_id=${item.cospar_id}设置spacetrack文档: _id=${item._id}`);
      } else {
        this.logger.debug(`忽略cospar_id=${item.cospar_id}的旧spacetrack文档: _id=${item._id}, 已有_id=${existing._id}`);
      }
    }
    
    this.logger.debug(`整理后的spacetrack文档数量: ${spacetrackByCosparId.size}, cospar_ids: ${JSON.stringify([...spacetrackByCosparId.keys()])}`);
    
    // 列出所有discos文档的cospar_id
    this.logger.debug(`所有discos文档的cospar_id: ${JSON.stringify(discosResults.map(item => item.cospar_id))}`);
    
    // 聚合discos和spacetrack结果
    let aggregatedWithFieldValues = 0;
    
    for (const discosItem of discosResults) {
      if (!discosItem.cospar_id) {
        this.logger.debug(`跳过没有cospar_id的discos文档: _id=${discosItem._id}`);
        // 如果没有cospar_id，直接添加discos文档
        aggregated.push({
          ...discosItem,
          source_index: this.debrisDiscosIndex
        });
        continue;
      }
      
      const spacetrackItem = spacetrackByCosparId.get(discosItem.cospar_id);
      
      if (!spacetrackItem) {
        // 如果没有匹配的spacetrack文档，直接添加discos文档
        this.logger.debug(`没有找到cospar_id=${discosItem.cospar_id}的spacetrack文档`);
        const modifiedDiscosItem: DebrisItemWithMatch = {
          ...discosItem,
          source_index: this.debrisDiscosIndex,
          source_fields: {},
          field_values: {}
        };
        
        // 为所有字段添加来源记录
        for (const key of Object.keys(discosItem)) {
          if (key === '_id' || key === '_index' || key === 'match_score' || 
              key === 'source_fields' || key === 'source_index' || key === 'field_values') continue;
          
          const value = discosItem[key];
          if (value !== undefined && modifiedDiscosItem.source_fields && modifiedDiscosItem.field_values) {
            modifiedDiscosItem.source_fields[key] = this.debrisDiscosIndex;
            modifiedDiscosItem.field_values[key] = [
              { value, source: this.debrisDiscosIndex }
            ];
          }
        }
        
        aggregated.push(modifiedDiscosItem);
      } else {
        // 聚合discos和spacetrack文档
        this.logger.debug(`为cospar_id=${discosItem.cospar_id}聚合文档，discos文档_id=${discosItem._id}，spacetrack文档_id=${spacetrackItem._id}`);
        this.logger.debug(`discos文档: ${JSON.stringify(discosItem)}`);
        this.logger.debug(`spacetrack文档: ${JSON.stringify(spacetrackItem)}`);
        
        const aggregatedItem: DebrisItemWithMatch = {
          _id: discosItem._id,
          cospar_id: discosItem.cospar_id,
          match_score: discosItem.match_score,
          source_fields: {}, // 记录字段来源
          field_values: {}   // 存储不同索引的不同值
        };
        
        // 合并所有字段，并标记字段来源
        const allKeys = new Set([
          ...Object.keys(discosItem),
          ...Object.keys(spacetrackItem)
        ]);
        
        this.logger.debug(`合并字段列表: ${JSON.stringify([...allKeys])}`);
        
        for (const key of allKeys) {
          if (key === '_id' || key === '_index' || key === 'match_score' || 
              key === 'source_fields' || key === 'source_index' || key === 'field_values') continue;
          
          const discosValue = discosItem[key];
          const spacetrackValue = spacetrackItem[key];
          
          this.logger.debug(`处理字段 ${key}: discos值=${JSON.stringify(discosValue)}, spacetrack值=${JSON.stringify(spacetrackValue)}`);
          
          if (discosValue !== undefined && spacetrackValue !== undefined) {
            // 深度比较，避免对象引用比较
            const isValueEqual = JSON.stringify(discosValue) === JSON.stringify(spacetrackValue);
            
            // 默认使用discos的值
            aggregatedItem[key] = discosValue;
            
            // 记录字段来源
            if (aggregatedItem.source_fields) {
              aggregatedItem.source_fields[key] = this.debrisDiscosIndex;
            }
            
            // 无论值是否相同，都创建字段值数组
            if (isValueEqual) {
              // 值相同，只记录一个来源
              if (aggregatedItem.field_values) {
                aggregatedItem.field_values[key] = [
                  { value: discosValue, source: this.debrisDiscosIndex }
                ];
              }
            } else {
              // 值不同，记录两个来源
              this.logger.debug(`字段${key}在两个索引中值不同: discos=${JSON.stringify(discosValue)}, spacetrack=${JSON.stringify(spacetrackValue)}`);
              if (aggregatedItem.field_values) {
                aggregatedItem.field_values[key] = [
                  { value: discosValue, source: this.debrisDiscosIndex },
                  { value: spacetrackValue, source: this.debrisSpacetrackIndex }
                ];
              }
              aggregatedWithFieldValues++;
            }
          } else if (discosValue !== undefined) {
            // 使用discos的值
            aggregatedItem[key] = discosValue;
            if (aggregatedItem.source_fields) {
              aggregatedItem.source_fields[key] = this.debrisDiscosIndex;
            }
            if (aggregatedItem.field_values) {
              aggregatedItem.field_values[key] = [
                { value: discosValue, source: this.debrisDiscosIndex }
              ];
            }
          } else if (spacetrackValue !== undefined) {
            // 使用spacetrack的值
            aggregatedItem[key] = spacetrackValue;
            if (aggregatedItem.source_fields) {
              aggregatedItem.source_fields[key] = this.debrisSpacetrackIndex;
            }
            if (aggregatedItem.field_values) {
              aggregatedItem.field_values[key] = [
                { value: spacetrackValue, source: this.debrisSpacetrackIndex }
              ];
            }
          }
        }
        
        this.logger.debug(`聚合后的文档: ${JSON.stringify({
          _id: aggregatedItem._id,
          cospar_id: aggregatedItem.cospar_id,
          name: aggregatedItem.name,
          source_fields: aggregatedItem.source_fields,
          field_values_keys: aggregatedItem.field_values ? Object.keys(aggregatedItem.field_values) : []
        })}`);
        
        aggregated.push(aggregatedItem);
      }
    }
    
    this.logger.debug(`聚合完成: 总结果数量=${aggregated.length}, 包含field_values的项目数=${aggregatedWithFieldValues}`);
    
    // 处理未在discos中但在spacetrack中的文档
    for (const [cosparId, spacetrackItem] of spacetrackByCosparId.entries()) {
      if (!discosResults.some(item => item.cospar_id === cosparId)) {
        this.logger.debug(`添加仅在spacetrack中的文档: cospar_id=${cosparId}, _id=${spacetrackItem._id}`);
        const modifiedSpacetrackItem: DebrisItemWithMatch = {
          ...spacetrackItem,
          source_index: this.debrisSpacetrackIndex,
          source_fields: {},
          field_values: {}
        };
        
        // 为所有字段添加来源记录
        for (const key of Object.keys(spacetrackItem)) {
          if (key === '_id' || key === '_index' || key === 'match_score' || 
              key === 'source_fields' || key === 'source_index' || key === 'field_values') continue;
          
          const value = spacetrackItem[key];
          if (value !== undefined && modifiedSpacetrackItem.source_fields && modifiedSpacetrackItem.field_values) {
            modifiedSpacetrackItem.source_fields[key] = this.debrisSpacetrackIndex;
            modifiedSpacetrackItem.field_values[key] = [
              { value, source: this.debrisSpacetrackIndex }
            ];
          }
        }
        
        aggregated.push(modifiedSpacetrackItem);
      }
    }
    
    return aggregated;
  }

  /**
   * 应用范围过滤
   * @param items 待过滤的项
   * @param ranges 范围条件
   * @returns 过滤后的结果
   * @private
   */
  private applyRangeFilters(items: DebrisItemWithMatch[], ranges: {
    period_minutes_range?: { min?: number; max?: number };
    incl_degrees_range?: { min?: number; max?: number };
    apogee_km_range?: { min?: number; max?: number };
    perigee_km_range?: { min?: number; max?: number };
  }): DebrisItemWithMatch[] {
    if (!ranges.period_minutes_range && !ranges.incl_degrees_range && 
        !ranges.apogee_km_range && !ranges.perigee_km_range) {
      return items;
    }
    
    return items.filter(item => {
      const rangeMatches = this.checkRangeFieldsMatch(item, ranges);
      return rangeMatches.allMatched;
    });
  }

  /**
   * 计算匹配分数并排序结果
   * @param items 待计算的项
   * @param filters 过滤条件
   * @param ranges 范围条件
   * @returns 计算分数后的结果
   * @private
   */
  private calculateMatchScores(items: DebrisItemWithMatch[], filters: Partial<DebrisQueryDto>, ranges: {
    period_minutes_range?: { min?: number; max?: number };
    incl_degrees_range?: { min?: number; max?: number };
    apogee_km_range?: { min?: number; max?: number };
    perigee_km_range?: { min?: number; max?: number };
  }): DebrisItemWithMatch[] {
    return items.map(item => {
      // 检查范围字段是否匹配
      const rangeFieldsMatches = this.checkRangeFieldsMatch(item, ranges);
      
      // 计算匹配的字段
      const matchedFields = this.calculateMatchedFields(item, filters, rangeFieldsMatches.matches);
      
      // 构造匹配字段描述
      const matchedFieldsArr = Object.entries(matchedFields).filter(([_, matched]) => matched).map(([field]) => field);
      const matchedFieldsDescription = this.createMatchFieldsDescription(matchedFields);
      
      return {
        ...item,
        matched_fields: matchedFields,
        matched_fields_description: matchedFieldsDescription,
        matched_fields_count: matchedFieldsArr.length,
        
        // 避免将_spacetrack后缀的字段计入相关性排序计算
        match_score: item.match_score || 
                     (matchedFieldsArr.length + 
                      matchedFieldsDescription.reduce((sum, desc) => sum + desc.score, 0))
      };
    }).sort((a, b) => {
      if (a.match_score !== b.match_score) {
        return b.match_score - a.match_score; // 按匹配分数降序排序
      }
      
      if (a.matched_fields_count !== b.matched_fields_count) {
        return b.matched_fields_count - a.matched_fields_count; // 按匹配字段数量降序排序
      }
      
      return (b._id || '').localeCompare(a._id || ''); // 按ID降序排序
    });
  }

  /**
   * 构建碎片查询条件
   * @param filters 过滤条件
   * @returns 查询条件数组
   * @private
   */
  private buildDebrisQueryConditions(filters: Partial<DebrisQueryDto>): any[] {
    const must = [];
    
    // 添加关键词搜索
    if (filters.keyword) {
      must.push({
        multi_match: {
          query: filters.keyword,
          fields: ['name^2', 'cospar_id^2', 'country', 'mission^1.5', 'object_class', 'launch_site', 'rcs_size'], // 移除decay字段，它是日期类型
          type: 'best_fields',
          fuzziness: 'AUTO',
          operator: 'OR',
          minimum_should_match: '70%'
        },
      });
    }
    
    // 精确匹配字段: cospar_id, norad_id
    if (filters.cospar_id) {
      this.logger.debug(`添加cospar_id查询条件: ${JSON.stringify(filters.cospar_id)}`);
      
      // 尝试两种可能的字段格式
      if (filters.cospar_id.matchType === MatchType.EXACT) {
        this.logger.debug(`使用精确匹配查询cospar_id: ${filters.cospar_id.value.toString()}`);
        must.push({
          bool: {
            should: [
              { term: { "cospar_id": filters.cospar_id.value.toString() } },
              { term: { "cospar_id.keyword": filters.cospar_id.value.toString() } }
            ],
            minimum_should_match: 1
          }
        });
      } else {
        this.logger.debug(`使用模糊匹配查询cospar_id: ${filters.cospar_id.value.toString()}`);
        must.push({
          match: {
            cospar_id: {
              query: filters.cospar_id.value.toString(),
              fuzziness: "AUTO"
            }
          }
        });
      }
    }
    
    if (filters.norad_id) {
      must.push({
        term: {
          norad_id: filters.norad_id.value.toString()
        }
      });
    }
    
    // 相似度匹配字段: name, mission
    if (filters.name) {
      const nameQuery = {
        match: {
          name: {
            query: filters.name,
            fuzziness: 'AUTO',
            operator: 'OR'
          }
        }
      };
      
      must.push(nameQuery);
    }
    
    if (filters.mission) {
      const missionQuery = {
        match: {
          mission: {
            query: filters.mission,
            fuzziness: 'AUTO',
            operator: 'OR'
          }
        }
      };
      
      must.push(missionQuery);
    }
    
    // 含义匹配字段: country, object_class
    if (filters.country) {
      const countryValue = filters.country.value.toString();
      this.logger.debug(`处理国家查询条件: ${countryValue}`);
      
      // 获取所有可能的国家名称表示（中英文）
      const countryVariants = this.getAllCountryNameVariants(countryValue);
      this.logger.debug(`国家名称变体: ${JSON.stringify(countryVariants)}`);
      
      // 构建should查询，匹配任一变体即可
      const shouldQueries = countryVariants.map(variant => ({
        match: {
          country: {
            query: variant,
            operator: 'OR'
          }
        }
      }));
      
      must.push({
        bool: {
          should: shouldQueries,
          minimum_should_match: 1
        }
      });
    }
    
    if (filters.object_class) {
      must.push({
        match: {
          object_class: {
            query: filters.object_class.value.toString(),
            operator: 'OR'
          }
        }
      });
    }
    
    // 其他字段使用原始的添加字段匹配方法
    this.addFieldMatchCondition(must, 'launch_date', filters.launch_date);
    this.addFieldMatchCondition(must, 'launch_site', filters.launch_site);
    this.addFieldMatchCondition(must, 'rcs_size', filters.rcs_size);
    this.addFieldMatchCondition(must, 'decay', filters.decay);
    this.addFieldMatchCondition(must, 'first_epoch', filters.first_epoch);
    
    return must.length ? must : [{ match_all: {} }];
  }

  /**
   * 构建碎片事件查询条件
   * @param filters 过滤条件
   * @returns 查询条件数组
   * @private
   */
  private buildDebrisEventQueryConditions(filters: Partial<DebrisQueryDto> & {
    event_id?: string;
    event_type?: string;
    event_time_start?: string;
    event_time_end?: string;
  }): any[] {
    const must = [];
    
    // 添加关键词搜索
    if (filters.keyword) {
      must.push({
        multi_match: {
          query: filters.keyword,
          fields: ['event_type', 'description'],
          type: 'best_fields',
          fuzziness: 'AUTO',
        },
      });
    }
    
    // 添加事件特定字段匹配
    if (filters.event_id) {
      must.push({
        term: {
          event_id: filters.event_id,
        },
      });
    }
    
    if (filters.event_type) {
      must.push({
        match: {
          event_type: filters.event_type,
        },
      });
    }
    
    // 添加时间范围查询
    const timeRangeQuery: any = {};
    
    if (filters.event_time_start) {
      timeRangeQuery.gte = this.normalizeDate(filters.event_time_start);
    }
    
    if (filters.event_time_end) {
      timeRangeQuery.lte = this.normalizeDate(filters.event_time_end);
    }
    
    if (Object.keys(timeRangeQuery).length > 0) {
      must.push({
        range: {
          event_time: timeRangeQuery,
        },
      });
    }
    
    return must.length ? must : [{ match_all: {} }];
  }

  /**
   * 添加字段匹配条件
   * @param must 查询条件数组
   * @param fieldName 字段名
   * @param matchConfig 匹配配置
   * @private
   */
  private addFieldMatchCondition(must: any[], fieldName: string, matchConfig?: FieldMatchConfig): void {
    if (!matchConfig) return;
    
    const { matchType, value } = matchConfig;
    
    if (matchType === 'exact') {
      must.push({
        term: {
          [fieldName]: value,
        },
      });
    } else {
      must.push({
        match: {
          [fieldName]: {
            query: value,
            fuzziness: 'AUTO',
          },
        },
      });
    }
  }

  /**
   * 标准化日期格式
   * @param date 日期字符串
   * @returns 标准化后的日期字符串
   * @private
   */
  private normalizeDate(date: string): string | null {
    if (!date) return null;
    
    try {
      const dateObj = new Date(date);
      return dateObj.toISOString();
    } catch (error) {
      this.logger.warn(`日期格式化失败: ${date}`);
      return date;
    }
  }

  /**
   * 检查数值范围字段是否匹配
   * @param source 文档源数据
   * @param ranges 范围查询条件
   * @returns 匹配结果和匹配字段
   * @private
   */
  private checkRangeFieldsMatch(source: any, ranges: {
    period_minutes_range?: { min?: number; max?: number };
    incl_degrees_range?: { min?: number; max?: number };
    apogee_km_range?: { min?: number; max?: number };
    perigee_km_range?: { min?: number; max?: number };
  }): { allMatched: boolean; matches: Record<string, boolean> } {
    const matches: Record<string, boolean> = {};
    let allMatched = true;
    
    // 获取轨道信息对象，可能直接在根级别或嵌套在orbit_info中
    const orbitInfo = source.orbit_info || source;
    
    // 检查轨道周期
    if (ranges.period_minutes_range) {
      const { min, max } = ranges.period_minutes_range;
      // 尝试从orbit_info中获取，如果不存在则尝试从根级别获取
      const value = parseFloat(orbitInfo.period_minutes);
      
      if (!isNaN(value)) {
        const minMatch = min === undefined || value >= min;
        const maxMatch = max === undefined || value <= max;
        matches.period_minutes_range = minMatch && maxMatch;
        
        if (!matches.period_minutes_range) {
          allMatched = false;
        }
      } else {
        // 如果字段不存在或非数字，则不匹配
        matches.period_minutes_range = false;
        allMatched = false;
      }
    }
    
    // 检查轨道倾角
    if (ranges.incl_degrees_range) {
      const { min, max } = ranges.incl_degrees_range;
      // 尝试从orbit_info中获取，如果不存在则尝试从根级别获取
      const value = parseFloat(orbitInfo.incl_degrees);
      
      if (!isNaN(value)) {
        const minMatch = min === undefined || value >= min;
        const maxMatch = max === undefined || value <= max;
        matches.incl_degrees_range = minMatch && maxMatch;
        
        if (!matches.incl_degrees_range) {
          allMatched = false;
        }
      } else {
        matches.incl_degrees_range = false;
        allMatched = false;
      }
    }
    
    // 检查远地点高度
    if (ranges.apogee_km_range) {
      const { min, max } = ranges.apogee_km_range;
      // 尝试从orbit_info中获取，如果不存在则尝试从根级别获取
      const value = parseFloat(orbitInfo.apogee_km);
      
      if (!isNaN(value)) {
        const minMatch = min === undefined || value >= min;
        const maxMatch = max === undefined || value <= max;
        matches.apogee_km_range = minMatch && maxMatch;
        
        if (!matches.apogee_km_range) {
          allMatched = false;
        }
      } else {
        matches.apogee_km_range = false;
        allMatched = false;
      }
    }
    
    // 检查近地点高度
    if (ranges.perigee_km_range) {
      const { min, max } = ranges.perigee_km_range;
      // 尝试从orbit_info中获取，如果不存在则尝试从根级别获取
      const value = parseFloat(orbitInfo.perigee_km);
      
      if (!isNaN(value)) {
        const minMatch = min === undefined || value >= min;
        const maxMatch = max === undefined || value <= max;
        matches.perigee_km_range = minMatch && maxMatch;
        
        if (!matches.perigee_km_range) {
          allMatched = false;
        }
      } else {
        matches.perigee_km_range = false;
        allMatched = false;
      }
    }
    
    return { allMatched, matches };
  }

  /**
   * 计算匹配字段
   * @param hit 命中结果
   * @param filters 过滤条件
   * @param rangeMatches 范围匹配结果
   * @returns 匹配字段对象
   * @private
   */
  private calculateMatchedFields(hit: any, filters: Partial<DebrisQueryDto>, rangeMatches: Record<string, boolean>): Record<string, boolean> {
    const source = hit;
    const matchedFields: Record<string, boolean> = {};
    
    // 添加范围匹配结果
    Object.entries(rangeMatches).forEach(([key, value]) => {
      matchedFields[key] = value;
    });
    
    // 跳过处理field_values字段
    if (filters.keyword) {
      matchedFields.keyword = true; // 如果命中了，说明关键词匹配
    }
    
    // 精确匹配字段: cospar_id, norad_id
    if (filters.norad_id) {
      matchedFields.norad_id = source.norad_id && source.norad_id.toString() === filters.norad_id.value.toString();
    }
    
    if (filters.cospar_id) {
      matchedFields.cospar_id = source.cospar_id === filters.cospar_id.value.toString();
    }
    
    // 相似度匹配字段: name, mission
    if (filters.name) {
      const nameValue = filters.name.toLowerCase();
      const sourceName = source.name ? source.name.toLowerCase() : '';
      
      // 使用Levenshtein距离来计算相似度
      const similarity = this.calculateStringSimilarity(sourceName, nameValue);
      matchedFields.name = similarity > 0.6; // 如果相似度大于0.6，则认为匹配
      
      // 如果有field_values中的name字段，也检查这些值
      if (!matchedFields.name && source.field_values && source.field_values.name) {
        // 检查所有name值是否有匹配的
        matchedFields.name = source.field_values.name.some((item: {value: any, source: string}) => {
          const itemName = item.value ? item.value.toString().toLowerCase() : '';
          const itemSimilarity = this.calculateStringSimilarity(itemName, nameValue);
          return itemSimilarity > 0.6;
        });
      }
    }
    
    if (filters.mission) {
      const missionValue = filters.mission.toLowerCase();
      const sourceMission = source.mission ? source.mission.toLowerCase() : '';
      
      // 使用Levenshtein距离来计算相似度
      const similarity = this.calculateStringSimilarity(sourceMission, missionValue);
      matchedFields.mission = similarity > 0.6; // 如果相似度大于0.6，则认为匹配
      
      // 如果有field_values中的mission字段，也检查这些值
      if (!matchedFields.mission && source.field_values && source.field_values.mission) {
        // 检查所有mission值是否有匹配的
        matchedFields.mission = source.field_values.mission.some((item: {value: any, source: string}) => {
          const itemMission = item.value ? item.value.toString().toLowerCase() : '';
          const itemSimilarity = this.calculateStringSimilarity(itemMission, missionValue);
          return itemSimilarity > 0.6;
        });
      }
    }
    
    // 含义匹配字段: country, object_class
    if (filters.country) {
      const countryValue = filters.country.value.toString().toLowerCase();
      const sourceCountry = source.country ? source.country.toLowerCase() : '';
      
      // 仅考虑单词是否存在，不考虑顺序和精确匹配
      const countryWords = countryValue.split(/\s+/);
      const sourceCountryWords = sourceCountry.split(/\s+/);
      
      // 如果有任何一个单词匹配，则认为匹配
      matchedFields.country = countryWords.some(word => 
        sourceCountryWords.some((sourceWord: string) => sourceWord.includes(word) || word.includes(sourceWord))
      );
      
      // 如果有field_values中的country字段，也检查这些值
      if (!matchedFields.country && source.field_values && source.field_values.country) {
        // 检查所有country值是否有匹配的
        matchedFields.country = source.field_values.country.some((item: {value: any, source: string}) => {
          const itemCountry = item.value ? item.value.toString().toLowerCase() : '';
          const itemCountryWords = itemCountry.split(/\s+/);
          return countryWords.some(word => 
            itemCountryWords.some((itemWord: string) => itemWord.includes(word) || word.includes(itemWord))
          );
        });
      }
    }
    
    if (filters.object_class) {
      const objectClassValue = filters.object_class.value.toString().toLowerCase();
      const sourceObjectClass = source.object_class ? source.object_class.toLowerCase() : '';
      
      // 仅考虑单词是否存在，不考虑顺序和精确匹配
      const objectClassWords = objectClassValue.split(/\s+/);
      const sourceObjectClassWords = sourceObjectClass.split(/\s+/);
      
      // 如果有任何一个单词匹配，则认为匹配
      matchedFields.object_class = objectClassWords.some(word => 
        sourceObjectClassWords.some((sourceWord: string) => sourceWord.includes(word) || word.includes(sourceWord))
      );
      
      // 如果有field_values中的object_class字段，也检查这些值
      if (!matchedFields.object_class && source.field_values && source.field_values.object_class) {
        // 检查所有object_class值是否有匹配的
        matchedFields.object_class = source.field_values.object_class.some((item: {value: any, source: string}) => {
          const itemObjectClass = item.value ? item.value.toString().toLowerCase() : '';
          const itemObjectClassWords = itemObjectClass.split(/\s+/);
          return objectClassWords.some(word => 
            itemObjectClassWords.some((itemWord: string) => itemWord.includes(word) || word.includes(itemWord))
          );
        });
      }
    }
    
    return matchedFields;
  }

  /**
   * 计算两个字符串的相似度
   * 使用Levenshtein距离算法
   * @param a 第一个字符串
   * @param b 第二个字符串
   * @returns 相似度(0-1)，1表示完全相同
   * @private
   */
  private calculateStringSimilarity(a: string, b: string): number {
    if (!a || !b) return 0;
    if (a === b) return 1;
    
    // 如果一个字符串包含另一个，给予较高的相似度
    if (a.includes(b) || b.includes(a)) {
      return 0.8;
    }
    
    // 计算Levenshtein距离
    const matrix: number[][] = [];
    
    // 初始化矩阵
    for (let i = 0; i <= a.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= b.length; j++) {
      matrix[0][j] = j;
    }
    
    // 填充矩阵
    for (let i = 1; i <= a.length; i++) {
      for (let j = 1; j <= b.length; j++) {
        const cost = a[i - 1] === b[j - 1] ? 0 : 1;
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1, // 删除
          matrix[i][j - 1] + 1, // 插入
          matrix[i - 1][j - 1] + cost // 替换
        );
      }
    }
    
    // 计算最大可能的编辑距离
    const maxDistance = Math.max(a.length, b.length);
    
    // 将距离转换为相似度(0-1)
    return 1 - matrix[a.length][b.length] / maxDistance;
  }

  /**
   * 创建匹配字段描述
   * @param matchedFields 匹配字段对象
   * @returns 匹配字段描述数组
   * @private
   */
  private createMatchFieldsDescription(matchedFields: Record<string, boolean>): Array<{ field: string; matchLevel: string; score: number }> {
    const descriptions: Array<{ field: string; matchLevel: string; score: number }> = [];
    
    Object.entries(matchedFields).forEach(([field, matched]) => {
      if (matched) {
        // 确定匹配级别和分数
        let matchLevel: string;
        let score: number;
        
        // 精确匹配字段
        if (field === 'norad_id' || field === 'cospar_id') {
          matchLevel = '精确匹配';
          score = 1.0;
        }
        // 范围匹配字段
        else if (field.endsWith('_range')) {
          matchLevel = '范围匹配';
          score = 0.8;
        }
        // 相似度匹配字段
        else if (field === 'name' || field === 'mission') {
          matchLevel = '相似度匹配';
          score = 0.7;
        }
        // 含义匹配字段
        else if (field === 'country' || field === 'object_class') {
          matchLevel = '含义匹配';
          score = 0.6;
        }
        // 其他字段
        else {
          matchLevel = '部分匹配';
          score = 0.5;
        }
        
        descriptions.push({
          field,
          matchLevel,
          score
        });
      }
    });
    
    return descriptions;
  }

  /**
   * 搜索碎片事件
   * @param query 碎片事件查询参数
   * @returns 碎片事件搜索结果
   */
  async searchDebrisEvents(query: DebrisQueryDto & {
    event_id?: string;
    event_type?: string;
    event_time_start?: string;
    event_time_end?: string;
  }): Promise<DebrisEventSearchResponse> {
    try {
      const { page = 1, limit = 10, sort, ...filters } = query;
      const sortField = sort?.field || 'event_time';
      const sortOrder = sort?.order || 'desc';
      
      // 构建查询条件
      const must = this.buildDebrisEventQueryConditions(filters);
      
      // 执行查询
      const searchResult = await this.esBaseService.search({
        index: this.debrisEventIndex,
        body: {
          query: {
            bool: {
              must,
            },
          },
          sort: [
            {
              [sortField]: {
                order: sortOrder,
              },
            },
          ],
          from: (page - 1) * limit,
          size: limit,
        },
      });
      
      // 处理结果
      const total = typeof searchResult.hits.total === 'number' 
        ? searchResult.hits.total 
        : (searchResult.hits.total as SearchTotalHits).value;
      
      // 转换结果并仅保留必要的字段
      const items = searchResult.hits.hits.map((hit: any) => {
        const source = hit._source;
        return {
          _id: hit._id,
          event_id: source.event_id,
          event_type: source.event_type,
          event_time: source.event_time,
          description: source.description,
          debris_ids: source.debris_ids,
          satellite_ids: source.satellite_ids,
          location: source.location
        };
      });
      
      return {
        total,
        page,
        size: limit,
        items,
      };
    } catch (error) {
      this.logger.error(`搜索碎片事件失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取所有碎片名称集合
   * @returns 唯一碎片名称数组
   */
  async getDebrisNames(): Promise<string[]> {
    try {
      this.logger.debug('获取所有碎片名称集合');
      
      // 从debris_discos索引获取名称
      const discosResult = await this.esBaseService.search({
        index: this.debrisDiscosIndex,
        body: {
          size: 0, // 不需要文档，只需要聚合结果
          aggs: {
            unique_names: {
              terms: {
                field: 'name.keyword',
                size: 10000 // 获取尽可能多的唯一名称
              }
            }
          }
        }
      });
      
      // 从debris_spacetrack索引获取名称
      const spacetrackResult = await this.esBaseService.search({
        index: this.debrisSpacetrackIndex,
        body: {
          size: 0,
          aggs: {
            unique_names: {
              terms: {
                field: 'name.keyword',
                size: 10000
              }
            }
          }
        }
      });
      
      // 提取名称并合并
      const discosNames = discosResult.aggregations && 'unique_names' in discosResult.aggregations
        ? (discosResult.aggregations.unique_names as any).buckets.map((bucket: any) => bucket.key as string) || []
        : [];
      
      const spacetrackNames = spacetrackResult.aggregations && 'unique_names' in spacetrackResult.aggregations
        ? (spacetrackResult.aggregations.unique_names as any).buckets.map((bucket: any) => bucket.key as string) || []
        : [];
      
      // 合并并去重
      const allNames = [...new Set([...discosNames, ...spacetrackNames])];
      
      // 过滤掉null和空字符串
      const filteredNames = allNames.filter(name => name && name.trim() !== '');
      
      // 按字母顺序排序
      const sortedNames = filteredNames.sort((a, b) => a.localeCompare(b));
      
      this.logger.debug(`获取到${sortedNames.length}个唯一碎片名称`);
      
      return sortedNames;
    } catch (error) {
      this.logger.error(`获取碎片名称集合失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取所有碎片类别集合
   * @returns 唯一碎片类别数组
   */
  async getDebrisObjectClasses(): Promise<string[]> {
    try {
      this.logger.debug('获取所有碎片类别集合');
      
      // 从debris_discos索引获取类别
      const discosResult = await this.esBaseService.search({
        index: this.debrisDiscosIndex,
        body: {
          size: 0, // 不需要文档，只需要聚合结果
          aggs: {
            unique_classes: {
              terms: {
                field: 'object_class.keyword',
                size: 1000 // 获取尽可能多的唯一类别
              }
            }
          }
        }
      });
      
      // 从debris_spacetrack索引获取类别
      const spacetrackResult = await this.esBaseService.search({
        index: this.debrisSpacetrackIndex,
        body: {
          size: 0,
          aggs: {
            unique_classes: {
              terms: {
                field: 'object_class.keyword',
                size: 1000
              }
            }
          }
        }
      });
      
      // 提取类别并合并
      const discosClasses = discosResult.aggregations && 'unique_classes' in discosResult.aggregations
        ? (discosResult.aggregations.unique_classes as any).buckets.map((bucket: any) => bucket.key as string) || []
        : [];
      
      const spacetrackClasses = spacetrackResult.aggregations && 'unique_classes' in spacetrackResult.aggregations
        ? (spacetrackResult.aggregations.unique_classes as any).buckets.map((bucket: any) => bucket.key as string) || []
        : [];
      
      // 合并并去重
      const allClasses = [...new Set([...discosClasses, ...spacetrackClasses])];
      
      // 过滤掉null和空字符串
      const filteredClasses = allClasses.filter(objectClass => objectClass && objectClass.trim() !== '');
      
      // 按字母顺序排序
      const sortedClasses = filteredClasses.sort((a, b) => a.localeCompare(b));
      
      this.logger.debug(`获取到${sortedClasses.length}个唯一碎片类别`);
      
      return sortedClasses;
    } catch (error) {
      this.logger.error(`获取碎片类别集合失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取所有碎片任务名称集合
   * @returns 唯一任务名称数组
   */
  async getDebrisMissions(): Promise<string[]> {
    try {
      this.logger.debug('获取所有碎片任务名称集合');
      
      // 仅从debris_discos索引获取任务名称
      const result = await this.esBaseService.search({
        index: this.debrisDiscosIndex,
        body: {
          size: 0, // 不需要文档，只需要聚合结果
          aggs: {
            unique_missions: {
              terms: {
                field: 'mission.keyword',
                size: 10000, // 获取尽可能多的唯一任务名称
                // 排除空或null值
                exclude: ["", "null"]
              }
            }
          }
        }
      });
      
      // 提取任务名称
      const missions = result.aggregations && 'unique_missions' in result.aggregations
        ? (result.aggregations.unique_missions as any).buckets.map((bucket: any) => bucket.key as string) || []
        : [];
      
      // 过滤掉null和空字符串
      const filteredMissions = missions.filter((mission: string) => mission && mission.trim() !== '');
      
      // 按字母顺序排序
      const sortedMissions = filteredMissions.sort((a: string, b: string) => a.localeCompare(b));
      
      this.logger.debug(`获取到${sortedMissions.length}个唯一碎片任务名称`);
      
      return sortedMissions;
    } catch (error) {
      this.logger.error(`获取碎片任务名称集合失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取所有碎片所属国家集合
   * @returns 唯一国家名称数组，格式为"中文名称(英文名称)"
   */
  async getDebrisCountries(): Promise<string[]> {
    try {
      this.logger.debug('获取所有碎片所属国家集合');
      
      // 从debris_spacetrack索引获取国家
      const spacetrackResult = await this.esBaseService.search({
        index: this.debrisSpacetrackIndex,
        body: {
          size: 0,
          aggs: {
            unique_countries: {
              terms: {
                field: 'country.keyword',
                size: 1000 // 获取尽可能多的唯一国家
              }
            }
          }
        }
      });
      
      // 提取国家
      const countries = spacetrackResult.aggregations && 'unique_countries' in spacetrackResult.aggregations
        ? (spacetrackResult.aggregations.unique_countries as any).buckets.map((bucket: any) => bucket.key as string) || []
        : [];
      
      // 过滤掉null和空字符串
      const filteredCountries = countries.filter((country: string) => country && country.trim() !== '');
      
      // 转换为"中文名称(英文名称)"格式
      const formattedCountries = filteredCountries.map((englishName: string) => {
        const chineseName = this.countryNameMap[englishName] || englishName; // 如果没有映射，使用原始英文名
        return `${chineseName}(${englishName})`;
      });
      
      // 按中文名称排序
      const sortedCountries = formattedCountries.sort((a: string, b: string) => {
        const chineseNameA = a.split('(')[0];
        const chineseNameB = b.split('(')[0];
        return chineseNameA.localeCompare(chineseNameB, 'zh-CN');
      });
      
      this.logger.debug(`获取到${sortedCountries.length}个唯一碎片所属国家`);
      
      return sortedCountries;
    } catch (error) {
      this.logger.error(`获取碎片所属国家集合失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 根据轨道参数范围从debris_spacetrack索引中搜索碎片信息
   * @param ranges 轨道参数范围
   * @param page 页码
   * @param limit 每页数量
   * @param sortField 排序字段
   * @param sortOrder 排序顺序
   * @returns 查询结果
   * @private
   */
  private async searchDebrisByOrbitParams(
    ranges: {
      period_minutes_range?: { min?: number; max?: number };
      incl_degrees_range?: { min?: number; max?: number };
      apogee_km_range?: { min?: number; max?: number };
      perigee_km_range?: { min?: number; max?: number };
    },
    page: number,
    limit: number,
    sortField?: string,
    sortOrder: string = 'desc'
  ): Promise<DebrisItemWithMatch[]> {
    this.logger.debug(`根据轨道参数范围搜索碎片信息: ${JSON.stringify(ranges)}`);
    
    // 构建查询条件
    const must: any[] = [];
    
    // 添加轨道周期范围条件
    if (ranges.period_minutes_range) {
      const rangeQuery: any = {};
      if (ranges.period_minutes_range.min !== undefined) {
        rangeQuery.gte = ranges.period_minutes_range.min;
      }
      if (ranges.period_minutes_range.max !== undefined) {
        rangeQuery.lte = ranges.period_minutes_range.max;
      }
      if (Object.keys(rangeQuery).length > 0) {
        must.push({
          range: {
            "orbit_info.period_minutes": rangeQuery
          }
        });
      }
    }
    
    // 添加轨道倾角范围条件
    if (ranges.incl_degrees_range) {
      const rangeQuery: any = {};
      if (ranges.incl_degrees_range.min !== undefined) {
        rangeQuery.gte = ranges.incl_degrees_range.min;
      }
      if (ranges.incl_degrees_range.max !== undefined) {
        rangeQuery.lte = ranges.incl_degrees_range.max;
      }
      if (Object.keys(rangeQuery).length > 0) {
        must.push({
          range: {
            "orbit_info.incl_degrees": rangeQuery
          }
        });
      }
    }
    
    // 添加远地点高度范围条件
    if (ranges.apogee_km_range) {
      const rangeQuery: any = {};
      if (ranges.apogee_km_range.min !== undefined) {
        rangeQuery.gte = ranges.apogee_km_range.min;
      }
      if (ranges.apogee_km_range.max !== undefined) {
        rangeQuery.lte = ranges.apogee_km_range.max;
      }
      if (Object.keys(rangeQuery).length > 0) {
        must.push({
          range: {
            "orbit_info.apogee_km": rangeQuery
          }
        });
      }
    }
    
    // 添加近地点高度范围条件
    if (ranges.perigee_km_range) {
      const rangeQuery: any = {};
      if (ranges.perigee_km_range.min !== undefined) {
        rangeQuery.gte = ranges.perigee_km_range.min;
      }
      if (ranges.perigee_km_range.max !== undefined) {
        rangeQuery.lte = ranges.perigee_km_range.max;
      }
      if (Object.keys(rangeQuery).length > 0) {
        must.push({
          range: {
            "orbit_info.perigee_km": rangeQuery
          }
        });
      }
    }
    
    // 如果没有任何范围条件，返回空结果
    if (must.length === 0) {
      this.logger.debug('没有提供任何轨道参数范围条件');
      return [];
    }
    
    // 构建查询体
    const searchBody: any = {
      query: {
        bool: {
          must
        }
      },
      from: 0,
      size: 1000, // 获取足够多的结果用于后续处理
      // 按_id降序排序，确保获取最新的文档
      sort: [
        {
          _id: {
            order: 'desc'
          }
        }
      ]
    };
    
    // 如果有明确的排序字段，则添加到排序条件中
    if (sortField) {
      searchBody.sort.unshift({
        [sortField]: {
          order: sortOrder
        }
      });
    }
    
    this.logger.debug(`轨道参数查询体: ${JSON.stringify(searchBody)}`);
    
    // 执行查询
    const searchResult = await this.esBaseService.search({
      index: this.debrisSpacetrackIndex,
      body: searchBody
    });
    
    this.logger.debug(`轨道参数查询结果数量: ${searchResult.hits.hits.length}`);
    
    // 按cospar_id分组，只保留每组中_id最大的文档
    const spacetrackByCosparId = new Map<string, any>();
    
    for (const hit of searchResult.hits.hits) {
      if (!hit._id || !hit._source) {
        this.logger.warn(`跳过无效的文档: ${JSON.stringify(hit)}`);
        continue;
      }
      
      const source = hit._source;
      const cosparId = source.cospar_id;
      
      if (!cosparId) {
        this.logger.debug(`跳过没有cospar_id的文档: _id=${hit._id}`);
        continue;
      }
      
      const existing = spacetrackByCosparId.get(cosparId);
      if (!existing || hit._id > existing._id) {
        spacetrackByCosparId.set(cosparId, hit);
        this.logger.debug(`为cospar_id=${cosparId}设置文档: _id=${hit._id}`);
      } else {
        this.logger.debug(`忽略cospar_id=${cosparId}的旧文档: _id=${hit._id}, 已有_id=${existing._id}`);
      }
    }
    
    this.logger.debug(`按cospar_id分组后的文档数量: ${spacetrackByCosparId.size}`);
    
    // 提取所有匹配的cospar_id
    const cosparIds = Array.from(spacetrackByCosparId.keys());
    
    // 如果没有找到任何文档，返回空结果
    if (cosparIds.length === 0) {
      this.logger.debug('没有找到任何匹配的文档');
      return [];
    }
    
    // 查询debris_discos索引中匹配的文档
    const discosResults = await this.searchDiscosByCosparIds(cosparIds);
    
    this.logger.debug(`在debris_discos中找到匹配的文档数量: ${discosResults.length}`);
    
    // 处理spacetrack结果
    const spacetrackResults = Array.from(spacetrackByCosparId.values()).map(hit => {
      const source = hit._source;
      const result: DebrisItemWithMatch = {
        ...source,
        _id: hit._id,
        _index: this.debrisSpacetrackIndex,
        match_score: hit._score,
        source_index: this.debrisSpacetrackIndex,
        source_fields: {},
        field_values: {}
      };
      
      // 为所有字段添加来源记录
      for (const key of Object.keys(source)) {
        if (key === '_id' || key === '_index' || key === 'match_score' || 
            key === 'source_fields' || key === 'source_index' || key === 'field_values') continue;
        
        const value = source[key];
        if (value !== undefined && result.source_fields && result.field_values) {
          result.source_fields[key] = this.debrisSpacetrackIndex;
          result.field_values[key] = [
            { value, source: this.debrisSpacetrackIndex }
          ];
        }
      }
      
      return result;
    });
    
    // 如果在debris_discos中找到了匹配的文档，则进行聚合
    if (discosResults.length > 0) {
      this.logger.debug('将debris_discos和debris_spacetrack的结果进行聚合');
      return this.aggregateResults(discosResults, spacetrackResults);
    }
    
    // 否则直接返回spacetrack结果
    return spacetrackResults;
  }
  
  /**
   * 根据cospar_id查询debris_discos索引
   * @param cosparIds cospar_id数组
   * @returns 查询结果
   * @private
   */
  private async searchDiscosByCosparIds(cosparIds: string[]): Promise<DebrisItemWithMatch[]> {
    this.logger.debug(`根据cospar_id搜索debris_discos索引，cospar_ids: ${JSON.stringify(cosparIds)}`);
    
    // 使用bool查询构建多个should条件
    const should = cosparIds.map(cosparId => ({
      term: {
        'cospar_id.keyword': {
          value: cosparId
        }
      }
    }));
    
    const searchBody: any = {
      query: {
        bool: {
          should: should,
          minimum_should_match: 1
        }
      },
      size: 1000 // 获取足够多的结果用于后续聚合
    };
    
    try {
      const searchResult = await this.esBaseService.search({
        index: this.debrisDiscosIndex,
        body: searchBody
      });
      
      const results: DebrisItemWithMatch[] = [];
      
      for (const hit of searchResult.hits.hits) {
        if (!hit._id || !hit._source) {
          this.logger.warn(`跳过无效的文档: ${JSON.stringify(hit)}`);
          continue;
        }
        
        results.push({
          ...hit._source,
          _id: hit._id,
          _index: this.debrisDiscosIndex,
          match_score: hit._score || 0
        });
      }
      
      return results;
    } catch (error) {
      this.logger.error(`搜索debris_discos索引失败: ${error.message}`, error.stack);
      return [];
    }
  }
} 