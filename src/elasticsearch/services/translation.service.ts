import { Injectable, Logger } from '@nestjs/common';
import OpenAI from 'openai';
import { 
  TranslationConfig, 
  ThemeExtractionConfig, 
  getTranslationConfig, 
  getThemeExtractionConfig,
  mergeConfig,
  validateLLMConfig
} from '../../../config/llm.config';

/**
 * 翻译服务类
 * 负责与Qwen大模型API交互，进行文本翻译和主题提取
 * 支持可配置的模型选择，默认使用qwen-turbo模型
 */
@Injectable()
export class TranslationService {
  private readonly logger = new Logger(TranslationService.name);
  private openai: OpenAI;
  private translationCache = new Map<string, string>();
  private apiCallCount = 0;
  private apiTotalTime = 0;

  // 配置属性
  private translationConfig: TranslationConfig;
  private themeExtractionConfig: ThemeExtractionConfig;

  // 并发控制
  private activeRequests = 0;
  private readonly requestQueue: Array<() => Promise<void>> = [];

  // 文本分段配置
  private readonly paragraphSeparators = ["\n\n", "\n", ". ", "! ", "? "]; // 段落分隔符

  // 重试配置
  private consecutiveFailures = 0; // 当前连续失败次数（仅用于日志记录）
  
  // 失败统计
  private failureStats = {
    contentFilter: 0,
    timeout: 0,
    rateLimit: 0,
    networkError: 0,
    other: 0,
    totalAttempts: 0,
    successfulProcessing: 0
  };

  // 敏感词及替换映射，用于在发送给模型前降低被拒绝概率
  private readonly sensitiveReplacements: Array<{ pattern: RegExp; replacement: string }> = [
    // 英文敏感词替换为较温和表达
    { pattern: /\bterrorist(s)?\b/gi, replacement: 'extremist $1' },
    { pattern: /\bterrorism\b/gi, replacement: 'extremism' },
    { pattern: /\bexplosive(s)?\b/gi, replacement: 'explosive material' },
    { pattern: /\bbomb(s)?\b/gi, replacement: 'explosive device$1' },
    { pattern: /\bnuclear weapon(s)?\b/gi, replacement: 'special weapon$1' },
    { pattern: /\bmissile(s)?\b/gi, replacement: 'projectile$1' },
    // 中文敏感词
    { pattern: /炸弹/g, replacement: '爆炸装置' },
    { pattern: /核武器/g, replacement: '特殊武器' },
    { pattern: /恐怖分子/g, replacement: '极端分子' },
    { pattern: /恐怖主义/g, replacement: '极端主义' }
  ];

  constructor() {
    // 初始化翻译配置
    this.translationConfig = this.loadTranslationConfig();
    this.themeExtractionConfig = this.loadThemeExtractionConfig();

    // 验证配置
    try {
      validateLLMConfig(this.translationConfig);
      validateLLMConfig(this.themeExtractionConfig);
    } catch (error) {
      this.logger.error(`配置验证失败: ${error.message}`);
      throw error;
    }

    // 初始化OpenAI客户端
    this.openai = new OpenAI({
      apiKey: this.translationConfig.apiKey,
      baseURL: this.translationConfig.baseURL
    });

    this.logger.log(`翻译服务初始化完成 - 翻译模型: ${this.translationConfig.model}, 主题提取模型: ${this.themeExtractionConfig.model}`);
  }

  /**
   * 加载翻译配置
   * @returns 翻译配置
   */
  private loadTranslationConfig(): TranslationConfig {
    const mode = (process.env.TRANSLATION_MODE as 'default' | 'high_quality' | 'fast') || 'default';
    let config = getTranslationConfig(mode);

    // 从环境变量覆盖配置
    const envOverrides: Partial<TranslationConfig> = {};
    
    if (process.env.TRANSLATION_MODEL) {
      envOverrides.model = process.env.TRANSLATION_MODEL;
    }
    
    if (process.env.QWEN_API_KEY) {
      envOverrides.apiKey = process.env.QWEN_API_KEY;
    }
    
    if (process.env.QWEN_BASE_URL) {
      envOverrides.baseURL = process.env.QWEN_BASE_URL;
    }
    
    if (process.env.TRANSLATION_MAX_CONCURRENT) {
      envOverrides.maxConcurrentRequests = parseInt(process.env.TRANSLATION_MAX_CONCURRENT);
    }
    
    if (process.env.TRANSLATION_TIMEOUT) {
      envOverrides.timeout = parseInt(process.env.TRANSLATION_TIMEOUT);
    }
    
    if (process.env.TRANSLATION_MAX_RETRIES) {
      envOverrides.maxRetries = parseInt(process.env.TRANSLATION_MAX_RETRIES);
    }

    config = mergeConfig(config, envOverrides);
    
    this.logger.log(`加载翻译配置 - 模式: ${mode}, 模型: ${config.model}`);
    return config;
  }

  /**
   * 加载主题提取配置
   * @returns 主题提取配置
   */
  private loadThemeExtractionConfig(): ThemeExtractionConfig {
    const mode = (process.env.THEME_EXTRACTION_MODE as 'default' | 'high_quality' | 'fast') || 'default';
    let config = getThemeExtractionConfig(mode);

    // 从环境变量覆盖配置
    const envOverrides: Partial<ThemeExtractionConfig> = {};
    
    if (process.env.THEME_EXTRACTION_MODEL) {
      envOverrides.model = process.env.THEME_EXTRACTION_MODEL;
    }
    
    if (process.env.QWEN_API_KEY) {
      envOverrides.apiKey = process.env.QWEN_API_KEY;
    }
    
    if (process.env.QWEN_BASE_URL) {
      envOverrides.baseURL = process.env.QWEN_BASE_URL;
    }
    
    if (process.env.THEME_EXTRACTION_MAX_CONCURRENT) {
      envOverrides.maxConcurrentRequests = parseInt(process.env.THEME_EXTRACTION_MAX_CONCURRENT);
    }
    
    if (process.env.THEME_EXTRACTION_TIMEOUT) {
      envOverrides.timeout = parseInt(process.env.THEME_EXTRACTION_TIMEOUT);
    }
    
    if (process.env.THEME_EXTRACTION_MAX_RETRIES) {
      envOverrides.maxRetries = parseInt(process.env.THEME_EXTRACTION_MAX_RETRIES);
    }

    config = mergeConfig(config, envOverrides);
    
    this.logger.log(`加载主题提取配置 - 模式: ${mode}, 模型: ${config.model}`);
    return config;
  }

  /**
   * 获取当前翻译配置
   * @returns 翻译配置
   */
  getTranslationConfig(): TranslationConfig {
    return { ...this.translationConfig };
  }

  /**
   * 获取当前主题提取配置
   * @returns 主题提取配置
   */
  getThemeExtractionConfig(): ThemeExtractionConfig {
    return { ...this.themeExtractionConfig };
  }

  /**
   * 动态更新翻译配置
   * @param newConfig 新的配置
   */
  updateTranslationConfig(newConfig: Partial<TranslationConfig>): void {
    this.translationConfig = mergeConfig(this.translationConfig, newConfig);
    validateLLMConfig(this.translationConfig);
    
    // 如果API配置发生变化，重新初始化OpenAI客户端
    if (newConfig.apiKey || newConfig.baseURL) {
      this.openai = new OpenAI({
        apiKey: this.translationConfig.apiKey,
        baseURL: this.translationConfig.baseURL
      });
    }
    
    this.logger.log(`翻译配置已更新 - 模型: ${this.translationConfig.model}`);
  }

  /**
   * 动态更新主题提取配置
   * @param newConfig 新的配置
   */
  updateThemeExtractionConfig(newConfig: Partial<ThemeExtractionConfig>): void {
    this.themeExtractionConfig = mergeConfig(this.themeExtractionConfig, newConfig);
    validateLLMConfig(this.themeExtractionConfig);
    this.logger.log(`主题提取配置已更新 - 模型: ${this.themeExtractionConfig.model}`);
  }

  /**
   * 在发送到大模型前，对文本进行敏感词智能替换，降低拒绝概率
   * @param text 原始文本
   */
  private sanitizeSensitiveWords(text: string): string {
    let sanitized = text;
    for (const { pattern, replacement } of this.sensitiveReplacements) {
      sanitized = sanitized.replace(pattern, replacement);
    }
    return sanitized;
  }

  /**
   * 翻译文本 - 增强版错误处理
   * @param text 待翻译文本
   * @param retryCount 当前重试次数
   * @param isSegment 是否是分段调用，如果是则不再进行分段处理
   * @returns 翻译后的文本
   * @throws 如果熔断器已触发，抛出异常
   */
  async translateText(text: string, retryCount = 0, isSegment = false): Promise<string> {
    if (!text || text.trim() === '') {
      return '';
    }

    // 统计总尝试次数
    this.failureStats.totalAttempts++;

    // ===== 新增：主动敏感词替换 =====
    let inputText = text;
    if (!isSegment) {
      // 仅在首次调用时执行，避免分段递归时重复替换
      inputText = this.sanitizeSensitiveWords(text);
    }

    // 检查缓存
    const cacheKey = this.generateCacheKey(inputText);
    if (this.translationCache.has(cacheKey)) {
      this.apiCallCount++;
      this.logger.debug(`命中翻译缓存 - 长度: ${inputText.length}`);
      return this.translationCache.get(cacheKey) || '';
    }

    // 如果文本太长，使用分段翻译
    if (!isSegment && inputText.length > this.translationConfig.maxTextLength) {
      return this.translateLongText(inputText);
    }

    // 并发控制
    return this.executeWithConcurrencyControl(async () => {
      try {
        this.apiCallCount++;
        const startTime = Date.now();

        // 预处理文本
        const processedText = this.preprocessContent(inputText);

        const completion = await this.openai.chat.completions.create({
          model: this.translationConfig.model,
          messages: [
            { role: 'system', content: this.translationConfig.systemPrompt },
            { role: 'user', content: processedText }
          ],
        });

        const translatedText = completion.choices[0]?.message?.content || '';
        const elapsed = Date.now() - startTime;
        this.apiTotalTime += elapsed;

        this.logger.debug(`翻译API调用完成，耗时: ${elapsed}ms，总调用次数: ${this.apiCallCount}`);

        // 缓存翻译结果
        this.translationCache.set(cacheKey, translatedText);

        // 重置连续失败计数
        this.consecutiveFailures = 0;
        
        // 统计成功处理
        this.failureStats.successfulProcessing++;

        return translatedText;
      } catch (error: any) {
        // 增加连续失败计数（仅用于日志记录）
        this.consecutiveFailures++;
        
        // 记录详细的错误信息
        this.logger.error(`翻译失败 - 模型: ${this.translationConfig.model}, 重试次数: ${retryCount}, 错误: ${error.message}`);

        // 处理内容不适当错误 - 增强版
        if (error.message.includes('inappropriate content') || 
            error.message.includes('content_filter') || 
            error.message.includes('content policy') ||
            error.message.includes('safety') ||
            error.message.includes('harmful')) {
          
          // 统计内容过滤错误
          this.failureStats.contentFilter++;
          
          this.logger.warn(`检测到内容过滤错误，错误信息: ${error.message}, 当前重试次数=${retryCount}, 最大重试次数=${this.translationConfig.maxRetries}`);

          // 如果重试次数大于等于最大重试次数，直接跳过文档
          if (retryCount >= (this.translationConfig.maxRetries || 2)) {
            this.logger.error(`内容过滤错误超过最大重试次数(${this.translationConfig.maxRetries})，当前重试次数=${retryCount}，跳过该文档`);
            this.logger.error(`原始文本前100字符: ${inputText.substring(0, 100)}...`);
            throw new Error(`SKIP_DOCUMENT:内容过滤错误超过最大重试次数 - ${error.message}`);
          }

          // 使用智能预处理重试
          this.logger.warn(`使用智能预处理重试，重试次数: ${retryCount + 1}`);
          
          // 分析原始文本
          const preprocessingResult = this.intelligentPreprocessing(inputText);
          
          this.logger.debug(`智能预处理结果: 策略=${preprocessingResult.strategy}, 敏感词=${preprocessingResult.analysis.original.sensitiveWords.length}个, 风险等级=${preprocessingResult.analysis.original.riskLevel}, 敏感类别=[${preprocessingResult.analysis.original.categories.join(', ')}]`);
          
          // 记录预处理前后的变化
          const originalLength = inputText.length;
          const processedLength = preprocessingResult.processedText.length;
          this.logger.debug(`文本预处理: 原长度=${originalLength}, 处理后长度=${processedLength}, 压缩率=${Math.round((1 - processedLength/originalLength) * 100)}%`);
          
          // 如果需要人工审核，记录详细信息
          if (preprocessingResult.needsManualReview) {
            this.logger.warn(`文档需要人工审核: 敏感类别=${preprocessingResult.analysis.original.categories.join(', ')}, 敏感词数量=${preprocessingResult.analysis.original.sensitiveWords.length}`);
            this.logger.debug(`检测到的敏感词: ${preprocessingResult.analysis.original.sensitiveWords.slice(0, 10).join(', ')}${preprocessingResult.analysis.original.sensitiveWords.length > 10 ? '...' : ''}`);
          }

          return this.translateText(preprocessingResult.processedText, retryCount + 1, isSegment);
        }

        // 处理超时错误
        if (error.message.includes('timed out') || error.message.includes('timeout')) {
          // 统计超时错误
          this.failureStats.timeout++;
          
          if (retryCount < (this.translationConfig.maxRetries || 2)) {
            // 指数退避重试
            const waitTime = Math.pow(2, retryCount) * (this.translationConfig.retryDelay || 1000);
            this.logger.warn(`翻译请求超时，${waitTime}ms后重试，重试次数: ${retryCount + 1} - 模型: ${this.translationConfig.model}`);

            await new Promise(resolve => setTimeout(resolve, waitTime));
            return this.translateText(inputText, retryCount + 1, isSegment);
          } else {
            // 超过最大重试次数，返回特殊标记以跳过该文档
            this.logger.error(`翻译超时错误超过最大重试次数(${this.translationConfig.maxRetries})，跳过该文档`);
            throw new Error(`SKIP_DOCUMENT:翻译超时错误超过最大重试次数`);
          }
        }

        // 处理API限制错误
        if (error.message.includes('rate limit') || error.message.includes('429')) {
          // 统计API限制错误
          this.failureStats.rateLimit++;
          
          if (retryCount < (this.translationConfig.maxRetries || 2)) {
            // 更长的等待时间
            const waitTime = (retryCount + 1) * 5000; // 5秒、10秒、15秒...
            this.logger.warn(`API限制错误，${waitTime}ms后重试，重试次数: ${retryCount + 1} - 模型: ${this.translationConfig.model}`);

            await new Promise(resolve => setTimeout(resolve, waitTime));
            return this.translateText(inputText, retryCount + 1, isSegment);
          } else {
            this.logger.error(`API限制错误超过最大重试次数(${this.translationConfig.maxRetries})，跳过该文档`);
            throw new Error(`SKIP_DOCUMENT:API限制错误超过最大重试次数`);
          }
        }

        // 处理网络错误
        if (error.message.includes('network') || 
            error.message.includes('connection') || 
            error.message.includes('ENOTFOUND') ||
            error.message.includes('ECONNRESET') ||
            error.message.includes('socket')) {
          this.failureStats.networkError++;
          this.logger.error(`网络连接错误 - 模型: ${this.translationConfig.model}: ${error.message}`);
        } else {
          // 其他未分类错误
          this.failureStats.other++;
        }

        // 处理其他错误
        this.logger.error(`翻译失败 - 模型: ${this.translationConfig.model}: ${error.message}`, error.stack);
        throw new Error(`翻译失败: ${error.message}`);
      }
    });
  }

  /**
   * 批量翻译文本
   * @param texts 待翻译文本数组
   * @returns 翻译后的文本数组
   */
  async translateBatch(texts: string[]): Promise<string[]> {
    const results = [];
    for (const text of texts) {
      try {
        const translated = await this.translateText(text);
        results.push(translated);
      } catch (error: any) {
        this.logger.error(`批量翻译出错: ${error.message}`);
        results.push(''); // 翻译失败返回空字符串
      }
    }
    return results;
  }

  /**
   * 生成缓存键
   * @param text 文本
   * @returns 缓存键
   */
  private generateCacheKey(text: string): string {
    // 简单哈希函数用于缓存
    return text.trim().toLowerCase();
  }

  /**
   * 获取API调用统计信息
   */
  getAPIStats() {
    return {
      callCount: this.apiCallCount,
      totalTime: this.apiTotalTime,
      averageTime: this.apiCallCount > 0 ? this.apiTotalTime / this.apiCallCount : 0,
      cacheSize: this.translationCache.size
    };
  }

  /**
   * 获取详细的失败统计信息
   */
  getFailureStats() {
    const totalFailures = this.failureStats.contentFilter + 
                         this.failureStats.timeout + 
                         this.failureStats.rateLimit + 
                         this.failureStats.networkError + 
                         this.failureStats.other;
    
    const successRate = this.failureStats.totalAttempts > 0 ? 
                       (this.failureStats.successfulProcessing / this.failureStats.totalAttempts * 100).toFixed(2) : 
                       '0.00';
    
    return {
      // 基础统计
      totalAttempts: this.failureStats.totalAttempts,
      successfulProcessing: this.failureStats.successfulProcessing,
      totalFailures: totalFailures,
      successRate: parseFloat(successRate),
      
      // 失败分类统计
      failures: {
        contentFilter: {
          count: this.failureStats.contentFilter,
          percentage: totalFailures > 0 ? (this.failureStats.contentFilter / totalFailures * 100).toFixed(2) : '0.00'
        },
        timeout: {
          count: this.failureStats.timeout,
          percentage: totalFailures > 0 ? (this.failureStats.timeout / totalFailures * 100).toFixed(2) : '0.00'
        },
        rateLimit: {
          count: this.failureStats.rateLimit,
          percentage: totalFailures > 0 ? (this.failureStats.rateLimit / totalFailures * 100).toFixed(2) : '0.00'
        },
        networkError: {
          count: this.failureStats.networkError,
          percentage: totalFailures > 0 ? (this.failureStats.networkError / totalFailures * 100).toFixed(2) : '0.00'
        },
        other: {
          count: this.failureStats.other,
          percentage: totalFailures > 0 ? (this.failureStats.other / totalFailures * 100).toFixed(2) : '0.00'
        }
      },
      
      // 性能指标
      consecutiveFailures: this.consecutiveFailures,
      
      // 建议
      recommendations: this.generateRecommendations()
    };
  }

  /**
   * 根据失败统计生成优化建议
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const stats = this.failureStats;
    const totalFailures = stats.contentFilter + stats.timeout + stats.rateLimit + stats.networkError + stats.other;
    
    if (totalFailures === 0) {
      recommendations.push('系统运行正常，无需特殊优化');
      return recommendations;
    }
    
    // 内容过滤问题
    if (stats.contentFilter > totalFailures * 0.3) {
      recommendations.push('内容过滤失败率较高，建议：');
      recommendations.push('  - 加强敏感内容预处理');
      recommendations.push('  - 检查新闻来源的内容质量');
      recommendations.push('  - 考虑调整内容处理策略');
    }
    
    // 超时问题
    if (stats.timeout > totalFailures * 0.2) {
      recommendations.push('超时问题频发，建议：');
      recommendations.push('  - 增加请求超时时间');
      recommendations.push('  - 减少批次大小');
      recommendations.push('  - 检查网络连接稳定性');
    }
    
    // API限制问题
    if (stats.rateLimit > totalFailures * 0.2) {
      recommendations.push('API限制频繁触发，建议：');
      recommendations.push('  - 减少并发请求数');
      recommendations.push('  - 增加请求间隔时间');
      recommendations.push('  - 考虑升级API套餐');
    }
    
    // 网络问题
    if (stats.networkError > totalFailures * 0.1) {
      recommendations.push('网络连接不稳定，建议：');
      recommendations.push('  - 检查网络环境');
      recommendations.push('  - 增加重试机制');
      recommendations.push('  - 考虑使用备用网络');
    }
    
    return recommendations;
  }

  /**
   * 重置失败统计
   */
  resetFailureStats() {
    this.failureStats = {
      contentFilter: 0,
      timeout: 0,
      rateLimit: 0,
      networkError: 0,
      other: 0,
      totalAttempts: 0,
      successfulProcessing: 0
    };
    this.consecutiveFailures = 0;
    this.logger.log('失败统计已重置');
  }

  /**
   * 清除翻译缓存
   */
  clearCache() {
    const cacheSize = this.translationCache.size;
    this.translationCache.clear();
    this.logger.log(`清除了${cacheSize}条翻译缓存`);
  }

  /**
   * 并发控制执行器
   * @param task 要执行的任务
   * @returns 任务结果
   */
  private async executeWithConcurrencyControl<T>(task: () => Promise<T>): Promise<T> {
    // 如果当前活跃请求数小于最大并发数，直接执行
    if (this.activeRequests < (this.translationConfig.maxConcurrentRequests || 3)) {
      this.activeRequests++;
      try {
        return await task();
      } finally {
        this.activeRequests--;
        // 处理队列中的下一个任务
        this.processNextQueuedTask();
      }
    }

    // 如果并发数已达到上限，将任务加入队列
    return new Promise<T>((resolve, reject) => {
      this.requestQueue.push(async () => {
        try {
          const result = await task();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      this.logger.debug(`当前并发请求数: ${this.activeRequests}, 队列中的请求数: ${this.requestQueue.length}`);
    });
  }

  /**
   * 处理队列中的下一个任务
   */
  private processNextQueuedTask() {
    if (this.requestQueue.length > 0 && this.activeRequests < (this.translationConfig.maxConcurrentRequests || 3)) {
      const nextTask = this.requestQueue.shift();
      if (nextTask) {
        this.activeRequests++;
        nextTask().finally(() => {
          this.activeRequests--;
          this.processNextQueuedTask();
        });
      }
    }
  }

  /**
   * 分段处理长文本
   * @param text 长文本
   * @returns 翻译后的文本
   */
  private async translateLongText(text: string): Promise<string> {
    // 将长文本分成段落
    const segments = this.splitTextIntoSegments(text);
    this.logger.debug(`长文本分段处理: 共${segments.length}段`);

    const translatedSegments: string[] = [];

    // 逐段翻译
    for (let i = 0; i < segments.length; i++) {
      const segment = segments[i];
      if (segment.trim()) {
        try {
          this.logger.debug(`翻译第${i+1}/${segments.length}段，长度: ${segment.length}`);
          // 传入 isSegment = true，表示这是分段调用，避免无限递归
          const translated = await this.translateText(segment, 0, true);
          translatedSegments.push(translated);

          // 添加短暂延迟，避免API过载
          if (i < segments.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 200));
          }
        } catch (error: any) {
          // 如果单段翻译失败，记录错误但继续处理其他段落
          this.logger.error(`段落翻译失败: ${error.message}`);
          translatedSegments.push(`[翻译失败: ${segment.substring(0, 30)}...]`);
        }
      } else {
        translatedSegments.push('');
      }
    }

    // 合并翻译结果
    return translatedSegments.join('\n\n');
  }

  /**
   * 将文本分成段落
   * @param text 要分段的文本
   * @returns 文本段落数组
   */
  private splitTextIntoSegments(text: string): string[] {
    const segments: string[] = [];
    let remainingText = text;

    while (remainingText.length > 0) {
      if (remainingText.length <= this.translationConfig.maxTextLength) {
        segments.push(remainingText);
        break;
      }

      // 尝试在段落分隔符处分割
      let splitIndex = -1;

      for (const separator of this.paragraphSeparators) {
        // 在最大长度范围内找到最后一个分隔符
        const lastSeparatorIndex = remainingText.lastIndexOf(separator, this.translationConfig.maxTextLength);

        if (lastSeparatorIndex > splitIndex) {
          splitIndex = lastSeparatorIndex + separator.length;
        }
      }

      // 如果没有找到合适的分隔符，则强制在最大长度处分割
      if (splitIndex <= 0) {
        splitIndex = this.translationConfig.maxTextLength;
      }

      segments.push(remainingText.substring(0, splitIndex));
      remainingText = remainingText.substring(splitIndex);
    }

    return segments;
  }

  /**
   * 预处理文本，移除可能触发内容过滤的内容
   * @param text 原始文本
   * @returns 处理后的文本
   */
  private removeAdvertisements(text: string): string {
    // 基于常见广告关键词和格式的简单启发式过滤
    const adPatterns = [
      /^\s*广告[:：]/i,
      /^\s*Sponsored[:：]?/i,
      /^\s*Advertisement[:：]?/i,
      /点击(这里|链接)查看全文/i,
      /如果您无法正常浏览此邮件/i,
      /免责声明[:：]/i,
      /本文(来源|来自)/i,
      /更多精彩内容请关注/i,
      /All rights reserved/i,
      /版权所有/i
    ];

    // 将文本按行切分，过滤广告行
    const cleanedLines = text.split(/\n+/).filter(line => {
      return !adPatterns.some(pattern => pattern.test(line.trim()));
    });

    // 过滤掉过短且疑似广告的最后一段
    if (cleanedLines.length > 0) {
      const lastLine = cleanedLines[cleanedLines.length - 1].trim();
      if (lastLine.length < 30 && /\b(http|www\.|\.com|\.cn)\b/i.test(lastLine)) {
        cleanedLines.pop();
      }
    }

    return cleanedLines.join('\n').trim();
  }

  // 修改 preprocessContent 调用广告去除
  private preprocessContent(text: string): string {
    let processed = this.removeAdvertisements(text);
    // 原有预处理逻辑
    processed = processed
      // 移除HTML标签
      .replace(/<[^>]+>/g, ' ')
      // 移除URL
      .replace(/https?:\/\/\S+/g, '[URL]')
      // 移除邮箱地址
      .replace(/\S+@\S+\.\S+/g, '[EMAIL]')
      // 移除多余空白
      .replace(/\s{2,}/g, ' ')
      .trim();

    return processed;
  }

  /**
   * 应用更严格的预处理，在基本预处理失败后使用
   * @param text 原始文本
   * @returns 处理后的文本
   */
  private applyStricterPreprocessing(text: string): string {
    // 先应用基本预处理
    let processedText = this.preprocessContent(text);

    // 更严格的处理
    processedText = processedText
      // 移除所有数字序列
      .replace(/(\d{4,})/g, '[NUMBER]')
      // 移除所有特殊字符
      .replace(/[^\w\s.,;:\-()\[\]{}"']/g, ' ')
      // 移除可能的敏感内容 - 更全面的敏感词汇
      .replace(/\b(missile|nuclear|classified|confidential|secret|intelligence|spy|surveillance|hack|breach|leak|sensitive|restricted|clearance|covert|operation|agent|agency|infiltrate|sabotage|conspiracy|whistleblower)\b/gi, '[SENSITIVE]')
      // 将连续的多个空格替换为单个空格
      .replace(/\s+/g, ' ')
      // 将文本分成短句子，每个句子不超过100个字符
      .split(/[.!?]\s+/)
      .map(sentence => sentence.trim().substring(0, 100))
      .join('. ')
      .trim();

    // 如果文本还是很长，截取前1000个字符
    if (processedText.length > 1000) {
      processedText = processedText.substring(0, 1000) + '...';
    }

    return processedText;
  }

  /**
   * 提取中文文本的主题词
   * @param title 文章标题
   * @param content 文章内容
   * @param retryCount 当前重试次数
   * @returns 主题词数组，逗号分隔的字符串
   */
  async extractThemes(title: string, content: string, retryCount = 0): Promise<string> {
    if ((!title || title.trim() === '') && (!content || content.trim() === '')) {
      return '';
    }

    // 构建用于提取主题的文本
    let textForThemeExtraction = '';
    if (title && title.trim() !== '') {
      textForThemeExtraction += `标题：${title}\n\n`;
    }
    if (content && content.trim() !== '') {
      // 如果内容太长，只取前2000个字符用于主题提取
      const maxContentLength = 2000;
      const truncatedContent = content.length > maxContentLength
        ? content.substring(0, maxContentLength) + '...'
        : content;
      textForThemeExtraction += `内容：${truncatedContent}`;
    }

    // 检查缓存
    const cacheKey = 'theme_' + this.generateCacheKey(textForThemeExtraction);
    if (this.translationCache.has(cacheKey)) {
      this.logger.debug(`使用缓存的主题提取结果: ${cacheKey.substring(0, 30)}...`);
      return this.translationCache.get(cacheKey) || '';
    }

    // 并发控制
    return this.executeWithConcurrencyControl(async () => {
      try {
        this.apiCallCount++;
        const startTime = Date.now();

        const completion = await this.openai.chat.completions.create({
          model: this.themeExtractionConfig.model,
          messages: [
            { role: 'system', content: this.themeExtractionConfig.systemPrompt },
            { role: 'user', content: textForThemeExtraction }
          ],
        });

        const themes = completion.choices[0]?.message?.content || '';
        const elapsed = Date.now() - startTime;
        this.apiTotalTime += elapsed;

        this.logger.debug(`主题提取API调用完成，耗时: ${elapsed}ms，总调用次数: ${this.apiCallCount}`);

        // 缓存提取结果
        this.translationCache.set(cacheKey, themes);

        // 重置连续失败计数
        this.consecutiveFailures = 0;

        return themes;
      } catch (error: any) {
        // 增加连续失败计数（仅用于日志记录）
        this.consecutiveFailures++;
        this.logger.warn(`主题提取服务连续失败${this.consecutiveFailures}次 - 模型: ${this.themeExtractionConfig.model}`);

        // 处理内容不适当错误
        if (error.message.includes('inappropriate content')) {
          if (retryCount < (this.themeExtractionConfig.maxRetries || 2)) {
            this.logger.warn(`检测到不适当内容，尝试进一步处理后重试，重试次数: ${retryCount + 1}`);
            // 更强力的内容处理
            const furtherProcessedTitle = this.applyStricterPreprocessing(title);
            const furtherProcessedContent = this.applyStricterPreprocessing(content);
            return this.extractThemes(furtherProcessedTitle, furtherProcessedContent, retryCount + 1);
          } else {
            // 超过最大重试次数，返回特殊标记以跳过该文档
            this.logger.error(`主题提取内容不适当错误超过最大重试次数(${this.themeExtractionConfig.maxRetries})，跳过该文档`);
            throw new Error(`SKIP_DOCUMENT:主题提取内容不适当错误超过最大重试次数`);
          }
        }

        // 处理超时错误
        if (error.message.includes('timed out')) {
          if (retryCount < (this.themeExtractionConfig.maxRetries || 2)) {
            // 指数退避重试
            const waitTime = Math.pow(2, retryCount) * (this.themeExtractionConfig.retryDelay || 1000);
            this.logger.warn(`主题提取请求超时，${waitTime}ms后重试，重试次数: ${retryCount + 1} - 模型: ${this.themeExtractionConfig.model}`);

            await new Promise(resolve => setTimeout(resolve, waitTime));
            return this.extractThemes(title, content, retryCount + 1);
          } else {
            // 超过最大重试次数，返回特殊标记以跳过该文档
            this.logger.error(`主题提取超时错误超过最大重试次数(${this.themeExtractionConfig.maxRetries})，跳过该文档`);
            throw new Error(`SKIP_DOCUMENT:主题提取超时错误超过最大重试次数`);
          }
        }

        this.logger.error(`主题提取失败 - 模型: ${this.themeExtractionConfig.model}: ${error.message}`, error.stack);
        throw new Error(`主题提取失败: ${error.message}`);
      }
    });
  }

  /**
   * 一次性完成翻译和主题提取（优化版本，减少API调用次数）
   * @param title 原始标题
   * @param summary 原始摘要
   * @param content 原始内容
   * @param retryCount 当前重试次数
   * @returns 包含翻译结果和主题词的对象
   */
  async translateAndExtractInOneCall(
    title: string, 
    summary: string, 
    content: string, 
    retryCount = 0
  ): Promise<{
    title_cn: string;
    summary_cn: string;
    content_cn: string;
    themes_cn: string;
  }> {
    if ((!title || title.trim() === '') && 
        (!summary || summary.trim() === '') && 
        (!content || content.trim() === '')) {
      return {
        title_cn: '',
        summary_cn: '',
        content_cn: '',
        themes_cn: ''
      };
    }

    // 构建输入文本
    let inputText = '';
    if (title && title.trim() !== '') {
      inputText += `标题：${title}\n\n`;
    }
    if (summary && summary.trim() !== '') {
      inputText += `摘要：${summary}\n\n`;
    }
    if (content && content.trim() !== '') {
      // 如果内容太长，截取前2000个字符
      const maxContentLength = 2000;
      const truncatedContent = content.length > maxContentLength
        ? content.substring(0, maxContentLength) + '...'
        : content;
      inputText += `内容：${truncatedContent}`;
    }

    // 应用敏感词预处理
    const processedInput = this.sanitizeSensitiveWords(inputText);

    // 检查缓存
    const cacheKey = 'combined_' + this.generateCacheKey(processedInput);
    if (this.translationCache.has(cacheKey)) {
      this.logger.debug(`命中组合翻译缓存: ${cacheKey.substring(0, 30)}...`);
      const cachedResult = this.translationCache.get(cacheKey);
      if (cachedResult) {
        try {
          return JSON.parse(cachedResult);
        } catch (parseError) {
          this.logger.warn(`缓存结果JSON解析失败，重新调用API`);
        }
      }
    }

    // 组合任务的系统提示词
    const combinedSystemPrompt = `你是一位专业的翻译和文本分析专家，特别擅长航空航天领域。请对以下内容执行三个任务：

1. 翻译：将标题、摘要、内容准确翻译为中文，保留数字和专有名词格式
2. 主题提取：提取不超过5个最重要的中文主题词，概括文章核心内容
3. 类型识别：请严格按照以下标准判断文章类型：
   - 科普类型：文章主要目的是教育和知识普及，重点解释科学原理、技术概念、工作机制等，具有明显的教学性质（如"什么是量子计算"、"火箭发动机工作原理解析"等）
   - 军事类型：文章涉及军事装备、军事行动、国防安全、武器系统等军事相关内容
   - 新闻事实：文章主要报道具体事件、发布消息、描述事实，即使涉及技术话题也主要是事实报道而非教学解释
   
   判断原则：只有当文章明确以教育为目的、主要在解释概念和原理时才标记为科普；单纯的技术新闻报道不应标记为科普。如果是科普类型在主题词中添加"科普"，如果是军事类型添加"军事"，如果都不是则不添加类型标签。

请严格按照以下JSON格式返回结果，不要有其他内容：
{"title_cn":"翻译后的标题","summary_cn":"翻译后的摘要","content_cn":"翻译后的内容","themes_cn":"主题词1,主题词2,主题词3"}`;

    // 并发控制
    return this.executeWithConcurrencyControl(async () => {
      try {
        this.apiCallCount++;
        const startTime = Date.now();

        // 预处理文本
        const finalProcessedText = this.preprocessContent(processedInput);

        const completion = await this.openai.chat.completions.create({
          model: this.translationConfig.model,
          messages: [
            { role: 'system', content: combinedSystemPrompt },
            { role: 'user', content: finalProcessedText }
          ],
          // 增加maxTokens以适应更长的输出
          max_tokens: Math.max(this.translationConfig.maxTokens || 4000, 6000),
          temperature: this.translationConfig.temperature,
        });

        const responseContent = completion.choices[0]?.message?.content || '';
        const elapsed = Date.now() - startTime;
        this.apiTotalTime += elapsed;

        this.logger.debug(`组合翻译API调用完成，耗时: ${elapsed}ms，总调用次数: ${this.apiCallCount}`);

        // 解析JSON响应
        let result;
        try {
          // 清理响应内容，移除可能的markdown代码块标记
          let cleanedResponse = responseContent.trim();
          
          // 移除markdown代码块标记
          cleanedResponse = cleanedResponse.replace(/^```json\s*/i, '').replace(/\s*```$/i, '');
          
          // 尝试提取JSON部分（可能包含其他文本）
          const jsonMatch = cleanedResponse.match(/\{[\s\S]*"title_cn"[\s\S]*\}/);
          const jsonStr = jsonMatch ? jsonMatch[0] : cleanedResponse;
          
          result = JSON.parse(jsonStr);
          
          // 验证必要字段
          if (!result.title_cn && !result.summary_cn && !result.content_cn) {
            throw new Error('响应中缺少翻译字段');
          }
          
          // 确保所有字段都存在
          result = {
            title_cn: result.title_cn || '',
            summary_cn: result.summary_cn || '',
            content_cn: result.content_cn || '',
            themes_cn: result.themes_cn || ''
          };
          
        } catch (parseError) {
          this.logger.error(`JSON解析失败，原始响应: ${responseContent.substring(0, 200)}...`);
          
          // 如果JSON解析失败，尝试从响应中提取内容
          const lines = responseContent.split('\n');
          result = {
            title_cn: this.extractFieldFromResponse(lines, 'title_cn') || title,
            summary_cn: this.extractFieldFromResponse(lines, 'summary_cn') || summary,
            content_cn: this.extractFieldFromResponse(lines, 'content_cn') || content,
            themes_cn: this.extractFieldFromResponse(lines, 'themes_cn') || ''
          };
          
          this.logger.warn(`使用备用解析方法，提取结果: ${JSON.stringify(result)}`);
        }

        // 缓存结果
        this.translationCache.set(cacheKey, JSON.stringify(result));

        // 重置连续失败计数
        this.consecutiveFailures = 0;
        this.failureStats.successfulProcessing++;

        return result;
      } catch (error: any) {
        // 增加连续失败计数
        this.consecutiveFailures++;
        
        this.logger.error(`组合翻译失败 - 模型: ${this.translationConfig.model}, 重试次数: ${retryCount}, 错误: ${error.message}`);

        // 处理内容不适当错误
        if (error.message.includes('inappropriate content') || 
            error.message.includes('content_filter') || 
            error.message.includes('content policy') ||
            error.message.includes('safety') ||
            error.message.includes('harmful')) {
          
          this.failureStats.contentFilter++;
          
          if (retryCount >= (this.translationConfig.maxRetries || 2)) {
            this.logger.error(`组合翻译内容过滤错误超过最大重试次数(${this.translationConfig.maxRetries})，跳过该文档`);
            throw new Error(`SKIP_DOCUMENT:组合翻译内容过滤错误超过最大重试次数 - ${error.message}`);
          }

          this.logger.warn(`使用智能预处理重试组合翻译，重试次数: ${retryCount + 1}`);
          const preprocessingResult = this.intelligentPreprocessing(inputText);
          
          // 重新构建输入
          const retryTitle = this.extractOriginalField(preprocessingResult.processedText, '标题');
          const retrySummary = this.extractOriginalField(preprocessingResult.processedText, '摘要');
          const retryContent = this.extractOriginalField(preprocessingResult.processedText, '内容');
          
          return this.translateAndExtractInOneCall(retryTitle, retrySummary, retryContent, retryCount + 1);
        }

        // 处理超时错误
        if (error.message.includes('timed out') || error.message.includes('timeout')) {
          this.failureStats.timeout++;
          
          if (retryCount < (this.translationConfig.maxRetries || 2)) {
            const waitTime = Math.pow(2, retryCount) * (this.translationConfig.retryDelay || 1000);
            this.logger.warn(`组合翻译请求超时，${waitTime}ms后重试，重试次数: ${retryCount + 1}`);

            await new Promise(resolve => setTimeout(resolve, waitTime));
            return this.translateAndExtractInOneCall(title, summary, content, retryCount + 1);
          } else {
            this.logger.error(`组合翻译超时错误超过最大重试次数，跳过该文档`);
            throw new Error(`SKIP_DOCUMENT:组合翻译超时错误超过最大重试次数`);
          }
        }

        // 处理API限制错误
        if (error.message.includes('rate limit') || error.message.includes('429')) {
          this.failureStats.rateLimit++;
          
          if (retryCount < (this.translationConfig.maxRetries || 2)) {
            const waitTime = (retryCount + 1) * 5000;
            this.logger.warn(`组合翻译API限制错误，${waitTime}ms后重试，重试次数: ${retryCount + 1}`);

            await new Promise(resolve => setTimeout(resolve, waitTime));
            return this.translateAndExtractInOneCall(title, summary, content, retryCount + 1);
          } else {
            this.logger.error(`组合翻译API限制错误超过最大重试次数，跳过该文档`);
            throw new Error(`SKIP_DOCUMENT:组合翻译API限制错误超过最大重试次数`);
          }
        }

        // 处理网络错误
        if (error.message.includes('network') || 
            error.message.includes('connection') || 
            error.message.includes('ENOTFOUND') ||
            error.message.includes('ECONNRESET') ||
            error.message.includes('socket')) {
          this.failureStats.networkError++;
        } else {
          this.failureStats.other++;
        }

        this.logger.error(`组合翻译失败: ${error.message}`, error.stack);
        throw new Error(`组合翻译失败: ${error.message}`);
      }
    });
  }

  /**
   * 从响应文本中提取指定字段的值
   * @param lines 响应文本行数组
   * @param fieldName 字段名
   * @returns 提取的值
   */
  private extractFieldFromResponse(lines: string[], fieldName: string): string {
    for (const line of lines) {
      if (line.includes(`"${fieldName}"`)) {
        const match = line.match(new RegExp(`"${fieldName}"\\s*:\\s*"([^"]*)"`, 'i'));
        if (match) {
          return match[1];
        }
      }
    }
    return '';
  }

  /**
   * 从预处理后的文本中提取原始字段
   * @param processedText 预处理后的文本
   * @param fieldLabel 字段标签（如"标题"、"摘要"、"内容"）
   * @returns 提取的原始字段值
   */
  private extractOriginalField(processedText: string, fieldLabel: string): string {
    const regex = new RegExp(`${fieldLabel}：([\\s\\S]*?)(?=\\n\\n|$)`, 'i');
    const match = processedText.match(regex);
    return match ? match[1].trim() : '';
  }

  /**
   * 检测文本中的潜在敏感内容 - 增强版
   * @param text 要检测的文本
   * @returns 检测结果
   */
  private analyzeSensitiveContent(text: string): {
    hasSensitiveContent: boolean;
    sensitiveWords: string[];
    categories: string[];
    riskLevel: 'low' | 'medium' | 'high';
  } {
    const sensitivePatterns = {
      // 安全相关 - 扩展词库
      security: /\b(terrorism|terrorist|bomb|explosive|attack|kill|weapon|suicide|violent|war|conflict|military|missile|nuclear|assassination|threat|hostage|extremist|radical|jihad|massacre|combat|battlefield|warfare|fighter|bomber|destroyer|battleship|tank|artillery|grenade|rifle|pistol|ammunition|soldier|troops|combat|invasion|occupation|siege|raid|assault|warfare|defense|offensive|strike|launch|target|enemy|hostile|aggression|counterattack)\b/gi,
      
      // 毒品相关
      drugs: /\b(drug|cocaine|heroin|marijuana|cannabis|meth|amphetamine|opium|narcotic|substance abuse|overdose|smuggling|trafficking|dealer|addict|rehabilitation|withdrawal)\b/gi,
      
      // 成人内容
      adult: /\b(sex|porn|nude|naked|adult|explicit|xxx|erotic|obscene|prostitute|brothel|strip|sexual|intimacy|seduction)\b/gi,
      
      // 政治敏感 - 扩展
      political: /\b(genocide|torture|dictatorship|oppression|censorship|propaganda|corruption|riot|protest|revolution|rebellion|uprising|coup|regime|authoritarian|totalitarian|persecution|discrimination|violation|human rights|freedom|democracy|election fraud|suppression)\b/gi,
      
      // 情报和机密 - 扩展
      intelligence: /\b(missile|nuclear|classified|confidential|secret|intelligence|spy|surveillance|hack|breach|leak|sensitive|restricted|clearance|covert|operation|agent|agency|infiltrate|sabotage|conspiracy|whistleblower|espionage|reconnaissance|undercover|mole|double agent|black ops|cyber attack|data breach|national security|top secret|state secrets)\b/gi,
      
      // 暴力相关 - 扩展
      violence: /\b(death|murder|torture|abuse|violence|blood|injury|harm|damage|destroy|destruction|killing|slaughter|execution|brutality|cruelty|savage|vicious|ruthless|merciless)\b/gi,
      
      // 新增：网络安全和黑客
      cybersecurity: /\b(hacker|hacking|malware|virus|trojan|phishing|ransomware|ddos|cyber|breach|vulnerability|exploit|penetration|backdoor|rootkit|keylogger|botnet|dark web|anonymous|zero day)\b/gi,
      
      // 新增：恐怖主义相关术语
      terrorism_extended: /\b(isis|al.?qaeda|taliban|suicide bomber|car bomb|ied|improvised explosive|terror attack|mass shooting|school shooting|public attack|lone wolf|radicalization|indoctrination)\b/gi,
      
      // 新增：军事技术
      military_tech: /\b(drone|uav|guided missile|ballistic|intercontinental|submarine|aircraft carrier|stealth|radar|sonar|satellite surveillance|gps jamming|electronic warfare|signal intelligence)\b/gi
    };

    const sensitiveWords: string[] = [];
    const categories: string[] = [];
    let totalMatches = 0;

    Object.entries(sensitivePatterns).forEach(([category, pattern]) => {
      const matches = text.match(pattern);
      if (matches) {
        sensitiveWords.push(...matches);
        categories.push(category);
        totalMatches += matches.length;
      }
    });

    // 增强的风险等级计算
    let riskLevel: 'low' | 'medium' | 'high' = 'low';
    
    // 高风险条件更严格
    if (totalMatches > 15 || 
        categories.includes('terrorism_extended') || 
        categories.includes('cybersecurity') || 
        (categories.includes('security') && categories.includes('intelligence')) ||
        (categories.includes('violence') && totalMatches > 8)) {
      riskLevel = 'high';
    } else if (totalMatches > 8 || 
               categories.length > 3 || 
               categories.includes('security') || 
               categories.includes('intelligence') ||
               categories.includes('military_tech')) {
      riskLevel = 'medium';
    }

    return {
      hasSensitiveContent: sensitiveWords.length > 0,
      sensitiveWords: [...new Set(sensitiveWords)], // 去重
      categories: [...new Set(categories)], // 去重
      riskLevel
    };
  }

  /**
   * 智能预处理文本，根据内容分析选择合适的处理策略
   * @param text 原始文本
   * @param title 文章标题（可选，用于上下文判断）
   * @returns 处理后的文本和处理信息
   */
  private intelligentPreprocessing(text: string, title?: string): {
    processedText: string;
    strategy: string;
    analysis: any;
    needsManualReview: boolean;
  } {
    // 分析敏感内容
    const analysis = this.analyzeSensitiveContent(text);
    const titleAnalysis = title ? this.analyzeSensitiveContent(title) : null;

    let processedText = text;
    let strategy = 'none';
    let needsManualReview = false;

    // 判断是否是新闻类文本
    const isNewsContent = /\b(报道|新闻|发布|宣布|消息|据.*报告|according to|reported|announced|news|press|media)\b/i.test(text + (title || ''));
    
    // 判断是否是技术或科学文本
    const isTechnicalContent = /\b(技术|科学|研究|实验|数据|algorithm|technology|science|research|satellite|space|orbit)\b/i.test(text + (title || ''));

    if (analysis.riskLevel === 'high') {
      // 高风险内容需要更激进的处理
      if (isNewsContent || isTechnicalContent) {
        // 对于新闻或技术内容，使用温和的替换策略
        processedText = this.applyContextualPreprocessing(text, 'news_technical');
        strategy = 'contextual_news_technical';
      } else {
        // 对于其他高风险内容，使用严格处理
        processedText = this.applyStricterPreprocessing(text);
        strategy = 'strict';
        needsManualReview = true;
      }
    } else if (analysis.riskLevel === 'medium') {
      // 中等风险使用基础预处理
      processedText = this.preprocessContent(text);
      strategy = 'basic';
    } else if (analysis.hasSensitiveContent) {
      // 低风险但有敏感词，使用轻度处理
      processedText = this.applyLightPreprocessing(text);
      strategy = 'light';
    }

    return {
      processedText,
      strategy,
      analysis: {
        original: analysis,
        title: titleAnalysis,
        isNewsContent,
        isTechnicalContent
      },
      needsManualReview
    };
  }

  /**
   * 针对新闻和技术内容的上下文预处理
   * @param text 原始文本
   * @param contentType 内容类型
   * @returns 处理后的文本
   */
  private applyContextualPreprocessing(text: string, contentType: 'news_technical' | 'general'): string {
    let processedText = text;

    if (contentType === 'news_technical') {
      // 对于新闻和技术内容，保留更多信息，只替换明显敏感的词汇
      processedText = processedText
        // 保留军事技术术语，但替换暴力词汇
        .replace(/\b(kill|murder|destroy|attack|violence)\b/gi, '[ACTION]')
        // 保留核技术等术语，但替换威胁性描述
        .replace(/\b(threat|danger|risk|hazard)\b/gi, '[CONCERN]')
        // 保留情报相关术语在技术上下文中
        .replace(/\b(classified|secret|confidential)(?!\s+(information|data|technology))/gi, '[RESTRICTED]')
        // 移除明显的敏感政治内容
        .replace(/\b(terrorism|terrorist|extremist|radical)\b/gi, '[GROUP]');
    } else {
      // 对于一般内容使用标准预处理
      processedText = this.preprocessContent(text);
    }

    return processedText.trim();
  }

  /**
   * 轻度预处理，只处理最明显的敏感内容
   * @param text 原始文本
   * @returns 处理后的文本
   */
  private applyLightPreprocessing(text: string): string {
    return text
      // 只替换最明显的暴力词汇
      .replace(/\b(terrorism|terrorist|bomb|explosive|kill|murder|suicide)\b/gi, '[REDACTED]')
      // 替换明显的成人内容
      .replace(/\b(porn|nude|explicit|xxx|obscene)\b/gi, '[FILTERED]')
      // 移除URL和邮箱
      .replace(/https?:\/\/\S+/g, '[URL]')
      .replace(/\S+@\S+\.\S+/g, '[EMAIL]')
      .trim();
  }
}