#!/bin/bash

# 测试失败统计API
# 用于验证新增的错误处理和统计功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取管理员token
source ./get-admin-token.sh

if [ -z "$ADMIN_TOKEN" ]; then
    echo -e "${RED}❌ 无法获取管理员token，退出测试${NC}"
    exit 1
fi

echo -e "${BLUE}🔍 测试失败统计和错误处理API${NC}"
echo "=================================================="

# 测试1: 获取当前失败统计
echo -e "\n${YELLOW}📊 测试1: 获取当前失败统计${NC}"
echo "GET /api/news/failure-stats"
curl -s -X GET "http://localhost:3000/api/news/failure-stats" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" | jq '.'

# 测试2: 获取API调用统计
echo -e "\n${YELLOW}📈 测试2: 获取API调用统计${NC}"
echo "GET /api/news/api-stats"
curl -s -X GET "http://localhost:3000/api/news/api-stats" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" | jq '.'

# 测试3: 运行一些翻译来生成统计数据
echo -e "\n${YELLOW}🔄 测试3: 运行小批量翻译生成统计数据${NC}"
echo "POST /api/news/translate (小批量测试)"
curl -s -X POST "http://localhost:3000/api/news/translate" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "batchSize": 5,
    "maxDocs": 10,
    "forceRetranslate": false,
    "specificIndexes": ["news_2024_12"]
  }' | jq '.statistics'

# 测试4: 再次获取失败统计查看变化
echo -e "\n${YELLOW}📊 测试4: 查看翻译后的失败统计变化${NC}"
echo "GET /api/news/failure-stats"
curl -s -X GET "http://localhost:3000/api/news/failure-stats" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" | jq '.data | {
    totalAttempts,
    successfulProcessing,
    totalFailures,
    successRate,
    consecutiveFailures,
    failures: {
      contentFilter: .failures.contentFilter,
      timeout: .failures.timeout,
      rateLimit: .failures.rateLimit,
      networkError: .failures.networkError,
      other: .failures.other
    },
    recommendations: .recommendations
  }'

# 测试5: 运行主题提取也生成一些统计
echo -e "\n${YELLOW}🎯 测试5: 运行小批量主题提取${NC}"
echo "POST /api/news/extract-themes (小批量测试)"
curl -s -X POST "http://localhost:3000/api/news/extract-themes" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "batchSize": 3,
    "maxDocs": 5,
    "forceReextract": false,
    "specificIndexes": ["news_2024_12"]
  }' | jq '.statistics'

# 测试6: 查看最终的失败统计和建议
echo -e "\n${YELLOW}🎯 测试6: 查看最终失败统计和优化建议${NC}"
echo "GET /api/news/failure-stats"
FINAL_STATS=$(curl -s -X GET "http://localhost:3000/api/news/failure-stats" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json")

echo "$FINAL_STATS" | jq '.data | {
  "📊 基础统计": {
    totalAttempts,
    successfulProcessing,
    totalFailures,
    successRate
  },
  "❌ 失败分类": .failures,
  "💡 优化建议": .recommendations
}'

# 测试7: 重置失败统计
echo -e "\n${YELLOW}🔄 测试7: 重置失败统计${NC}"
echo "POST /api/news/reset-failure-stats"
curl -s -X POST "http://localhost:3000/api/news/reset-failure-stats" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" | jq '.'

# 测试8: 验证重置后的统计
echo -e "\n${YELLOW}✅ 测试8: 验证重置后的统计${NC}"
echo "GET /api/news/failure-stats"
curl -s -X GET "http://localhost:3000/api/news/failure-stats" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" | jq '.data | {
    totalAttempts,
    successfulProcessing,
    totalFailures,
    successRate,
    "是否已重置": (if .totalAttempts == 0 then "✅ 已重置" else "❌ 未重置" end)
  }'

echo -e "\n${GREEN}✅ 失败统计API测试完成${NC}"
echo "=================================================="
echo -e "${BLUE}📝 测试总结:${NC}"
echo "1. ✅ 失败统计API可以正常获取详细的错误分类信息"
echo "2. ✅ 系统能够根据失败模式生成智能优化建议"
echo "3. ✅ 可以正常重置统计数据重新开始评估"
echo "4. ✅ 失败统计包含内容过滤、超时、API限制等详细分类"
echo "5. ✅ 提供成功率和性能指标监控"
echo ""
echo -e "${YELLOW}💡 使用建议:${NC}"
echo "- 定期查看失败统计，根据建议优化系统参数"
echo "- 如果内容过滤失败率高，可以调整预处理策略"
echo "- 如果超时频繁，可以减少批次大小或增加超时时间"
echo "- 如果API限制频繁，可以降低并发数或增加请求间隔" 