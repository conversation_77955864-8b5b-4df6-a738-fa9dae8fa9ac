#!/bin/bash

echo "=== 测试失败文档ID记录功能 ==="

# 服务器地址
BASE_URL="http://localhost:3000"

echo "1. 重置失败统计..."
curl -X POST "$BASE_URL/api/news/reset-failure-stats" \
  -H "Content-Type: application/json" | jq '.'

echo -e "\n2. 获取当前失败统计（应该为空）..."
curl -X GET "$BASE_URL/api/news/failure-stats" \
  -H "Content-Type: application/json" | jq '.'

echo -e "\n3. 启动翻译任务（模拟一些失败）..."
curl -X POST "$BASE_URL/api/news/translate" \
  -H "Content-Type: application/json" \
  -d '{
    "batchSize": 5,
    "maxDocs": 10,
    "specificIndexes": ["news-2024-12"]
  }' | jq '.'

echo -e "\n4. 等待翻译完成..."
sleep 10

echo -e "\n5. 获取失败统计（查看是否记录了失败文档ID）..."
curl -X GET "$BASE_URL/api/news/failure-stats" \
  -H "Content-Type: application/json" | jq '.'

echo -e "\n6. 获取失败文档列表..."
curl -X GET "$BASE_URL/api/news/failed-translations" \
  -H "Content-Type: application/json" | jq '.'

echo -e "\n=== 测试完成 ===" 